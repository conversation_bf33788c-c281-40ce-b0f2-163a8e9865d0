package com.ecommerce.web.buyer.controller;

import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.exception.BusinessOperationException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.member.api.dto.account.AccountDTO;
import com.ecommerce.member.api.dto.base.PageQuery;
import com.ecommerce.member.api.dto.exception.MemberCode;
import com.ecommerce.member.api.dto.member.CancelRequestDTO;
import com.ecommerce.member.api.dto.member.MemberApprovalRequestDTO;
import com.ecommerce.member.api.dto.member.MemberApprovalRequestQueryDTO;
import com.ecommerce.member.api.dto.member.MemberBaseInfoDTO;
import com.ecommerce.member.api.dto.member.MemberBusinessInfoDTO;
import com.ecommerce.member.api.dto.member.MemberCarrierDTO;
import com.ecommerce.member.api.dto.member.MemberCertDTO;
import com.ecommerce.member.api.dto.member.MemberCertQueryDTO;
import com.ecommerce.member.api.dto.member.MemberCertViewDTO;
import com.ecommerce.member.api.dto.member.MemberDTO;
import com.ecommerce.member.api.dto.member.MemberDetailDTO;
import com.ecommerce.member.api.dto.member.MemberListViewDTO;
import com.ecommerce.member.api.dto.member.MemberQueryDTO;
import com.ecommerce.member.api.dto.member.MemberRegisterByAppDTO;
import com.ecommerce.member.api.dto.member.MemberRequestDTO;
import com.ecommerce.member.api.dto.member.MemberSearchDTO;
import com.ecommerce.member.api.dto.member.MemberSearchQueryDTO;
import com.ecommerce.member.api.dto.member.MemberSimpleDTO;
import com.ecommerce.member.api.dto.member.MemberStatusDTO;
import com.ecommerce.member.api.dto.member.MemberSuperSimpleDTO;
import com.ecommerce.member.api.dto.member.MemberTypeDTO;
import com.ecommerce.member.api.dto.member.MemberUpdateTypeDTO;
import com.ecommerce.member.api.dto.member.SearchCarrierDTO;
import com.ecommerce.member.api.dto.member.SubmitCertDTO;
import com.ecommerce.member.api.dto.member.UpdateCertDTO;
import com.ecommerce.member.api.service.IMemberService;
import com.ecommerce.member.api.session.LoginInfo;
import com.ecommerce.web.buyer.dto.member.MemberApprovalRequestPageDTO;
import com.ecommerce.web.buyer.dto.member.MemberBaseInfoQueryDTO;
import com.ecommerce.web.buyer.dto.member.MemberBusinessInfoQueryDTO;
import com.ecommerce.web.buyer.dto.member.MemberQueryPageDTO;
import com.ecommerce.web.buyer.dto.member.MemberRegisterDTO;
import com.ecommerce.web.buyer.dto.member.MemberUpdateTypeQueryDTO;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import com.ecommerce.common.utils.CsStringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;


/**
 * TODO
 *
 * <AUTHOR>
 */
@RestController
@Tag(name = "MemberController", description = "会员")
@RequestMapping("/member")
public class MemberController {

    private final static Logger logger = LoggerFactory.getLogger(MemberController.class);

    @Autowired
    private IMemberService iMemberService;

    @Operation(summary = "根据ID查看用户简要信息")
    @PostMapping(value = "/findMemberSimpleById")
    public ItemResult<MemberSimpleDTO> findMemberSimpleById(@Parameter(hidden = true)LoginInfo loginInfo) {
        checkLogin(loginInfo);
        MemberSimpleDTO simple = iMemberService.findMemberSimpleById(loginInfo.getMemberId());
        return new ItemResult<>(simple);
    }

    @Operation(summary = "获取会员类型")
    @GetMapping(value = "/findMemberTypeByMemberId")
    public ItemResult<MemberTypeDTO> findMemberTypeByMemberId(@Parameter(hidden = true)LoginInfo loginInfo) {
        checkLogin(loginInfo);
        MemberTypeDTO memberTypeDTO = iMemberService.findMemberTypeByMemberId(loginInfo.getMemberId());
        return new ItemResult<>(memberTypeDTO);
    }

    @Operation(summary = "根据电话查找用户信息")
    @PostMapping(value = "/findMemberIdByContactPhone")
    public ItemResult<List<MemberSuperSimpleDTO>> findMemberIdByContactPhone(@Parameter(name = "contactPhone", description = "联系人电话") @RequestParam String contactPhone) {
        List<MemberSuperSimpleDTO> phone = iMemberService.findMemberIdByContactPhone(contactPhone);
        return new ItemResult<>(phone);
    }

    @Operation(summary = "分页查询用户")
    @PostMapping(value = "/pageMemberListView")
    public ItemResult<PageInfo<MemberListViewDTO>> pageMemberListView(@Parameter(name = "query", description = "会员查询分页DTO") @RequestBody MemberQueryPageDTO query) {
        if (query.getPageNum() == null) {
            query.setPageNum(1);
        }
        if (query.getPageSize() == null) {
            query.setPageSize(10);
        }
        PageInfo<MemberListViewDTO> pageInfo = iMemberService.pageMemberListView(query.getMemberQueryDTO(), query.getPageNum(), query.getPageSize());
        return new ItemResult<>(pageInfo);
    }

    @Operation(summary = "分页查询企业承运商")
    @PostMapping(value = "/pageEnterpriseCarrier")
    public ItemResult<PageInfo<MemberSimpleDTO>> pageEnterpriseCarrier(@Parameter(name = "query", description = "会员查询分页DTO") @RequestBody MemberQueryPageDTO query) {
        if (query.getPageNum() == null) {
            query.setPageNum(1);
        }
        if (query.getPageSize() == null) {
            query.setPageSize(10);
        }
        PageInfo<MemberSimpleDTO> pageInfo = iMemberService.pageEnterpriseCarrier(query.getMemberQueryDTO(), query.getPageNum(), query.getPageSize());
        return new ItemResult<>(pageInfo);
    }

    @Operation(summary = "分页查询企业卖家")
    @PostMapping(value = "/pageEnterpriseSeller")
    public ItemResult<PageInfo<MemberSimpleDTO>> pageEnterpriseSeller(@Parameter(name = "query", description = "会员查询分页DTO") @RequestBody MemberQueryPageDTO query) {
        if (query.getPageNum() == null) {
            query.setPageNum(1);
        }
        if (query.getPageSize() == null) {
            query.setPageSize(10);
        }
        PageInfo<MemberSimpleDTO> pageInfo = iMemberService.pageEnterpriseSeller(query.getMemberQueryDTO(), query.getPageNum(), query.getPageSize());
        return new ItemResult<>(pageInfo);
    }

    @Operation(summary = "根据名字查找企业承运商")
    @PostMapping(value = "/findEnterpriseCarrierByName")
    public ItemResult<List<MemberSuperSimpleDTO>> findEnterpriseCarrierByName(@Parameter(name = "carrierName", description = "承运商名称") @RequestParam String carrierName) {
        List<MemberSuperSimpleDTO> name = iMemberService.findEnterpriseCarrierByName(carrierName);
        return new ItemResult<>(name);
    }

    @Operation(summary = "根据请求ID查询资质")
    @PostMapping(value = "/findMemberCertByRequestId")
    public ItemResult<MemberCertViewDTO> findMemberCertByRequestId(@Parameter(name = "requestId", description = "请求id") @RequestParam String requestId) {
        MemberCertViewDTO name = iMemberService.findMemberCertByRequestId(requestId);
        return new ItemResult<>(name);
    }

    @Operation(summary = "根据名字查找企业卖家")
    @PostMapping(value = "/findEnterpriseSellerByName")
    public ItemResult<List<MemberSuperSimpleDTO>> findEnterpriseSellerByName(@Parameter(name = "sellerName", description = "卖家名称") @RequestParam String sellerName) {
        List<MemberSuperSimpleDTO> sellerByName = iMemberService.findEnterpriseSellerByName(sellerName);
        return new ItemResult<>(sellerByName);
    }

    @Operation(summary = "审批司机认证")
    @PostMapping(value = "/driverCertApproval")
    public ItemResult<Object> driverCertApproval(@Parameter(hidden = true) LoginInfo loginInfo) {
        checkLogin(loginInfo);
        iMemberService.driverCertApproval(loginInfo.getMemberId());
        return new ItemResult<>(new Object());
    }

    @Operation(summary = "找到最近的变更记录")
    @PostMapping(value = "/findRecentMemberApproveRequest")
    public ItemResult<MemberApprovalRequestDTO> findRecentMemberApproveRequest(@Parameter(hidden = true) LoginInfo loginInfo,
                                                     @Parameter(name = "queryDTO", description = "会员审批请求查询DTO") @RequestBody MemberApprovalRequestQueryDTO queryDTO) {
        checkLogin(loginInfo);
        queryDTO.setMemberId(loginInfo.getMemberId());
        MemberApprovalRequestDTO request = iMemberService.findRecentMemberApproveRequest(queryDTO);
        return new ItemResult<>(request);
    }

    @Operation(summary = "变更企业经营信息")
    @PostMapping(value = "/updateBusinessInfo")
    public ItemResult<String> updateBusinessInfo(@Parameter(hidden = true) LoginInfo loginInfo,
                                         @Parameter(name = "dto", description = "会员企业经营信息查询DTO") @Valid @RequestBody MemberBusinessInfoQueryDTO dto) {
        MemberBusinessInfoDTO infoDTO = businessQueryInfo2BusinessInfo(dto);
        checkLogin(loginInfo);
        infoDTO.setMemberId(loginInfo.getMemberId());
        infoDTO.setOperatorId(loginInfo.getAccountId());
        infoDTO.setOperator(loginInfo.getOperator());
        String requestId = iMemberService.updateBusinessInfo(infoDTO);
        return new ItemResult<>(requestId);
    }

    @Operation(summary = "个人升级企业卖家")
    @PostMapping(value = "/updateMemberToEnterpriseSeller")
    public ItemResult<String> updateMemberToEnterpriseSeller(@Parameter(hidden = true)LoginInfo loginInfo,
                                                             @Parameter(name = "dto", description = "会员更新类型查询DTO") @Valid @RequestBody MemberUpdateTypeQueryDTO dto) {
        MemberUpdateTypeDTO typeDTO = updateTypeQuery2UpdateTypeDTO(dto);
        checkLogin(loginInfo);
        typeDTO.setMemberId(loginInfo.getMemberId());
        String requestId = iMemberService.updateMemberToEnterpriseSeller(typeDTO);
        return new ItemResult<>(requestId);
    }

    @Operation(summary = "保存实名认证资料")
    @PostMapping(value = "/createRealNameCert")
    public ItemResult<String> createRealNameCert(@Parameter(hidden = true) LoginInfo loginInfo,
                                         @Parameter(name = "dto", description = "会员资质查询DTO") @Valid @RequestBody MemberCertQueryDTO dto) {
        MemberCertDTO certDTO = memberCertQuery2MemberCert(dto);
        checkLogin(loginInfo);
        certDTO.setMemberId(loginInfo.getMemberId());
        certDTO.setAccountId(loginInfo.getAccountId());
        certDTO.setOperatorId(loginInfo.getAccountId());
        certDTO.setOperator(loginInfo.getOperator());
        String requestId = iMemberService.createRealNameCert(certDTO);
        return new ItemResult<>(requestId);
    }

    @Operation(summary = "个人升级承运商")
    @PostMapping(value = "/updateMemberToEnterpriseCarrier")
    public ItemResult<String> updateMemberToEnterpriseCarrier(@Parameter(hidden = true)LoginInfo loginInfo,
                                                              @Parameter(name = "dto", description = "会员更新类型查询DTO") @Valid @RequestBody MemberUpdateTypeQueryDTO dto) {
        MemberUpdateTypeDTO typeDTO = updateTypeQuery2UpdateTypeDTO(dto);
        checkLogin(loginInfo);
        typeDTO.setMemberId(loginInfo.getMemberId());
        typeDTO.setOperatorId(loginInfo.getAccountId());
        typeDTO.setOperator(loginInfo.getOperator());
        String requestId = iMemberService.updateMemberToEnterpriseCarrier(typeDTO);
        return new ItemResult<>(requestId);
    }

    @Operation(summary = "保存实名认证资料")
    @PostMapping(value = "/saveRealNameCert")
    public ItemResult<Object> saveRealNameCert(@Parameter(hidden = true) LoginInfo loginInfo,
                                       @Parameter(name = "dto", description = "会员资质查询DTO") @Valid @RequestBody MemberCertQueryDTO dto) {
        MemberCertDTO certDTO = memberCertQuery2MemberCert(dto);
        checkLogin(loginInfo);
        certDTO.setMemberId(loginInfo.getMemberId());
        certDTO.setAccountId(loginInfo.getAccountId());
        certDTO.setOperatorId(loginInfo.getAccountId());
        certDTO.setOperator(loginInfo.getOperator());
        iMemberService.saveRealNameCert(certDTO);
        return new ItemResult<>(new Object());
    }

    @Operation(summary = "提交实名认证")
    @PostMapping(value = "/submitRealNameCert")
    public ItemResult<String> submitRealNameCert(@Parameter(hidden = true) LoginInfo loginInfo) {
        checkLogin(loginInfo);
        String requestId = iMemberService.submitRealNameCert(loginInfo.getAccountId(), null);
        return new ItemResult<>(requestId);
    }


    @Operation(summary = "根据ID查找用户详情")
    @PostMapping(value = "/findMemberDetailById")
    public ItemResult<MemberDTO> findMemberDetailById(@Parameter(hidden = true)LoginInfo loginInfo,
                                                      @Parameter(name = "memberId", description = "会员id") String memberId) {
        checkLogin(loginInfo);
        if (CsStringUtils.isEmpty(memberId) || "null".equals(memberId)) {
            memberId = loginInfo.getMemberId();
        }
        if ((loginInfo.getAccountType() == null) || (loginInfo.getAccountType().intValue() != AccountDTO.ACCOUNT_TYPE_MEMBER_MASTER)) {
            return new ItemResult<>(iMemberService.findRealMemberById(memberId));
        }
        return new ItemResult<>(iMemberService.findMemberDetailById(memberId));
    }

    @Operation(summary = "个人升级供应商")
    @PostMapping(value = "/updateMemberToEnterpriseSupplier")
    public ItemResult<String> updateMemberToEnterpriseSupplier(@Parameter(hidden = true)LoginInfo loginInfo,
                                                               @Parameter(name = "dto", description = "会员更新类型查询DTO") @Valid @RequestBody MemberUpdateTypeQueryDTO dto) {
        MemberUpdateTypeDTO typeDTO = updateTypeQuery2UpdateTypeDTO(dto);
        checkLogin(loginInfo);
        typeDTO.setMemberId(loginInfo.getMemberId());
        typeDTO.setOperatorId(loginInfo.getAccountId());
        typeDTO.setOperator(loginInfo.getOperator());
        String requestId = iMemberService.updateMemberToEnterpriseSupplier(typeDTO);
        return new ItemResult<>(requestId);
    }

    @Operation(summary = "个人会员升级到买家企业会员")
    @PostMapping(value = "/updateMemberToEnterpriseBuyer")
    public ItemResult<String> updateMemberToEnterpriseBuyer(@Parameter(hidden = true)LoginInfo loginInfo,
                                                            @Parameter(name = "dto", description = "会员更新类型查询DTO") @Valid @RequestBody MemberUpdateTypeQueryDTO dto) {
        MemberUpdateTypeDTO typeDTO = updateTypeQuery2UpdateTypeDTO(dto);
        checkLogin(loginInfo);
        typeDTO.setMemberId(loginInfo.getMemberId());
        String requestId = iMemberService.updateMemberToEnterpriseBuyer(typeDTO);
        return new ItemResult<>(requestId);
    }

    @Operation(summary = "根据ID查找用户名")
    @PostMapping(value = "/findMemberNameByMemberId")
    public ItemResult<String> findMemberNameByMemberId(@Parameter(hidden = true)LoginInfo loginInfo) {
        checkLogin(loginInfo);
        String name = iMemberService.findMemberNameByMemberId(loginInfo.getMemberId());
        return new ItemResult<>(name);
    }

    @Operation(summary = "根据ids查找用户简单信息")
    @PostMapping(value = "/findMemberSimpleByIds")
    public ItemResult<List<MemberSimpleDTO>> findMemberSimpleByIds(@Parameter(name = "ids", description = "会员id列表") @RequestParam List<String> ids) {
        List<MemberSimpleDTO> simple = iMemberService.findMemberSimpleByIds(ids);
        return new ItemResult<>(simple);
    }

    @Operation(summary = "根据用户ID查找认证详情")
    @PostMapping(value = "/findMemberCertByMemberId")
    public ItemResult<List<MemberCertDTO>> findMemberCertByMemberId(@Parameter(hidden = true)LoginInfo loginInfo) {
        checkLogin(loginInfo);
        List<MemberCertDTO> cert = iMemberService.findMemberCertByMemberId(loginInfo.getMemberId());
        return new ItemResult<>(cert);
    }

    @Operation(summary = "获取资质（已拥有资质、待审批资质、拒绝资质）")
    @PostMapping(value = "/findMemberCertList")
    public ItemResult<List<MemberCertViewDTO>> findMemberCertList(@Parameter(hidden = true)LoginInfo loginInfo) {
        checkLogin(loginInfo);
        List<MemberCertViewDTO> cert = iMemberService.findMemberCertList(loginInfo.getMemberId());
        return new ItemResult<>(cert);
    }

    @Operation(summary = "获取企业更新资质")
    @PostMapping(value = "/findUpdateTypeMemberCertById")
    public ItemResult<List<MemberCertDTO>> findUpdateTypeMemberCertById(@Parameter(name = "requestId", description = "请求id") @RequestParam String requestId) {
        List<MemberCertDTO> cert = iMemberService.findUpdateTypeMemberCertById(requestId);
        return new ItemResult<>(cert);
    }

    @Operation(summary = "根据ids查找用户详细信息")
    @PostMapping(value = "/findMemberDetailsByIds")
    public ItemResult<List<MemberDetailDTO>> findMemberDetailsByIds(@Parameter(name = "ids", description = "会员id列表") @RequestParam List<String> ids) {
        List<MemberDetailDTO> memberDetailsByIds = iMemberService.findMemberDetailsByIds(ids);
        return new ItemResult<>(memberDetailsByIds);
    }

    @Operation(summary = "获取一条变更记录")
    @PostMapping(value = "/findMemberApprovalRequest")
    public ItemResult<MemberApprovalRequestDTO> findMemberApprovalRequest(@Parameter(name = "requestId", description = "请求id") @RequestParam String requestId) {
        MemberApprovalRequestDTO request = iMemberService.findMemberApprovalRequest(requestId);
        return new ItemResult<>(request);
    }

    @Operation(summary = "获取会员请求列表")
    @PostMapping(value = "/findMemberApprovalRequestList")
    public ItemResult<List<MemberApprovalRequestDTO>> findMemberApprovalRequestList(@Parameter(hidden = true)LoginInfo loginInfo,
                                                                                    @Parameter(name = "query", description = "会员审批请求查询DTO") @RequestBody MemberApprovalRequestQueryDTO query) {
        checkLogin(loginInfo);
        query.setMemberId(loginInfo.getMemberId());
        List<MemberApprovalRequestDTO> list = iMemberService.findMemberApprovalRequestList(query);
        return new ItemResult<>(list);
    }

    @Operation(summary = "分页获取会员请求")
    @PostMapping(value = "/pageMemberApprovalRequests")
    public ItemResult<PageInfo<MemberApprovalRequestDTO>> pageMemberApprovalRequests(@Parameter(hidden = true)LoginInfo loginInfo,
                                                                                     @Parameter(name = "query", description = "会员审批请求分页DTO") @RequestBody MemberApprovalRequestPageDTO query) {
        if (query.getPageNum() == null) {
            query.setPageNum(1);
        }
        if (query.getPageSize() == null) {
            query.setPageSize(10);
        }
        query.getMemberApprovalRequestQueryDTO().setMemberId(loginInfo.getMemberId());
        PageInfo<MemberApprovalRequestDTO> pageInfo = iMemberService.pageMemberApprovalRequests(query.getMemberApprovalRequestQueryDTO(), query.getPageNum(), query.getPageSize());
        return new ItemResult<>(pageInfo);
    }

    @Operation(summary = "获取会员请求详情")
    @PostMapping(value = "/getMemberApprovalDetails")
    public ItemResult<MemberDTO> getMemberApprovalDetails(@Parameter(name = "requestId", description = "请求id") @RequestParam String requestId) {
        MemberDTO details = iMemberService.getMemberApprovalDetails(requestId);
        return new ItemResult<>(details);
    }

    @Operation(summary = "根据资质ID查找资质详情")
    @PostMapping(value = "/findMemberCertByCertId")
    public ItemResult<MemberCertDTO> findMemberCertByCertId(@Parameter(name = "certId", description = "资质ID") @RequestParam String certId) {
        MemberCertDTO cert = iMemberService.findMemberCertByCertId(certId);
        return new ItemResult<>(cert);
    }

    @Operation(summary = "根据ids查找用户")
    @PostMapping(value = "/findMemberByIds")
    public ItemResult<List<MemberDTO>> findMemberByIds(@Parameter(name = "ids", description = "会员id列表") @RequestParam List<String> ids) {
        List<MemberDTO> member = iMemberService.findMemberByIds(ids);
        return new ItemResult<>(member);
    }

    @Operation(summary = "个人名是否使用")
    @PostMapping(value = "/isMemberNameUsed")
    public ItemResult<Boolean> isMemberNameUsed(@Parameter(name = "memberName", description = "会员名称") @RequestParam String memberName) {
        boolean used = iMemberService.isMemberNameUsed(memberName);
        return new ItemResult<>(used);
    }

    @Operation(summary = "提交企业注册（买家） requestType - 申请类型 必须指明每个资质的资质类型，是经营资质、卖家资质、承运商资质。。。")
    @PostMapping(value = "/registerBuyer")
    public ItemResult<String> registerBuyer(@Parameter(hidden = true)LoginInfo loginInfo,
                                            @Parameter(name = "registerDTO", description = "会员注册DTO") @Valid @RequestBody MemberRegisterDTO registerDTO) {
        MemberRequestDTO dto = registerDTO2RequestDTO(registerDTO);
        checkLogin(loginInfo);
        dto.setMemberId(loginInfo.getMemberId());
        dto.setAccountId(loginInfo.getAccountId());
        dto.setOperatorId(loginInfo.getAccountId());
        dto.setOperator(loginInfo.getOperator());
        String s = iMemberService.registerBuyer(dto);
        return new ItemResult<>(s);
    }

    @Operation(summary = "保存经营信息 *草稿*")
    @PostMapping(value = "/saveBusinessInfoDraft")
    public ItemResult<String> saveBusinessInfoDraft(@Parameter(hidden = true)LoginInfo loginInfo,
                                                    @Parameter(name = "dto", description = "会员APP注册DTO") @Valid @RequestBody MemberRegisterByAppDTO dto) {
        checkLogin(loginInfo);
        dto.setMemberId(loginInfo.getMemberId());
        dto.setAccountId(loginInfo.getAccountId());
        dto.setOperatorId(loginInfo.getAccountId());
        dto.setOperator(loginInfo.getOperator());
        String s = iMemberService.saveBusinessInfoDraft(dto);
        return new ItemResult<>(s);
    }

    @Operation(summary = "保存经营信息 *草稿*")
    @PostMapping(value = "/saveRequestUserDraft")
    public String saveRequestUserDraft(@Parameter(hidden = true)LoginInfo loginInfo,
                                       @Parameter(name = "memberCertQueryDTO", description = "会员资质查询DTO") @RequestBody MemberCertQueryDTO memberCertQueryDTO) {
        checkLogin(loginInfo);
        MemberCertDTO certDTO = memberCertQuery2MemberCert(memberCertQueryDTO);
        certDTO.setMemberId(loginInfo.getMemberId());
        certDTO.setAccountId(loginInfo.getAccountId());
        certDTO.setOperatorId(loginInfo.getAccountId());
        certDTO.setOperator(loginInfo.getOperator());
        return iMemberService.saveRequestUserDraft(certDTO);
    }

    /**
     * 提交保存的草稿
     *
     * @return
     */
    @Operation(summary = "提交保存的草稿")
    @PostMapping(value = "/submitDraftRegisterBuyer")
    public String submitDraftRegisterBuyer(@Parameter(hidden = true)LoginInfo loginInfo) {
        checkLogin(loginInfo);
        return iMemberService.submitDraftRegisterBuyer(loginInfo.getMemberId());
    }

    /**
     * 提交保存的草稿
     *
     * @return
     */
    @Operation(summary = "提交保存的草稿")
    @PostMapping(value = "/submitDraftRegisterSeller")
    public String submitDraftRegisterSeller(@Parameter(hidden = true)LoginInfo loginInfo) {
        checkLogin(loginInfo);
        return iMemberService.submitDraftRegisterSeller(loginInfo.getMemberId());
    }

    /**
     * 提交保存的草稿
     *
     * @return
     */
    @Operation(summary = "提交保存的草稿")
    @PostMapping(value = "/submitDraftRegisterCarrier")
    public String submitDraftRegisterCarrier(@Parameter(hidden = true)LoginInfo loginInfo) {
        checkLogin(loginInfo);
        return iMemberService.submitDraftRegisterCarrier(loginInfo.getMemberId());
    }

    /**
     * 提交保存的草稿
     *
     * @return
     */
    @Operation(summary = "提交保存的草稿")
    @PostMapping(value = "/submitDraftRegisterSupplier")
    public String submitDraftRegisterSupplier(@Parameter(hidden = true)LoginInfo loginInfo) {
        checkLogin(loginInfo);
        return iMemberService.submitDraftRegisterSupplier(loginInfo.getMemberId());
    }

    @Operation(summary = "查询草稿")
    @PostMapping(value = "/findDraft")
    public ItemResult<MemberRequestDTO> findDraft(@Parameter(hidden = true)LoginInfo loginInfo) {
        checkLogin(loginInfo);
        MemberRequestDTO draft = iMemberService.findDraft(loginInfo.getMemberId());
        return new ItemResult<>(draft);
    }

    @Operation(summary = "企业注册（承运商）")
    @PostMapping(value = "/registerCarrier")
    public ItemResult<String> registerCarrier(@Parameter(hidden = true)LoginInfo loginInfo,
                                              @Parameter(name = "registerDTO", description = "会员注册DTO") @Valid @RequestBody MemberRegisterDTO registerDTO) {
        MemberRequestDTO dto = registerDTO2RequestDTO(registerDTO);
        checkLogin(loginInfo);
        dto.setMemberId(loginInfo.getMemberId());
        dto.setAccountId(loginInfo.getAccountId());
        dto.setOperatorId(loginInfo.getAccountId());
        dto.setOperator(loginInfo.getOperator());
        String s = iMemberService.registerCarrier(dto);
        return new ItemResult<>(s);
    }

    @Operation(summary = "分页查询用户")
    @PostMapping(value = "/pageMember")
    public ItemResult<PageInfo<MemberSimpleDTO>> pageMember(@Parameter(name = "query", description = "会员查询分页DTO") @RequestBody MemberQueryPageDTO query) {
        if (query.getPageNum() == null) {
            query.setPageNum(1);
        }
        if (query.getPageSize() == null) {
            query.setPageSize(10);
        }
        PageInfo<MemberSimpleDTO> pageInfo = iMemberService.pageMember(query.getMemberQueryDTO(), query.getPageNum(), query.getPageSize());
        return new ItemResult<>(pageInfo);
    }

    @Operation(summary = "一般资质变更,必须写明资质类型 certType、CertId")
    @PostMapping(value = "/updateCert")
    public ItemResult<String> updateCert(@Parameter(hidden = true)LoginInfo loginInfo,
                                         @Parameter(name = "dto", description = "资质变更DTO") @Valid @RequestBody UpdateCertDTO dto) {
        checkLogin(loginInfo);
        dto.setOperatorId(loginInfo.getAccountId());
        dto.setOperator(loginInfo.getOperator());
        if (dto.getMemberCertDTOList() != null && !dto.getMemberCertDTOList().isEmpty()) {
            dto.getMemberCertDTOList().forEach(item -> {
                item.setMemberId(loginInfo.getMemberId());
            });
        }
        return new ItemResult<>(iMemberService.updateCert(dto));
    }

    @Operation(summary = "按照id查询用户（没有资质）")
    @PostMapping(value = "/findMemberById")
    public ItemResult<MemberDetailDTO> findMemberById(@Parameter(hidden = true)LoginInfo loginInfo,
                                                      @Parameter(name = "memberId", description = "会员id") String memberId) {
        checkLogin(loginInfo);
        if (CsStringUtils.isEmpty(memberId)) {
            memberId = loginInfo.getMemberId();
        }
        MemberDetailDTO member = iMemberService.findMemberById(memberId);
        return new ItemResult<>(member);
    }

    @Operation(summary = "按照id查询用户经营信息（包括审批中资质）")
    @PostMapping(value = "/findMemberBusinessInfoById")
    public ItemResult<MemberBusinessInfoDTO> findMemberBusinessInfoById(@Parameter(hidden = true)LoginInfo loginInfo) {
        checkLogin(loginInfo);
        MemberBusinessInfoDTO member = iMemberService.findMemberBusinessInfoById(loginInfo.getMemberId());
        return new ItemResult<>(member);
    }

    @Operation(summary = "根据账户ID查找实名认证详情")
    @GetMapping(value = "/findRealNameCertByAccountId")
    public ItemResult<MemberCertDTO> findRealNameCertByAccountId(@Parameter(hidden = true)LoginInfo loginInfo,
                                                                 @Parameter(name = "accountId", description = "账户ID") @RequestParam(required = false) String accountId) {
        checkLogin(loginInfo);
        MemberCertDTO cert = iMemberService.findRealNameCertByAccountId(CsStringUtils.isNotBlank(accountId) ? accountId : loginInfo.getAccountId());
        return new ItemResult<>(cert);
    }

    @Operation(summary = "按照id查询用户（只包括已审批的资质）")
    @PostMapping(value = "/findRealMemberById")
    public ItemResult<MemberDTO> findRealMemberById(@Parameter(hidden = true)LoginInfo loginInfo) {
        checkLogin(loginInfo);
        MemberDTO member = iMemberService.findRealMemberById(loginInfo.getMemberId());
        return new ItemResult<>(member);
    }

    @Operation(summary = "企业注册（供应商）")
    @PostMapping(value = "/registerSupplier")
    public ItemResult<String> registerSupplier(@Parameter(hidden = true)LoginInfo loginInfo,
                                               @Parameter(name = "registerDTO", description = "会员注册DTO") @Valid @RequestBody MemberRegisterDTO registerDTO) {
        MemberRequestDTO dto = registerDTO2RequestDTO(registerDTO);
        checkLogin(loginInfo);
        dto.setMemberId(loginInfo.getMemberId());
        dto.setAccountId(loginInfo.getAccountId());
        dto.setOperatorId(loginInfo.getAccountId());
        dto.setOperator(loginInfo.getOperator());
        String s = iMemberService.registerSupplier(dto);
        return new ItemResult<>(s);
    }

    @Operation(summary = "资质文件认证,必须写明资质类型 certType")
    @PostMapping(value = "/submitCert")
    public ItemResult<String> submitCert(@Parameter(hidden = true)LoginInfo loginInfo,
                                         @Parameter(name = "dto", description = "提交资质DTO") @Valid @RequestBody SubmitCertDTO dto) {
        checkLogin(loginInfo);
        dto.setOperatorId(loginInfo.getAccountId());
        dto.setOperator(loginInfo.getOperator());
        if (dto.getMemberCertDTOList() != null && !dto.getMemberCertDTOList().isEmpty()) {
            dto.getMemberCertDTOList().forEach(item -> {
                item.setMemberId(loginInfo.getMemberId());
            });
        }
        return new ItemResult<>(iMemberService.submitCert(dto));
    }

    @Operation(summary = "撤销会员变更请求")
    @PostMapping(value = "/cancelRequest")
    public ItemResult<Boolean> cancelRequest(LoginInfo loginInfo,
                                             @Parameter(name = "dto", description = "撤销会员变更请求DTO") @Valid @RequestBody CancelRequestDTO dto) {
        checkLogin(loginInfo);
        dto.setOperatorId(loginInfo.getAccountId());
        return new ItemResult<>(iMemberService.cancelRequest(dto));
    }

    @Operation(summary = "企业注册（卖家）")
    @PostMapping(value = "/registerSeller")
    public ItemResult<String> registerSeller(@Parameter(hidden = true)LoginInfo loginInfo,
                                             @Parameter(name = "registerDTO", description = "会员注册DTO") @Valid @RequestBody MemberRegisterDTO registerDTO) {
        MemberRequestDTO dto = registerDTO2RequestDTO(registerDTO);
        checkLogin(loginInfo);
        dto.setMemberId(loginInfo.getMemberId());
        dto.setAccountId(loginInfo.getAccountId());
        dto.setOperatorId(loginInfo.getAccountId());
        dto.setOperator(loginInfo.getOperator());
        String s = iMemberService.registerSeller(dto);
        return new ItemResult<>(s);
    }

    @Operation(summary = "拒绝司机认证")
    @PostMapping(value = "/driverCertReject")
    public ItemResult<Object> driverCertReject(@Parameter(hidden = true) LoginInfo loginInfo) {
        checkLogin(loginInfo);
        iMemberService.driverCertReject(loginInfo.getMemberId());
        return new ItemResult<>(new Object());
    }


    @Operation(summary = "更新会员基本信息")
    @PostMapping(value = "/updateBaseInfo")
    public ItemResult<Object> updateBaseInfo(@Parameter(hidden = true) LoginInfo loginInfo,
                                     @Parameter(name = "dto", description = "会员基本信息查询DTO") @Valid @RequestBody MemberBaseInfoQueryDTO dto) {
        MemberBaseInfoDTO infoDTO = baseInfoQuery2BaseInfo(dto);
        checkLogin(loginInfo);
        infoDTO.setMemberId(loginInfo.getMemberId());
        infoDTO.setOperatorId(loginInfo.getAccountId());
        infoDTO.setOperator(loginInfo.getOperator());
        iMemberService.updateBaseInfo(infoDTO);
        return new ItemResult<>(new Object());
    }

    @Operation(summary = "获取会员状态")
    @PostMapping(value = "/getMemberStatus")
    public ItemResult<MemberStatusDTO> getMemberStatus(@Parameter(hidden = true) LoginInfo loginInfo) {
        checkLogin(loginInfo);
        MemberStatusDTO memberStatus = iMemberService.getMemberStatus(loginInfo.getMemberId());
        return new ItemResult<>(memberStatus);
    }

    @Operation(summary = "分页获取会员请求")
    @PostMapping(value = "/pageRegisterMemberApprovalRequests")
    public ItemResult<PageInfo<MemberApprovalRequestDTO>> pageRegisterMemberApprovalRequests(@Parameter(name = "query", description = "会员审批请求分页DTO") @RequestBody MemberApprovalRequestPageDTO query) {
        if (query.getPageNum() == null) {
            query.setPageNum(1);
        }
        if (query.getPageSize() == null) {
            query.setPageSize(10);
        }
        PageInfo<MemberApprovalRequestDTO> info = iMemberService.pageRegisterMemberApprovalRequests(query.getMemberApprovalRequestQueryDTO(), query.getPageNum(), query.getPageSize());
        return new ItemResult<>(info);
    }

    @Operation(summary = "查询附近的卖家")
    @GetMapping(value = "/anon/findSellerByCityCode")
    public ItemResult<List<MemberSimpleDTO>> findSellerByCityCode(@Parameter(name = "cityCode", description = "市code") String cityCode) {
        MemberQueryDTO query = new MemberQueryDTO();
        query.setCityCode(cityCode);
        query.setSellerFlg(1);
        PageInfo<MemberSimpleDTO> page = iMemberService.pageMember(query, 1, 20);
        return new ItemResult<>(page == null ? Lists.newArrayList() : page.getList());
    }

    @Operation(summary = "查找承运商")
    @PostMapping(value = "/searchCarrierList")
    public ItemResult<PageData<MemberCarrierDTO>> searchCarrierList(@Parameter(name = "searchCarrierDTOPageQuery", description = "搜索承运商对象分页查询对象") @RequestBody PageQuery<SearchCarrierDTO> searchCarrierDTOPageQuery) {
        return iMemberService.searchCarrierList(searchCarrierDTOPageQuery);
    }

    @Operation(summary = "根据会员名称和会员代码搜索会员列表")
    @GetMapping(value = "/search")
    public ItemResult<List<MemberSearchDTO>> search(LoginInfo loginInfo, @Parameter(name = "keyword", description = "查询关键词 会员名称或会员代码模糊查询") @RequestParam(required = false) String keyword)
    {
        MemberSearchQueryDTO memberSearchQueryDTO = new MemberSearchQueryDTO();
        memberSearchQueryDTO.setCustomerId(loginInfo.getMemberId());
        memberSearchQueryDTO.setKeyword(CsStringUtils.isBlank(keyword) ? null : keyword.trim());
        memberSearchQueryDTO.setNum(25);

        return new ItemResult<>(iMemberService.search(memberSearchQueryDTO));
    }

    @Operation(summary = "保存法人身份证信息")
    @PostMapping(value = "/update")
    public ItemResult<Boolean> update(LoginInfo loginInfo, @Parameter(name = "legalName", description = "法人姓名", required = true) @RequestParam String legalName, @Parameter(name = "legalCertificateCode", description = "法人身份证号码", required = true) @RequestParam String legalCertificateCode)
    {
        MemberDTO memberDTO = new MemberDTO();
        memberDTO.setMemberId(loginInfo.getMemberId());
        memberDTO.setLegalName(legalName);
        memberDTO.setLegalCertificateCode(legalCertificateCode);
        iMemberService.updateMemberInfoByMemberId(memberDTO);

        return new ItemResult<>(true);
    }

    private MemberRequestDTO registerDTO2RequestDTO(MemberRegisterDTO memberRegisterDTO) {
        MemberRequestDTO memberRequestDTO = new MemberRequestDTO();
        BeanUtils.copyProperties(memberRegisterDTO, memberRequestDTO);

        if (memberRegisterDTO.getBusinessQueryCert() != null) {
            memberRequestDTO.setBusinessCert(memberCertQuery2MemberCert(memberRegisterDTO.getBusinessQueryCert()));
        }
        if (memberRegisterDTO.getOrganizationQueryCert() != null) {
            memberRequestDTO.setOrganizationCert(memberCertQuery2MemberCert(memberRegisterDTO.getOrganizationQueryCert()));
        }
        if (memberRegisterDTO.getTaxQueryCert() != null) {
            memberRequestDTO.setTaxCert(memberCertQuery2MemberCert(memberRegisterDTO.getTaxQueryCert()));
        }

        if (memberRegisterDTO.getMemberRegisterCertDTOList() != null && !memberRegisterDTO.getMemberRegisterCertDTOList().isEmpty()) {
            List<MemberCertDTO> collect = memberRegisterDTO.getMemberRegisterCertDTOList().stream()
                    .map(item -> {
                        MemberCertDTO memberCertDTO = new MemberCertDTO();
                        BeanUtils.copyProperties(item, memberCertDTO);
                        return memberCertDTO;
                    }).toList();
            memberRequestDTO.setMemberCertDTOList(collect);
        }
        return memberRequestDTO;
    }

    private MemberBusinessInfoDTO businessQueryInfo2BusinessInfo(MemberBusinessInfoQueryDTO dto) {
        MemberBusinessInfoDTO infoDTO = new MemberBusinessInfoDTO();
        if (dto.getBusinessCert() != null) {
            infoDTO.setBusinessCert(memberCertQuery2MemberCert(dto.getBusinessCert()));
        }
        if (dto.getOrganizationCert() != null) {
            infoDTO.setOrganizationCert(memberCertQuery2MemberCert(dto.getOrganizationCert()));
        }
        if (dto.getTaxCert() != null) {
            infoDTO.setTaxCert(memberCertQuery2MemberCert(dto.getTaxCert()));
        }
        BeanUtils.copyProperties(dto, infoDTO);
        return infoDTO;
    }

    private MemberCertDTO memberCertQuery2MemberCert(MemberCertQueryDTO dto) {
        MemberCertDTO memberCertDTO = new MemberCertDTO();
        BeanUtils.copyProperties(dto, memberCertDTO);
        return memberCertDTO;
    }

    private MemberUpdateTypeDTO updateTypeQuery2UpdateTypeDTO(MemberUpdateTypeQueryDTO dto) {
        MemberUpdateTypeDTO memberUpdateTypeDTO = new MemberUpdateTypeDTO();
        memberUpdateTypeDTO.setMemberCertTypeDTOList(dto.getMemberCertQueryDTOList().stream()
                .map(item -> {
                    MemberCertDTO memberCertDTO = new MemberCertDTO();
                    BeanUtils.copyProperties(item, memberCertDTO);
                    return memberCertDTO;
                }).toList());
        return memberUpdateTypeDTO;
    }

    private MemberBaseInfoDTO baseInfoQuery2BaseInfo(MemberBaseInfoQueryDTO dto) {
        MemberBaseInfoDTO memberBaseInfoDTO = new MemberBaseInfoDTO();
        BeanUtils.copyProperties(dto, memberBaseInfoDTO);
        return memberBaseInfoDTO;
    }

    private void checkLogin(LoginInfo loginInfo) {
        if (loginInfo == null || CsStringUtils.isBlank(loginInfo.getAccountId())) {
            throw new BizException(MemberCode.LOGIN_ERROR, "你还没有登录，请先登录");
        }
    }
}
