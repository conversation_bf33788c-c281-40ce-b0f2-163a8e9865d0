package com.ecommerce.web.buyer.controller;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.member.api.session.LoginInfo;
import com.ecommerce.pay.api.v2.dto.MemberCreditDTO;
import com.ecommerce.pay.api.v2.dto.MemberCreditQueryDTO;
import com.ecommerce.pay.api.v2.dto.MemberCreditRecordDTO;
import com.ecommerce.pay.api.v2.dto.MemberCreditRecordQueryDTO;
import com.ecommerce.pay.api.v2.dto.electricInvoiceAccount.PageQuery;
import com.ecommerce.pay.api.v2.service.IMemberCreditService;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@RestController
@Tag(name = "MemberCreditController", description = "新版买家授信额度管理")
@RequestMapping(value = "/membercredit")
public class MemberCreditController
{
    @Autowired
    private IMemberCreditService memberCreditService;

    @Operation(summary = "查询授信记录列表")
    @PostMapping(value = "/list", consumes = "application/json")
    public ItemResult<PageInfo> list(LoginInfo loginInfo, @Parameter(name = "memberCreditQueryDTO", description = "买家授信分页查询对象") MemberCreditQueryDTO memberCreditQueryDTO)
    {
        memberCreditQueryDTO.setMemberId(loginInfo.getMemberId());

        PageInfo<MemberCreditDTO> pageInfo = memberCreditService.list(memberCreditQueryDTO);
        return new ItemResult<>(pageInfo);
    }

    @Operation(summary = "查询授信资金记录列表")
    @PostMapping(value = "/recordlist", consumes = "application/json")
    public ItemResult<PageInfo> recordList(LoginInfo loginInfo, @Parameter(name = "pageQuery", description = "买家授信资金记录分页查询对象") @RequestBody PageQuery<MemberCreditRecordQueryDTO> pageQuery)
    {
        if(pageQuery.getQueryDTO() == null)
        {
            pageQuery.setQueryDTO(new MemberCreditRecordQueryDTO());
        }
        MemberCreditRecordQueryDTO memberCreditRecordQueryDTO = pageQuery.getQueryDTO();
        memberCreditRecordQueryDTO.setMemberId(loginInfo.getMemberId());

        PageInfo<MemberCreditRecordDTO> pageInfo = memberCreditService.recordList(pageQuery);
        return new ItemResult<>(pageInfo);
    }
}
