package com.ecommerce.web.buyer.controller;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.information.api.dto.memberIntegral.IntegralChangeInfoQueryDTO;
import com.ecommerce.information.api.dto.memberIntegral.MemberIntegralChangeInfoDTO;
import com.ecommerce.information.api.dto.memberIntegral.MemberIntegralDTO;
import com.ecommerce.information.api.service.IMemberIntegralService;
import com.ecommerce.member.api.session.LoginInfo;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 *
 * <AUTHOR>
 */
@RequestMapping("/memberIntegral")
@RestController
@Tag(name = "MemberIntegralController", description = "会员积分相关")
@CrossOrigin
public class MemberIntegralController {

    @Autowired
    private IMemberIntegralService memberIntegralService;

    @Operation(summary = "通过会员id查询会员积分等级")
    @PostMapping(value = "/findByMemberId")
    public ItemResult<MemberIntegralDTO> findByMemberId(@Parameter(hidden = true)LoginInfo loginInfo) {
        return new ItemResult<>(memberIntegralService.findByMemberId(loginInfo.getMemberId()));
    }

    @Operation(summary = "翻页查询所有的积分记录")
    @PostMapping(value = "/findAllChangeInfo")
    public ItemResult<PageInfo<MemberIntegralChangeInfoDTO>> findAllChangeInfo(@Parameter(hidden = true)LoginInfo loginInfo,
                                                                               @Parameter(name = "dto", description = "积分记录查询DTO") @RequestBody IntegralChangeInfoQueryDTO dto) {
        dto.setMemberId(loginInfo.getMemberId());
        dto.setMemberCode(null);
        dto.setMemberName(null);
        return new ItemResult<>(memberIntegralService.findAllChangeInfo(dto));
    }

}
