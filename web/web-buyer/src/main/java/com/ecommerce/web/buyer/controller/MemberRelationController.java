package com.ecommerce.web.buyer.controller;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.member.api.dto.base.PageQuery;
import com.ecommerce.member.api.dto.member.MemberDetailDTO;
import com.ecommerce.member.api.dto.relation.ErpCustomCondDTO;
import com.ecommerce.member.api.dto.relation.ErpMultiCustomerInfoDTO;
import com.ecommerce.member.api.dto.relation.MemberBlacklistDTO;
import com.ecommerce.member.api.dto.relation.MemberRelationAddDTO;
import com.ecommerce.member.api.dto.relation.MemberRelationDTO;
import com.ecommerce.member.api.dto.relation.MemberRelationQueryDTO;
import com.ecommerce.member.api.dto.relation.MemberTagDTO;
import com.ecommerce.member.api.dto.relation.MemberTagGroupDTO;
import com.ecommerce.member.api.service.IMemberRelationService;
import com.ecommerce.member.api.service.IMemberService;
import com.ecommerce.member.api.session.LoginInfo;
import com.ecommerce.web.buyer.dto.relation.MemberBlacklistQueryPageDTO;
import com.ecommerce.web.buyer.dto.relation.MemberRelationListQueryDTO;
import com.ecommerce.web.buyer.dto.relation.MemberRelationPageDTO;
import com.ecommerce.web.buyer.dto.relation.MemberTagCreateDTO;
import com.ecommerce.web.buyer.dto.relation.MemberTagGroupQueryDTO;
import com.ecommerce.web.buyer.dto.relation.MemberTagGroupUpdateDTO;
import com.ecommerce.web.buyer.dto.relation.MemberTagUpdateDTO;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 */
@Slf4j
@Tag(name = "MemberRelationController", description = "会员关系")
@RestController
@RequestMapping("/relation")
public class MemberRelationController {

    @Autowired
    private IMemberRelationService iMemberRelationService;
    @Autowired
    private IMemberService iMemberService;

    @Operation(summary = "创建标签")
    @PostMapping(value = "/createMemberTag")
    public ItemResult<Object> createMemberTag(@Parameter(hidden = true) LoginInfo loginInfo,
                                      @Parameter(name = "memberTagCreateDTO", description = "会员标签创建DTO") @Valid @RequestBody MemberTagCreateDTO memberTagCreateDTO) {
        MemberTagDTO memberTagDTO = new MemberTagDTO();
        BeanUtils.copyProperties(memberTagCreateDTO, memberTagDTO);
        memberTagDTO.setMemberId(loginInfo.getMemberId());
        iMemberRelationService.createMemberTag(memberTagDTO, loginInfo.getAccountId());
        return new ItemResult<>(new Object());
    }

    @Operation(summary = "删除标签")
    @PostMapping(value = "/deleteMemberTag")
    public ItemResult<Object> deleteMemberTag(@Parameter(hidden = true) LoginInfo loginInfo,
                                      @Parameter(name = "memberTagId", description = "会员标签id") @RequestParam String memberTagId) {
        iMemberRelationService.deleteMemberTag(memberTagId, loginInfo.getAccountId());
        return new ItemResult<>(new Object());
    }

    @Operation(summary = "修改标签信息")
    @PostMapping(value = "/updateMemberTag")
    public ItemResult<Object> updateMemberTag(@Parameter(hidden = true) LoginInfo loginInfo,
                                      @Parameter(name = "memberTagUpdateDTO", description = "会员标签更新DTO") @RequestBody MemberTagUpdateDTO memberTagUpdateDTO) {
        MemberTagDTO memberTagDTO = new MemberTagDTO();
        BeanUtils.copyProperties(memberTagUpdateDTO, memberTagDTO);
        memberTagDTO.setMemberId(loginInfo.getMemberId());
        iMemberRelationService.updateMemberTag(memberTagDTO, loginInfo.getAccountId());
        return new ItemResult<>(new Object());
    }

    @Operation(summary = "查询标签组")
    @PostMapping(value = "/findMemberGroup")
    public ItemResult<List<MemberTagGroupDTO>> findMemberGroup(@Parameter(hidden = true)LoginInfo loginInfo,
                                                               @Parameter(name = "memberTagGroupQueryDTO", description = "会员标签组查询DTO") @RequestBody MemberTagGroupQueryDTO memberTagGroupQueryDTO) {
        MemberTagGroupDTO memberTagGroupDTO = new MemberTagGroupDTO();
        BeanUtils.copyProperties(memberTagGroupQueryDTO, memberTagGroupDTO);
        memberTagGroupDTO.setMemberId(loginInfo.getMemberId());
        List<MemberTagGroupDTO> memberGroup = iMemberRelationService.findMemberGroup(memberTagGroupDTO);
        return new ItemResult<>(memberGroup);
    }

    @Operation(summary = "查询标签组内的标签")
    @PostMapping(value = "/listMemberGroup")
    public ItemResult<List<MemberTagDTO>> listMemberGroup(@Parameter(name = "memberTagGroupId", description = "会员标签组id") @RequestParam String memberTagGroupId) {
        List<MemberTagDTO> memberTagDTOS = iMemberRelationService.listMemberGroup(memberTagGroupId);
        return new ItemResult<>(memberTagDTOS);
    }

    @Operation(summary = "查询标签详情")
    @PostMapping(value = "/getMemberTag")
    public ItemResult<MemberTagDTO> getMemberTag(@Parameter(name = "memberTagId", description = "会员标签id") @RequestParam String memberTagId) {
        MemberTagDTO memberTag = iMemberRelationService.getMemberTag(memberTagId);
        return new ItemResult<>(memberTag);
    }

    @Operation(summary = "判断两个对象是否有黑名单关系（myself，target）")
    @PostMapping(value = "/isDefriend")
    public ItemResult<Boolean> isDefriend(@Parameter(hidden = true)LoginInfo loginInfo,
                                          @Parameter(name = "customerId", description = "客户id") @RequestParam String customerId) {
        Boolean defriend = iMemberRelationService.isDefriend(loginInfo.getMemberId(), customerId);
        return new ItemResult<>(defriend);
    }

    @Operation(summary = "为客户关系添加标签")
    @PostMapping(value = "/addTagRelation")
    public ItemResult<Object> addTagRelation(@Parameter(hidden = true) LoginInfo loginInfo,
                                     @Parameter(name = "relationId", description = "关系id") @RequestParam String relationId,
                                     @Parameter(name = "tagId", description = "标签id") @RequestParam String tagId) {
        iMemberRelationService.addTagRelation(relationId, tagId, loginInfo.getAccountId());
        return new ItemResult<>(new Object());
    }

    @Operation(summary = "根据关系ID查询会员关系详情")
    @PostMapping(value = "/getMemberRelationDetailByRelationId")
    public ItemResult<MemberRelationDTO> getMemberRelationDetailByRelationId(@Parameter(name = "relationId", description = "关系id") @RequestParam String relationId) {
        MemberRelationDTO detail = iMemberRelationService.getMemberRelationDetailByRelationId(relationId);
        return new ItemResult<>(detail);
    }

    @Operation(summary = "根据会员ID和客户ID查询会员关系详情")
    @PostMapping(value = "/getMemberRelationDetailByMemberIdAndCustomerId")
    public ItemResult<MemberRelationDTO> getMemberRelationDetailByMemberIdAndCustomerId(@Parameter(hidden = true)LoginInfo loginInfo,
                                                                                        @Parameter(name = "customerId", description = "客户ID") @RequestParam String customerId) {
        MemberRelationDTO detail = iMemberRelationService.getMemberRelationDetailByMemberIdAndCustomerId(loginInfo.getMemberId(), customerId);
        return new ItemResult<>(detail);
    }

    @Operation(summary = "移除客户关系")
    @PostMapping(value = "/removeMemberRelationByMemberIdAndCustomerId")
    public ItemResult<Object> removeMemberRelationByMemberIdAndCustomerId(@Parameter(hidden = true) LoginInfo loginInfo,
                                                                  @Parameter(name = "customerId", description = "客户ID") @RequestParam String customerId) {
        iMemberRelationService.removeMemberRelationByMemberIdAndCustomerId(loginInfo.getMemberId(), customerId, loginInfo.getAccountId());
        return new ItemResult<>(new Object());
    }

    @Operation(summary = "查询标签组详情")
    @PostMapping(value = "/findMemberTagGroupDetail")
    public ItemResult<MemberTagGroupDTO> findMemberTagGroupDetail(@Parameter(name = "memberTagGroupId", description = "会员标签组id") @RequestParam String memberTagGroupId) {
        MemberTagGroupDTO groupDetail = iMemberRelationService.findMemberTagGroupDetail(memberTagGroupId);
        return new ItemResult<>(groupDetail);
    }

    @Operation(summary = "为客户关系移除标签")
    @PostMapping(value = "/removeTagRelation")
    public ItemResult<Object> removeTagRelation(@Parameter(name = "relationId", description = "关系id") @RequestParam String relationId,
                                        @Parameter(name = "tagId", description = "标签id") @RequestParam String tagId) {
        iMemberRelationService.removeTagRelation(relationId, tagId);
        return new ItemResult<>(new Object());
    }

    @Operation(summary = "批量得到某会员所有黑名单信息")
    @PostMapping(value = "/pageBlacklist")
    public ItemResult<PageInfo<MemberBlacklistDTO>> pageBlacklist(@Parameter(hidden = true)LoginInfo loginInfo,
                                                                  @Parameter(name = "dto", description = "会员黑名单查询分页DTO") @RequestBody MemberBlacklistQueryPageDTO dto) {
        if (dto.getPageNum() == null) {
            dto.setPageNum(1);
        }
        if (dto.getPageSize() == null) {
            dto.setPageSize(10);
        }
        dto.getMemberBlacklistQueryDTO().setMemberId(loginInfo.getMemberId());
        PageInfo<MemberBlacklistDTO> blacklist = iMemberRelationService.pageBlacklist(dto.getMemberBlacklistQueryDTO(), dto.getPageNum(), dto.getPageSize());
        return new ItemResult<>(blacklist);
    }

    @Operation(summary = "修改会员关系")
    @PostMapping(value = "/updateMemberRelation")
    public ItemResult<Object> updateMemberRelation(@Parameter(hidden = true) LoginInfo loginInfo,
                                           @Parameter(name = "memberRelationDTO", description = "会员关系DTO") @Valid @RequestBody MemberRelationDTO memberRelationDTO) {
        memberRelationDTO.setMemberId(loginInfo.getMemberId());
        iMemberRelationService.updateMemberRelation(memberRelationDTO, loginInfo.getAccountId());
        return new ItemResult<>(new Object());
    }

    @Operation(summary = "把某对象从某会员黑名单列表中删除")
    @PostMapping(value = "/removeObjFromBlacklist")
    public ItemResult<Object> removeObjFromBlacklist(@Parameter(hidden = true) LoginInfo loginInfo,
                                             @Parameter(name = "blackMemberId", description = "黑名单会员id") @RequestParam String blackMemberId,
                                             @Parameter(name = "remarks", description = "备注") @RequestParam String remarks) {
        iMemberRelationService.removeObjFromBlacklist(loginInfo.getMemberId(), blackMemberId, loginInfo.getMemberName(), remarks);
        return new ItemResult<>(new Object());
    }

    @Operation(summary = "通过交易添加会员关系")
    @PostMapping(value = "/addMemberRelationByTrade")
    public ItemResult<Object> addMemberRelationByTrade(@Parameter(hidden = true) LoginInfo loginInfo,
                                               @Parameter(name = "customerMemberId", description = "客户会员id") @RequestParam String customerMemberId) {
        iMemberRelationService.addMemberRelationByTrade(loginInfo.getMemberId(), customerMemberId);
        return new ItemResult<>(new Object());
    }

    @Operation(summary = "是否可以删除标签")
    @PostMapping(value = "/canDeleteMemberTag")
    public ItemResult<Boolean> canDeleteMemberTag(@Parameter(name = "memberTagId", description = "会员标签id") @RequestParam String memberTagId) {
        boolean b = iMemberRelationService.canDeleteMemberTag(memberTagId);
        return new ItemResult<>(b);
    }

    @Operation(summary = "由商家自己添加客户关系")
    @PostMapping(value = "/addMemberRelationBySelf")
    public ItemResult<Object> addMemberRelationBySelf(@Parameter(hidden = true) LoginInfo loginInfo,
                                              @Parameter(name = "customerMemberId", description = "客户会员id") @RequestParam String customerMemberId) {
        MemberRelationAddDTO dto = new MemberRelationAddDTO();
        dto.setMemberId(loginInfo.getMemberId());
        dto.setCustomerId(customerMemberId);
        iMemberRelationService.addMemberRelationBySelf(dto);
        return new ItemResult<>(new Object());
    }

    @Operation(summary = "管理员添加会员关系")
    @PostMapping(value = "/addMemberRelationByAdmin")
    public ItemResult<Object> addMemberRelationByAdmin(@Parameter(hidden = true) LoginInfo loginInfo,
                                               @Parameter(name = "customerMemberId", description = "客户会员id") @RequestParam String customerMemberId) {
        iMemberRelationService.addMemberRelationByAdmin(loginInfo.getMemberId(), customerMemberId, loginInfo.getAccountId());
        return new ItemResult<>(new Object());
    }

    @Operation(summary = "通过系统添加会员关系")
    @PostMapping(value = "/addMemberRelationBySystem")
    public ItemResult<Object> addMemberRelationBySystem(@Parameter(hidden = true) LoginInfo loginInfo,
                                                @Parameter(name = "customerMemberId", description = "客户会员id") @RequestParam String customerMemberId) {
        MemberRelationAddDTO dto = new MemberRelationAddDTO();
        dto.setMemberId(loginInfo.getMemberId());
        dto.setCustomerId(customerMemberId);
        iMemberRelationService.addMemberRelationBySystem(dto);
        return new ItemResult<>(new Object());
    }

    @Operation(summary = "移除客户关系")
    @PostMapping(value = "/removeMemberRelationByRelationId")
    public ItemResult<Object> removeMemberRelationByRelationId(@Parameter(hidden = true) LoginInfo loginInfo,
                                                       @Parameter(name = "relationId", description = "关系id") @RequestParam String relationId) {
        iMemberRelationService.removeMemberRelationByRelationId(relationId, loginInfo.getAccountId());
        return new ItemResult<>(new Object());
    }

    @Operation(summary = "删除标签组")
    @PostMapping(value = "/deleteMemberTagGroup")
    public ItemResult<Object> deleteMemberTagGroup(@Parameter(hidden = true) LoginInfo loginInfo,
                                           @Parameter(name = "memberGroupId", description = "会员标签组id") @RequestParam String memberTagGroupId) {
        iMemberRelationService.deleteMemberTagGroup(memberTagGroupId, loginInfo.getAccountId());
        return new ItemResult<>(new Object());
    }

    @Operation(summary = "修改标签组信息")
    @PostMapping(value = "/updateMemberGroup")
    public ItemResult<Object> updateMemberGroup(@Parameter(hidden = true) LoginInfo loginInfo,
                                        @Parameter(name = "memberTagGroupUpdateDTO", description = "会员标签组更新DTO") @Valid @RequestBody MemberTagGroupUpdateDTO memberTagGroupUpdateDTO) {
        MemberTagGroupDTO memberTagGroupDTO = new MemberTagGroupDTO();
        BeanUtils.copyProperties(memberTagGroupUpdateDTO, memberTagGroupDTO);
        memberTagGroupDTO.setMemberId(loginInfo.getMemberId());
        iMemberRelationService.updateMemberGroup(memberTagGroupDTO, loginInfo.getAccountId());
        return new ItemResult<>(new Object());
    }

    @Operation(summary = "增加某对象进黑名单")
    @PostMapping(value = "/addObjToBlacklist")
    public ItemResult<Object> addObjToBlacklist(@Parameter(hidden = true) LoginInfo loginInfo,
                                        @Parameter(name = "blackMemberId", description = "黑名单会员id") @RequestParam String blackMemberId,
                                        @Parameter(name = "remarks", description = "备注") @RequestParam String remarks) {
        iMemberRelationService.addObjToBlacklist(loginInfo.getMemberId(), blackMemberId, loginInfo.getMemberName(), remarks);
        return new ItemResult<>(new Object());
    }

    @Operation(summary = "创建标签组 （数量控制）")
    @PostMapping(value = "/createMemberTagGroup")
    public ItemResult<Object> createMemberTagGroup(@Parameter(hidden = true) LoginInfo loginInfo,
                                           @Parameter(name = "memberTagGroupDTO", description = "会员标签组DTO") @RequestBody MemberTagGroupDTO memberTagGroupDTO) {
        memberTagGroupDTO.setMemberId(loginInfo.getMemberId());
        iMemberRelationService.createMemberTagGroup(memberTagGroupDTO, loginInfo.getAccountId());
        return new ItemResult<>(new Object());
    }

    @Operation(summary = "分页查询客户（分类型）")
    @PostMapping(value = "/pageRelationMember")
    public ItemResult<PageInfo<MemberRelationDTO>> pageRelationMember(@Parameter(hidden = true)LoginInfo loginInfo,
                                                                      @Parameter(name = "memberRelationPageDTO", description = "会员关系分页DTO") @RequestBody MemberRelationPageDTO memberRelationPageDTO) {
        if (memberRelationPageDTO.getPageNum() == null) {
            memberRelationPageDTO.setPageNum(1);
        }
        if (memberRelationPageDTO.getPageSize() == null) {
            memberRelationPageDTO.setPageSize(10);
        }
        MemberRelationQueryDTO memberRelationQueryDTO = new MemberRelationQueryDTO();
        BeanUtils.copyProperties(memberRelationPageDTO.getMemberRelationListQueryDTO(), memberRelationQueryDTO);
        memberRelationQueryDTO.setMemberId(loginInfo.getMemberId());
        PageInfo<MemberRelationDTO> pageInfo = iMemberRelationService.pageRelationMember(memberRelationQueryDTO, memberRelationPageDTO.getPageNum(), memberRelationPageDTO.getPageSize());
        return new ItemResult<>(pageInfo);
    }

    @Operation(summary = "是否可以删除标签组")
    @PostMapping(value = "/canDeleteMemberTagGroup")
    public ItemResult<Boolean> canDeleteMemberTagGroup(@Parameter(name = "memberGroupId", description = "会员标签组id") @RequestParam String memberGroupId) {
        boolean b = iMemberRelationService.canDeleteMemberTagGroup(memberGroupId);
        return new ItemResult<>(b);
    }

    @Operation(summary = "查询会员关系返回list")
    @PostMapping(value = "/findRelationMemberList")
    public ItemResult<List<MemberRelationDTO>> findRelationMemberList(@Parameter(hidden = true)LoginInfo loginInfo,
                                                                      @Parameter(name = "query", description = "会员关系列表查询DTO") @RequestBody MemberRelationListQueryDTO query) {
        MemberRelationQueryDTO memberRelationQueryDTO = new MemberRelationQueryDTO();
        memberRelationQueryDTO.setCustomerId(loginInfo.getMemberId());
        List<MemberRelationDTO> b = iMemberRelationService.findRelationMemberList(memberRelationQueryDTO);
        List<MemberRelationDTO> list = new ArrayList<>();
        if (b != null && b.size() > 0) {
            for (MemberRelationDTO memberRelationDTO : b) {
                MemberDetailDTO memberDetailDTO = iMemberService.findMemberById(memberRelationDTO.getMemberId());
                memberRelationDTO.setMemberName(memberDetailDTO == null ? null : memberDetailDTO.getMemberName());
            }
            if (CsStringUtils.isNotBlank(query.getMemberName())) {
                for (MemberRelationDTO memberRelationDTO : b) {
                    if (memberRelationDTO.getMemberName() != null && memberRelationDTO.getMemberName().contains(query.getMemberName())) {
                        list.add(memberRelationDTO);
                    }
                }
            } else {
                return new ItemResult<>(b);
            }
        }
        return new ItemResult<>(list);
    }

    /**
     * 买家视角,分页查询erp信息
     * @param pageQuery
     * @return
     */
    @Operation(summary = "买家视角,分页查询erp信息")
    @PostMapping(value = "/pageCustomerInfoBuyerView")
    public ItemResult<PageInfo<ErpMultiCustomerInfoDTO>> pageCustomerInfoBuyerView(@Parameter(hidden = true)LoginInfo loginInfo, @RequestBody PageQuery<ErpCustomCondDTO> pageQuery) {
        if (pageQuery == null) {
            pageQuery = new PageQuery<>();
            pageQuery.setPageSize(20);
            pageQuery.setPageNum(1);
        }

        if (pageQuery.getQueryDTO() == null) {
            pageQuery.setQueryDTO(new ErpCustomCondDTO());
        }

        pageQuery.getQueryDTO().setCustomerId(loginInfo.getMemberId());

        return new ItemResult<>(iMemberRelationService.pageCustomerInfoBuyerView(pageQuery));
    }
}