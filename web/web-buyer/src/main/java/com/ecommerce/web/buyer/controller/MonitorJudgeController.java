package com.ecommerce.web.buyer.controller;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.order.api.dto.MonitorJudgeCondDTO;
import com.ecommerce.order.api.dto.MonitorJudgeResDTO;
import com.ecommerce.order.api.service.IMonitorJudgeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 监控判断服务
 *
 * <AUTHOR>
 */

@RestController
@Tag(name = "MonitorJudgeController", description = "监控判断服务")
@RequestMapping("/monitorJudge")
public class MonitorJudgeController {

    @Autowired
    private IMonitorJudgeService iMonitorJudgeService;

    @Operation(summary = "断定订单,发货单是否监控")
    @PostMapping(value = "/monitorJudge")
    public ItemResult<MonitorJudgeResDTO> monitorJudge(@Parameter(name = "monitorJudgeCondDTO", description = "监控判断条件输入实体") @RequestBody MonitorJudgeCondDTO monitorJudgeCondDTO) {
        return iMonitorJudgeService.monitorJudge(monitorJudgeCondDTO);
    }
}
