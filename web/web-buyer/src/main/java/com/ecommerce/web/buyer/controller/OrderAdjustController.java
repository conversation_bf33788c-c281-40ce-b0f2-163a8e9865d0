package com.ecommerce.web.buyer.controller;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.member.api.session.LoginInfo;
import com.ecommerce.order.api.dto.OrderAdjustDTO;
import com.ecommerce.order.api.dto.PageOrderAdjustDTO;
import com.ecommerce.order.api.service.IOrderAdjustService;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;


/**
 * @Created锛�Mon Jun 03 16:12:33 CST 2019
 * <AUTHOR>
 * @Version:2
 * @Description:null
*/

@RestController
@RequestMapping("/orderAdjust")
@Slf4j
@Tag(name = "OrderAdjust", description = "null")
public class OrderAdjustController {

   @Autowired 
   private IOrderAdjustService iOrderAdjustService;

   @Operation(summary = "新增手工调价历史")
   @PostMapping(value="/doCreateOrderAdjust")
   public ItemResult<Boolean> doCreateOrderAdjust(@RequestBody OrderAdjustDTO orderAdjustDTO)throws Exception{
      log.info("新增手工调价历史===========>{}",orderAdjustDTO);
      return iOrderAdjustService.doCreateOrderAdjust(orderAdjustDTO);
   }


   @Operation(summary = "订单确认调价")
   @PostMapping(value="/confirmOrderAdjust")
   public ItemResult<Boolean> confirmOrderAdjust(@Parameter(hidden = true)LoginInfo loginInfo,@Parameter(name = "orderId", description = "订单Id") @RequestParam String orderId)throws Exception{
      log.info("订单确认调价===========>{}",orderId);
      String operator=loginInfo.getAccountId();
      return iOrderAdjustService.confirmOrderAdjust(orderId,operator);
   }


   @Operation(summary = "根据订单ID分页查询调价列表")
   @PostMapping(value="/pageOrderAdjustList")
   public ItemResult<PageInfo<OrderAdjustDTO>> pageOrderAdjustList(@RequestBody PageOrderAdjustDTO pageOrderAdjustDTO)throws Exception{
      log.info("根据订单ID分页查询调价列表============>{}",pageOrderAdjustDTO);
      return iOrderAdjustService.pageOrderAdjustList(pageOrderAdjustDTO);
   }

    @Operation(summary = "计算调价总金额")
    @PostMapping(value="/countAdjustAmount")
    public ItemResult<BigDecimal> countAdjustAmount(@Parameter(name = "orderId", description = "订单Id") @RequestParam String orderId)throws Exception{
       log.info("计算调价费用：==========》{}",orderId);
       return iOrderAdjustService.countAdjustAmount(orderId);
    }



}
