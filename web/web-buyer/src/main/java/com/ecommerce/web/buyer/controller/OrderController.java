package com.ecommerce.web.buyer.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.ecommerce.base.api.dto.authRes.ResCheckDTO;
import com.ecommerce.base.api.enums.ResourceTypeEnum;
import com.ecommerce.base.api.service.IRoleService;
import com.ecommerce.common.enums.AppNames;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.result.AbstractResult;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.service.RedisService;
import com.ecommerce.common.utils.ArithUtils;
import com.ecommerce.common.utils.BeanConvertUtils;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.common.utils.DateUtil;
import com.ecommerce.goods.api.dto.DynamicAttributeDTO;
import com.ecommerce.goods.api.dto.GoodsAddItemDTO;
import com.ecommerce.goods.api.dto.ResourceDTO;
import com.ecommerce.goods.api.dto.ResourceDetailDTO;
import com.ecommerce.goods.api.enums.DynamicAttributeEnum;
import com.ecommerce.goods.api.enums.ResourceStatusEnum;
import com.ecommerce.goods.api.enums.contract.ContractStatusEnum;
import com.ecommerce.goods.api.service.IContractService;
import com.ecommerce.goods.api.service.IGoodsAddItemService;
import com.ecommerce.goods.api.service.IGoodsService;
import com.ecommerce.goods.api.service.IResourceService;
import com.ecommerce.logistics.api.enums.PickingBillTypeEnum;
import com.ecommerce.member.api.dto.account.AccountDTO;
import com.ecommerce.member.api.session.ERPAccountInfo;
import com.ecommerce.member.api.session.LoginInfo;
import com.ecommerce.order.api.dto.ApplyOrderInvoiceRequestDTO;
import com.ecommerce.order.api.dto.ApplyOrderInvoiceResponseDTO;
import com.ecommerce.order.api.dto.BuyerSelectInfoDTO;
import com.ecommerce.order.api.dto.CartAdditemDTO;
import com.ecommerce.order.api.dto.CartResourceDTO;
import com.ecommerce.order.api.dto.ConfirmOrderInvoiceReqDTO;
import com.ecommerce.order.api.dto.OrderCostsDTO;
import com.ecommerce.order.api.dto.OrderCountDTO;
import com.ecommerce.order.api.dto.OrderCreateDTO;
import com.ecommerce.order.api.dto.OrderDTO;
import com.ecommerce.order.api.dto.OrderItemAddDTO;
import com.ecommerce.order.api.dto.OrderItemDTO;
import com.ecommerce.order.api.dto.OrderLogisticsQueryDTO;
import com.ecommerce.order.api.dto.OrderLogisticsResultDTO;
import com.ecommerce.order.api.dto.OrderPayinfoDTO;
import com.ecommerce.order.api.dto.OrderPayinfoQueryDTO;
import com.ecommerce.order.api.dto.OrderQueryDTO;
import com.ecommerce.order.api.dto.OrderRefundDTO;
import com.ecommerce.order.api.dto.OrderRefundQueryDTO;
import com.ecommerce.order.api.dto.OrderStatusStatisticDTO;
import com.ecommerce.order.api.dto.TrContractDTO;
import com.ecommerce.order.api.dto.UninvoicedOrderDTO;
import com.ecommerce.order.api.dto.UninvoicedOrderReqDTO;
import com.ecommerce.order.api.dto.export.OrderExportCondDTO;
import com.ecommerce.order.api.dto.export.OrderExportDTO;
import com.ecommerce.order.api.dto.pay.UnderlinePaymentConfirmDTO;
import com.ecommerce.order.api.dto.redis.OrderRedisKeys;
import com.ecommerce.order.api.dto.trade.TrContractAdditemDTO;
import com.ecommerce.order.api.dto.trade.TrContractGoodsDTO;
import com.ecommerce.order.api.enums.OperatorTypeEnum;
import com.ecommerce.order.api.enums.OrderStatusEnum;
import com.ecommerce.order.api.enums.OrderTypeEnum;
import com.ecommerce.order.api.enums.PayStatusEnum;
import com.ecommerce.order.api.enums.PayTypeEnum;
import com.ecommerce.order.api.service.IBuyerSelectInfoService;
import com.ecommerce.order.api.service.ICartResourceService;
import com.ecommerce.order.api.service.IOrderExportService;
import com.ecommerce.order.api.service.IOrderPayinfoService;
import com.ecommerce.order.api.service.IOrderService;
import com.ecommerce.order.api.service.ITakeInfoService;
import com.ecommerce.pay.api.v2.enums.ChannelCodeEnum;
import com.ecommerce.web.buyer.dto.order.ContractOrderAgainDTO;
import com.ecommerce.web.buyer.dto.order.OrderCreateAgainDTO;
import com.ecommerce.web.buyer.utils.OrderHtmlExcelUtil;
import com.ecommerce.web.buyer.vo.order.UnderLinePayComfireVO;
import com.ecommerce.web.buyer.vo.order.UnderLinePayVO;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;


/**
 * 订单服务
 *
 * <AUTHOR>
 */

@Slf4j
@Tag(name = "OrderController", description = "订单服务")
@RestController
@RequestMapping("/order")
public class OrderController {

    @Autowired
    private IOrderService iOrderService;
    @Autowired
    private IOrderPayinfoService orderPayinfoService;
    @Autowired
    private ITakeInfoService iTakeInfoService;
    @Autowired
    private ICartResourceService cartResourceService;
    @Autowired
    private IResourceService iResourceService;
    @Autowired
    private IGoodsAddItemService goodsAddItemService;
    @Autowired
    private IBuyerSelectInfoService buyerSelectInfoService;
    @Autowired
    private IGoodsService iGoodsService;
    @Autowired
    private IContractService contractService;
    @Autowired
    IContractService iContractService;
    @Autowired
    private IOrderExportService orderExportService;
    @Autowired
    private RedisService redisService;

    @Autowired
    private IRoleService roleService;

    @Operation(summary = "买家订单详情")
    @PostMapping(value = "/getBuyerOrderDetail")
    public ItemResult<OrderDTO> getBuyerOrderDetail(@Parameter(hidden = true)LoginInfo loginInfo,
                                                    @Parameter(name = "orderId", description = "订单id") @RequestParam String orderId) {
        ItemResult<OrderDTO> orderDTO = iOrderService.getBuyerOrderDetail(orderId, loginInfo.getAccountId());
        OrderDTO data = Optional.ofNullable(orderDTO).map(AbstractResult::getData).orElse(null);
        if (data != null) {
            try {
                data.setShowPrice(data.getShowPrice() == null || data.getShowPrice());
                data.setTakeInfos(iTakeInfoService.getBuyerOrderTakeInfo(orderId, loginInfo.getAccountId()));
                for (OrderItemDTO orderItem : data.getOrderItems()) {
                    com.ecommerce.order.api.dto.ResourceDetailDTO resourceDetailDTO = new com.ecommerce.order.api.dto.ResourceDetailDTO();
                    BeanUtils.copyProperties(this.getResourceAttri(orderItem.getGoodsId()), resourceDetailDTO);
                    orderItem.setResourceForCheck(resourceDetailDTO);
                }
            } catch (Exception e) {
                log.error("getBuyerOrderDetail_getBuyerOrderTakeInfo", e);
            }
        }
        return orderDTO;
    }

    @Operation(summary = "询价下单")
    @PostMapping(value = "/doCreateEnquiryOrder")
    public ItemResult<OrderDTO> doCreateEnquiryOrder(@Parameter(hidden = true)LoginInfo loginInfo,
                                                     @Parameter(name = "order", description = "订单创建DTO") @RequestBody OrderCreateDTO order) {
        return iOrderService.doCreateEnquiryOrder(order, loginInfo.getAccountId());
    }

    @Operation(summary = "合同下单")
    @PostMapping(value = "/doCreateContractOrder")
    public ItemResult<OrderDTO> doCreateContractOrder(@Parameter(hidden = true)LoginInfo loginInfo,
                                                      @Parameter(name = "order", description = "订单创建DTO") @RequestBody OrderCreateDTO order) {
        order.setBuyerId(loginInfo.getMemberId());
        return iOrderService.doCreateContractOrder(order, loginInfo.getAccountId());
    }

    @Operation(summary = "自助下单")
    @PostMapping(value = "/doCreateSelfOrder")
    public ItemResult<OrderDTO> doCreateSelfOrder(@Parameter(hidden = true)LoginInfo loginInfo,
                                                  @Parameter(name = "order", description = "订单创建DTO") @RequestBody OrderCreateDTO order) {
        order.setBuyerId(loginInfo.getMemberId());
        log.info("doCreateSelfOrder_1:" + JSON.toJSONString(order));
        ItemResult<OrderDTO> resault = iOrderService.doCreateSelfOrder(order, loginInfo.getAccountId());
        log.info("doCreateSelfOrder_2:" + JSON.toJSONString(resault));
        return resault;
    }

    @Operation(summary = "买家取消订单")
    @PostMapping(value = "/doBuyerCancelOrder")
    public ItemResult<String> doBuyerCancelOrder(@Parameter(hidden = true)LoginInfo loginInfo,
                                                 @Parameter(name = "orderId", description = "订单id") @RequestParam String orderId,
                                                 @Parameter(name = "remarks", description = "备注") String remarks) {
        return iOrderService.doBuyerCancelOrder(orderId, remarks, loginInfo.getAccountId());
    }

    @Operation(summary = "买家关闭订单")
    @PostMapping(value = "/doBuyerCloseOrder")
    public ItemResult<String> doBuyerCloseOrder(@Parameter(hidden = true)LoginInfo loginInfo,
                                                @Parameter(name = "orderId", description = "订单id") @RequestParam String orderId,
                                                @Parameter(name = "remarks", description = "备注") String remarks) {
        return iOrderService.doBuyerCloseOrder(orderId, remarks, loginInfo.getAccountId());
    }

    @Operation(summary = "买家订单列表查询")
    @PostMapping(value = "/pageBuyerOrder")
    public ItemResult<PageInfo<OrderDTO>> pageBuyerOrder(@Parameter(hidden = true)LoginInfo loginInfo,
                                                         @Parameter(name = "orderQuery", description = "订单查询DTO") @RequestBody OrderQueryDTO orderQuery,
                                                         @Parameter(name = "pageSize", description = "每页条数") @RequestParam Integer pageSize,
                                                         @Parameter(name = "pageNum", description = "当前页码") @RequestParam Integer pageNum) {
        orderQuery.setBuyerId(loginInfo.getMemberId());
        //只有企业子账号受权限控制
        orderQuery.setRegionCodeList(null);
        orderQuery.setMdmCodeList(null);
        if (loginInfo.getAccountType() == AccountDTO.ACCOUNT_TYPE_MEMBER_SUB) {
            //设置买家行政区域权限
            if (CollectionUtils.isNotEmpty(loginInfo.getAccountRegionAdCodeList())) {
                log.info("downloadSellerOrder_regionCode:" + loginInfo.getAccountRegionAdCodeList());
                orderQuery.setRegionCodeList(loginInfo.getAccountRegionAdCodeList());
            }
            //设置ERP账户权限
            if (CollectionUtils.isNotEmpty(loginInfo.getErpAccountList())) {
                log.info("downloadSellerOrder_mdmCode:" + loginInfo.getErpAccountList());
                List<String> mdmCodeList = loginInfo.getErpAccountList().stream().map(ERPAccountInfo::getMdmCode)
                        .toList();
                orderQuery.setMdmCodeList(mdmCodeList);
            }
        }

        ItemResult<PageInfo<OrderDTO>> result = iOrderService.pageBuyerOrder(orderQuery, pageSize, pageNum);
        if (result != null && result.getData() != null && result.getData().getList() != null) {
            result.getData().getList().forEach(item ->{
                item.getOrderItems().forEach(orderItem -> {
                com.ecommerce.order.api.dto.ResourceDetailDTO resourceDetailDTO = new com.ecommerce.order.api.dto.ResourceDetailDTO();
                BeanUtils.copyProperties(this.getResourceAttri(orderItem.getGoodsId()), resourceDetailDTO);
                orderItem.setResourceForCheck(resourceDetailDTO);
                });
                item.setShowPrice(item.getShowPrice() == null ? Boolean.TRUE : item.getShowPrice());
            });
            updateShowPrice(result.getData().getList());
        }
        return result;
    }

    private ResourceDetailDTO getResourceAttri(String goodsId) {
        ResourceDetailDTO resultDTO = new ResourceDetailDTO();
        ItemResult<DynamicAttributeDTO> dynamicAttributeDTOResult = iGoodsService.getDynamicAttrByGoodsId(goodsId);
        if (dynamicAttributeDTOResult != null && dynamicAttributeDTOResult.getData() != null) {
            DynamicAttributeDTO dynamicAttributeDTO = dynamicAttributeDTOResult.getData();
            resultDTO.setIsSteelBar(dynamicAttributeDTO.check(DynamicAttributeEnum.STEEL_BAR.getCode()));
            ItemResult<Boolean> supportAddItemResult = iGoodsService.ifSupportAdditem(goodsId);
            resultDTO.setIsSupportAddItem(supportAddItemResult != null && supportAddItemResult.getData());
            resultDTO.setIsHidePrice(dynamicAttributeDTO.check(DynamicAttributeEnum.HIDE_PRICE.getCode()));
            resultDTO.setIsNeedSalesman(dynamicAttributeDTO.check(DynamicAttributeEnum.SALESMAN.getCode()));
            ItemResult<Boolean> computePorterageResult = iGoodsService.ifComputePorterage(goodsId);
            resultDTO.setIsCarry(computePorterageResult != null && computePorterageResult.getData());
            resultDTO.setIsTransactionTerms(dynamicAttributeDTO.check(DynamicAttributeEnum.TRANSACTION_TERMS.getCode()));
            resultDTO.setIsStoreDiscount(dynamicAttributeDTO.check(DynamicAttributeEnum.STORE_DISCOUNT.getCode()));
            resultDTO.setDeliveryTimeType(dynamicAttributeDTO.getValue(DynamicAttributeEnum.DELIVERY_TIME_TYPE.getCode()));
            return resultDTO;
        } else {
            return new ResourceDetailDTO();
        }
    }

    private void updateShowPrice(List<OrderDTO> list){
        if(CollectionUtils.isEmpty(list)){
            log.info("list is empty.");
            return;
        }
        Set<String> contractIds = list.stream().map(OrderDTO::getDealsId).filter(item -> CsStringUtils.isNotBlank(item)).collect(Collectors.toSet());
        if(CollectionUtils.isEmpty(contractIds)){
            log.info("contractIds is empty.");
            return;
        }
        List<String> showPriceContractIds = iContractService.showPriceFalseByContractIds(Lists.newArrayList(contractIds));
        log.info("showPriceFalseByContractIds:{}",showPriceContractIds);
        if(CollectionUtils.isEmpty(showPriceContractIds)){
            contractIds = Sets.newHashSet();
        }else{
            contractIds = Sets.newHashSet(showPriceContractIds);
        }
        for (OrderDTO orderDTO : list) {
            orderDTO.setShowPrice(true);
            if(contractIds.contains(orderDTO.getDealsId())){
                orderDTO.setShowPrice(false);
            }
        }
    }

    @Operation(summary = "计算订单商品费用、运费、搬运费、优惠")
    @PostMapping(value = "/computCosts")
    public ItemResult<OrderCostsDTO> computCosts(@Parameter(hidden = true)LoginInfo loginInfo,
                                                 @Parameter(name = "order", description = "订单创建DTO") @RequestBody OrderCreateDTO order) {
        order.setBuyerId(loginInfo.getMemberId());
        order.setOrderType(OrderTypeEnum.SELF.getCode());
        return iOrderService.computCosts(order);
    }

    @Operation(summary = "计算合同订单商品费用、运费、搬运费、优惠")
    @PostMapping(value = "/computContractCosts")
    public ItemResult<OrderCostsDTO> computContractCosts(@Parameter(hidden = true)LoginInfo loginInfo,
                                                         @Parameter(name = "order", description = "订单创建DTO") @RequestBody OrderCreateDTO order) {
        order.setBuyerId(loginInfo.getMemberId());
        order.setOrderType(OrderTypeEnum.CONTRACT.getCode());
        return iOrderService.computCosts(order);
    }

    @Operation(summary = "买家线下支付，上传凭证")
    @PostMapping(value = "/underLinePay")
    public ItemResult<String> underLinePay(@Parameter(hidden = true)LoginInfo loginInfo,
                                           @Parameter(name = "underLinePayVO", description = "线下支付VO") @RequestBody UnderLinePayVO underLinePayVO) {
        return iOrderService.underLinePay(underLinePayVO.getOrderId(), underLinePayVO.getPics(),
                underLinePayVO.getMome(), loginInfo.getAccountId());
    }

    @Operation(summary = "买家线下支付，上传凭证ByPayinfoId")
    @PostMapping(value = "/underLinePayinfoId")
    public ItemResult<String> underLinePayinfoId(@Parameter(hidden = true)LoginInfo loginInfo,
                                                 @Parameter(name = "underLinePayVO", description = "线下支付VO") @RequestBody UnderLinePayVO underLinePayVO) {
        return orderPayinfoService.underLinePayinfoId(underLinePayVO.getPayinfoId(), underLinePayVO.getPics(),
                underLinePayVO.getMome(), loginInfo.getAccountId());
    }

    @Operation(summary = "买家确认线下退款ByPayinfoId")
    @PostMapping(value = "/confirmUnderLineRefundPayinfoId")
    public ItemResult<String> confirmUnderLineRefundPayinfoId(@Parameter(hidden = true)LoginInfo loginInfo,
                                                              @Parameter(name = "comfireVO", description = "线下支付确认VO") @RequestBody UnderLinePayComfireVO comfireVO) {
        ItemResult<OrderPayinfoDTO> payinfo = orderPayinfoService.getOrderPayinfo(comfireVO.getPayinfoId());
        if (payinfo != null && payinfo.getData() != null &&
                PayTypeEnum.REFUND.getCode().equals(payinfo.getData().getPayinfoType())) {
            return orderPayinfoService.confirmUnderLinePayinfoId(comfireVO.getPayinfoId(), comfireVO.isPass(), loginInfo.getAccountId());
        } else {
            return new ItemResult<>(BasicCode.DATA_NOT_EXIST.getName(), "退款信息已失效");
        }
    }

    @Operation(summary = "买家确认线下退款ByRefundId")
    @PostMapping(value = "/confirmUnderLineRefundId")
    public ItemResult<String> confirmUnderLineRefundId(@Parameter(hidden = true)LoginInfo loginInfo,
                                                       @Parameter(name = "comfireVO", description = "线下支付确认VO") @RequestBody UnderLinePayComfireVO comfireVO) {
        OrderRefundQueryDTO queryDTO = new OrderRefundQueryDTO();
        queryDTO.setRefundId(comfireVO.getRefundId());
        ItemResult<List<OrderRefundDTO>> refs = orderPayinfoService.queryOrderRefundInfo(queryDTO);
        if (refs != null && CollectionUtils.isNotEmpty(refs.getData())) {
            for (OrderRefundDTO action : refs.getData()) {
                for (OrderPayinfoDTO payinfo : action.getPayinfos()) {
                    if (ChannelCodeEnum.OFFLINE.getCode().equals(payinfo.getPayinfoWay()) &&
                            (PayStatusEnum.IN_PAYMENT.code().equals(payinfo.getPayinfoStatus()) ||
                                    PayStatusEnum.FAILED.code().equals(payinfo.getPayinfoStatus()) ||
                                    PayStatusEnum.PARTIAL.code().equals(payinfo.getPayinfoStatus()))) {//线下待确认
                        ItemResult<String> result = orderPayinfoService.confirmUnderLinePayinfoId(payinfo.getPayinfoId(), comfireVO.isPass(), loginInfo.getAccountId());
                        if (!result.isSuccess()) {
                            result.setDescription("%s %s".formatted(payinfo.getPayinfoCode(), result.getDescription()));
                            return result;
                        }
                    }
                }
            }
        } else {
            return new ItemResult<>(BasicCode.DATA_NOT_EXIST.getName(), "退款信息已失效");
        }
        return new ItemResult<>("OK");
    }

    @Operation(summary = "生成支付系统凭证TradeBillId")
    @PostMapping(value = "/doPayinfoTradeBill")
    public ItemResult<String> doPayinfoTradeBill(@Parameter(hidden = true)LoginInfo loginInfo,
                                                 @Parameter(name = "payinfoId", description = "支付单id") String payinfoId) {
        ItemResult<OrderPayinfoDTO> payinfo = orderPayinfoService.doPayinfoTradeBill(payinfoId, loginInfo.getAccountId());
        if (payinfo.getData() != null && CsStringUtils.isNotBlank(payinfo.getData().getTradeBillId())) {
            return new ItemResult<>(payinfo.getData().getTradeBillId());
        }
        return new ItemResult<>();
    }

    @Operation(summary = "支付失败ByPayinfoId")
    @PostMapping(value = "/failedPayinfo")
    public ItemResult<String> failedPayinfo(@Parameter(hidden = true)LoginInfo loginInfo,
                                            @Parameter(name = "payinfoId", description = "支付单id") String payinfoId) {
        return orderPayinfoService.updPayinfoStatus(payinfoId, PayStatusEnum.FAILED.code(), loginInfo.getAccountId());
    }

    @Operation(summary = "订单详情ByTradeBillId")
    @PostMapping(value = "/getOrderByTradeBillId")
    public ItemResult<OrderDTO> getOrderByTradeBillId(@Parameter(name = "tradeBillId", description = "支付系统tradeId") @RequestParam String tradeBillId) {
        ItemResult<OrderDTO> orderDTO = iOrderService.getOrderByTradeBillId(tradeBillId);
        Optional.ofNullable(orderDTO).map(AbstractResult::getData).ifPresent(data -> data.setShowPrice(data.getShowPrice() == null || data.getShowPrice()));
        return orderDTO;
    }

    @Operation(summary = "买家删除订单")
    @PostMapping(value = "/doBuyerDeleteOrder")
    public ItemResult<String> doBuyerDeleteOrder(@Parameter(hidden = true)LoginInfo loginInfo,
                                                 @Parameter(name = "orderId", description = "订单id") String orderId) {
        return iOrderService.doBuyerDeleteOrder(orderId, loginInfo.getAccountId());
    }


    @Operation(summary = "查询支付单号")
    @PostMapping(value = "/getBuyerTradeBillIdByOrderId")
    public ItemResult<String> getBuyerTradeBillIdByOrderId(@Parameter(hidden = true)LoginInfo loginInfo,
                                                           @Parameter(name = "orderId", description = "订单id") @RequestParam String orderId) {
        return iOrderService.getBuyerTradeBillIdByOrderId(orderId, loginInfo.getMemberId());
    }

    @Operation(summary = "查询订单count")
    @PostMapping(value = "/countOrders")
    public ItemResult<Integer> countOrders(@Parameter(hidden = true)LoginInfo loginInfo,
                                           @Parameter(name = "orderCountDTO", description = "订单countDTO") @RequestBody OrderCountDTO orderCountDTO) {
        orderCountDTO.setCreateUser(loginInfo.getAccountId());
        orderCountDTO.setBuyerDel(false);
        return iOrderService.countOrders(orderCountDTO);
    }

    @Operation(summary = "根据订单生成购物车信息")
    @PostMapping(value = "/orderCartResource")
    public ItemResult<OrderCreateAgainDTO> orderCartResource(@Parameter(hidden = true)LoginInfo loginInfo,
                                                             @Parameter(name = "orderId", description = "订单id") @RequestParam String orderId) {
        ItemResult<OrderCreateAgainDTO> result = new ItemResult<>();
        OrderCreateAgainDTO createAgainDTO = new OrderCreateAgainDTO();
        List<CartResourceDTO> cartList = Lists.newArrayList();
        ItemResult<OrderDTO> orderDTO = iOrderService.getBuyerOrderDetail(orderId, loginInfo.getAccountId());
        OrderDTO data = Optional.ofNullable(orderDTO).map(AbstractResult::getData).orElse(null);
        if (data != null) {
            data.setShowPrice(data.getShowPrice() == null || data.getShowPrice());
            if (OrderTypeEnum.CONTRACT.getCode().equals(data.getOrderType())) {
                result.setSuccess(Boolean.FALSE);
                result.setDescription("不支持合同订单");
                return result;
            }

            for (OrderItemDTO item : data.getOrderItems()) {
                CartResourceDTO cartDTO = new CartResourceDTO();
                cartDTO.setBuyerId(loginInfo.getMemberId());
                cartDTO.setResourceId(item.getResourceId());
                cartDTO.setCountUnit(item.getUnits());
                cartDTO.setCountUnitName(item.getUnitsName());
                cartDTO.setQuantity(item.getItemQuantity());
                //判断商品价格是否变化和过期
                ResourceDTO resourceDTO = iResourceService.getResourceDetail(item.getResourceId());
                if (!ResourceStatusEnum.RES_STATUS100.code().equals(resourceDTO.getStatus())) {
                    createAgainDTO.setExpire(true);
                    return new ItemResult<>(createAgainDTO);
                }
                //自提或出厂价＋运费规则
                if (CsStringUtils.equals(data.getDeliverWay(), PickingBillTypeEnum.BUYER_TAKE.getCode()) ||
                        ArithUtils.multiply(resourceDTO.getArrivePrice(), BigDecimal.ONE).compareTo(BigDecimal.ZERO) < 1) {
                    BigDecimal historyPrice = ArithUtils.multiply(item.getOriginUnitPrice(), item.getUnconvertRate());
                    if (historyPrice.compareTo(resourceDTO.getFactoryPrice()) != 0) {
                        createAgainDTO.setPriceChange(true);
                    }
                } else {
                    //到位价反推，计算物流单价
                    BigDecimal logisticUnitPrice = ArithUtils.divide(item.getOriginLogisticPrice(), item.getItemQuantity());
                    BigDecimal historyPrice = ArithUtils.multiply(
                            ArithUtils.add(item.getOriginUnitPrice(), logisticUnitPrice), item.getUnconvertRate());
                    if (historyPrice.compareTo(resourceDTO.getArrivePrice()) != 0) {
                        createAgainDTO.setPriceChange(true);
                    }
                }

                //加价项
                List<OrderItemAddDTO> orderItemAdds = item.getOrderItemAdds();
                //判断加价项是否变化
                List<GoodsAddItemDTO> resourceAddItems = goodsAddItemService.listBusinessTemplate(item.getCategoryType(), resourceDTO.getSellerId());
                if (orderItemAdds != null && orderItemAdds.size() > 0 && resourceAddItems != null && resourceAddItems.size() > 0) {
                    log.info("====>进入更新加价项信息,orderItemAdds:{},resourceAddItems:{}", orderItemAdds, resourceAddItems);
                    for (OrderItemAddDTO orderItemAddDTO : orderItemAdds) {
                        boolean isExpire = true;
                        if (orderItemAddDTO.getAdditemId().equals("1")) {//施工单位不属于模板
                            continue;
                        }
                        for (GoodsAddItemDTO goodsAddItem : resourceAddItems) {
                            if (orderItemAddDTO.getParentId().equals(goodsAddItem.getAdditemId())) {
                                isExpire = false;
                                for (GoodsAddItemDTO childAddItem : goodsAddItem.getChildren()) {
                                    if (orderItemAddDTO.getAdditemId().equals(childAddItem.getAdditemId())) {
                                        log.info("加价项：{}，原订单价格：{}，现模板价格：{}", goodsAddItem.getAdditemName()
                                                , orderItemAddDTO.getAdditemPrice(), childAddItem.getAdditemPrice());
                                        if (orderItemAddDTO.getAdditemPrice().compareTo(childAddItem.getAdditemPrice()) != 0) {
                                            createAgainDTO.setPriceChange(true);
                                            log.info("加价项不相等，更新加价项！");
                                            orderItemAddDTO.setAdditemPrice(childAddItem.getAdditemPrice());
                                        }
                                    }
                                }
                                break;
                            }
                        }
                        if (isExpire) {
                            createAgainDTO.setExpire(true);
                            return new ItemResult<>(createAgainDTO);
                        }
                    }
                }

                List<CartAdditemDTO> cartAdditems = Lists.newArrayList();
                if (orderItemAdds != null && orderItemAdds.size() > 0) {
                    orderItemAdds.forEach(orderItemAddDTO -> {
                        CartAdditemDTO cartAdditemDTO = new CartAdditemDTO();
                        BeanUtils.copyProperties(orderItemAddDTO, cartAdditemDTO);
                        cartAdditems.add(cartAdditemDTO);
                    });
                    cartDTO.setCartAdditems(cartAdditems);
                }
                //购买选项
                List<BuyerSelectInfoDTO> selectInfos = item.getSelectInfos();
                List<BuyerSelectInfoDTO> selectBuyerList = new ArrayList<>();
                if (selectInfos != null && selectInfos.size() > 0) {
                    log.info("再次下单，钢筋选择信息：{}", selectInfos.get(0));
                    BeanUtils.copyProperties(selectInfos, selectBuyerList);
                    cartDTO.setSelectInfos(selectBuyerList);
                } else {
                    selectBuyerList = buyerSelectInfoService.findBuyerSelectInfoForOrder(orderId, data.getBuyerId());
                    for (BuyerSelectInfoDTO selectInfoDTO : selectBuyerList) {
                        if (selectInfoDTO.getOrderItemId().equals(item.getOrderItemId())) {
                            cartDTO.setSelectInfos(Arrays.asList(selectInfoDTO));
                            break;
                        }
                    }
                }
                ItemResult<CartResourceDTO> newCart = cartResourceService.addCart(cartDTO, loginInfo.getAccountId());
                if (newCart.isSuccess() && newCart.getData() != null) {
                    cartList.add(newCart.getData());
                } else {
                    result.setSuccess(Boolean.FALSE);
                    result.setCode(newCart.getCode());
                    result.setDescription(newCart.getDescription());
                    return result;
                }
            }
        }
        createAgainDTO.setCartList(cartList);

        return new ItemResult<>(createAgainDTO);
    }

    @Operation(summary = "查询订单countAll")
    @PostMapping(value = "/countOrdersAll")
    public ItemResult<Map<String, Integer>> countOrders(@Parameter(hidden = true)LoginInfo loginInfo) {
        Map<String, Integer> countMap = Maps.newHashMap();
        OrderCountDTO orderCountDTO = new OrderCountDTO();
        orderCountDTO.setCreateUser(loginInfo.getAccountId());
        orderCountDTO.setBuyerDel(false);
        countMap.put("all", iOrderService.countOrders(orderCountDTO).getData());

        orderCountDTO.setOrderStatus(OrderStatusEnum.WAIT_PAYMENT.code());
        countMap.put("wait_payment", iOrderService.countOrders(orderCountDTO).getData());

        orderCountDTO.setOrderStatus(OrderStatusEnum.WAIT_DELIVERED.code());
        countMap.put("wait_delivered", iOrderService.countOrders(orderCountDTO).getData());

        orderCountDTO.setOrderStatus(OrderStatusEnum.COMPLETED.code());
        countMap.put("completed", iOrderService.countOrders(orderCountDTO).getData());

        orderCountDTO.setOrderStatus(OrderStatusEnum.CONFIRMING.code());
        countMap.put("confirming", iOrderService.countOrders(orderCountDTO).getData());

        return new ItemResult<>(countMap);
    }

    @Operation(summary = "查询订单的退款请求单信息")
    @PostMapping(value = "/getOrderRefundInfo")
    public ItemResult<List<OrderRefundDTO>> getOrderRefundInfo(@Parameter(hidden = true)LoginInfo loginInfo,
                                                               @Parameter(name = "orderId", description = "订单id") @RequestParam String orderId) {
        OrderRefundQueryDTO queryDTO = new OrderRefundQueryDTO();
        queryDTO.setOrderId(orderId);
        return orderPayinfoService.queryOrderRefundInfo(queryDTO);
    }

    @Operation(summary = "查询退款请求单详情")
    @PostMapping(value = "/getRefundDetail")
    public ItemResult<OrderRefundDTO> getRefundDetail(@Parameter(hidden = true)LoginInfo loginInfo,
                                                      @Parameter(name = "refundId", description = "退款id") @RequestParam String refundId) {
        OrderRefundDTO refundDTO = null;
        OrderRefundQueryDTO queryDTO = new OrderRefundQueryDTO();
        queryDTO.setRefundId(refundId);
        ItemResult<List<OrderRefundDTO>> refs = orderPayinfoService.queryOrderRefundInfo(queryDTO);
        if (refs != null && CollectionUtils.isNotEmpty(refs.getData())) {
            refundDTO = refs.getData().get(0);
        }
        return new ItemResult<>(refundDTO);
    }

    @Operation(summary = "查询支付单信息ByOrgPayinfoId")
    @PostMapping(value = "/getPayinfoByOrgPayinfoId")
    public ItemResult<OrderPayinfoDTO> getPayinfoByOrgPayinfoId(@Parameter(hidden = true)LoginInfo loginInfo,
                                                                @Parameter(name = "orgPayinfoId", description = "原支付单id") @RequestParam String orgPayinfoId) {
        OrderPayinfoDTO payinfoDTO = null;
        OrderPayinfoQueryDTO payinfoQueryDTO = new OrderPayinfoQueryDTO();
        payinfoQueryDTO.setOriginalPayinfoId(orgPayinfoId);
        ItemResult<Page<OrderPayinfoDTO>> pages = orderPayinfoService.searchOrderPayinfoPage(payinfoQueryDTO, 100, 1);
        if (pages != null && pages.getData() != null &&
                CollectionUtils.isNotEmpty(pages.getData().getResult())) {
            payinfoDTO = pages.getData().getResult().get(0);
        }
        return new ItemResult<>(payinfoDTO);
    }

    @Operation(summary = "查询支付单信息")
    @PostMapping(value = "/getOrderPayinfo")
    public ItemResult<OrderPayinfoDTO> getOrderPayinfo(@Parameter(name = "payinfoId", description = "支付单id") @RequestParam String payinfoId) {
        return orderPayinfoService.getOrderPayinfo(payinfoId);
    }

    @Operation(summary = "更新支付单状态")
    @PostMapping(value = "/updateOrderPayinfoStatus")
    public ItemResult<OrderPayinfoDTO> updateOrderPayinfoStatus(@Parameter(name = "payinfoDTO", description = "支付单DTO") @RequestBody OrderPayinfoDTO payinfoDTO) {
        return orderPayinfoService.updateOrderPayinfoStatus(payinfoDTO);
    }

    @Operation(summary = "合同再次下单详情")
    @PostMapping(value = "/contractOrderDetail")
    public ItemResult<ContractOrderAgainDTO> contractOrderDetail(@Parameter(hidden = true) LoginInfo loginInfo,
                                          @Parameter(name = "orderId", description = "订单id") @RequestParam String orderId) {
        ItemResult<OrderDTO> orderDTO = iOrderService.getBuyerOrderDetail(orderId, loginInfo.getAccountId());
        ContractOrderAgainDTO contractOrderAgainDTO = new ContractOrderAgainDTO();
        contractOrderAgainDTO.setOrderDTO(orderDTO.getData());
        //判断合同是否变化
        com.ecommerce.order.api.dto.TrContractDTO contractDTO = orderDTO.getData().getContract();
        log.info("订单合同：{}", JSON.toJSONString(contractDTO));
        if (contractDTO.getIsHistory()) {
            //兼容历史数据
            if (CsStringUtils.isEmpty(contractDTO.getPresentContractId())) {
                contractOrderAgainDTO.setExpire(true);
                log.info("合同已过期，不能再次下单");
                return new ItemResult<>(contractOrderAgainDTO);
            } else {
                //获取最新版本的合同
                com.ecommerce.goods.api.dto.contract.TrContractDTO contract = contractService.getContract(contractDTO.getPresentContractId());
                log.info("新版本合同：" + JSON.toJSONString(contract));
                com.ecommerce.order.api.dto.TrContractDTO newContractDTO = new TrContractDTO();
                BeanUtils.copyProperties(contract, newContractDTO);
                contractDTO = newContractDTO;
            }
        }
        if (!CsStringUtils.equals(contractDTO.getContractStatus(), ContractStatusEnum.INEFFECT.getCode())) {
            contractOrderAgainDTO.setIneffective(true);
            log.info("合同已失效，不能再次下单");
            return new ItemResult<>(contractOrderAgainDTO);
        }
        //非历史单据
        if (!contractDTO.getIsHistory()) return new ItemResult<>(contractOrderAgainDTO);

        Map<String, TrContractGoodsDTO> contractGoodsMap = Maps.newTreeMap();
        if (CollectionUtils.isNotEmpty(contractDTO.getContractGoodsDTOS())) {
            contractDTO.getContractGoodsDTOS().stream().forEach(
                    contractGoods -> contractGoodsMap.put(contractGoods.getContractGoodsId(), contractGoods)
            );
        }
        Map<String, TrContractAdditemDTO> contractAdditemMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(contractDTO.getContractAdditemDTOS())) {
            contractDTO.getContractAdditemDTOS().stream().forEach(
                    contractAdditem -> contractAdditemMap.put(contractAdditem.getAdditemId(), contractAdditem)
            );
        }
        for (OrderItemDTO orderItem : orderDTO.getData().getOrderItems()) {
            if (contractGoodsMap.get(orderItem.getResourceId()) == null) {
                contractOrderAgainDTO.setExpire(true);
                log.info("合同商品已变更，请重新选择合同下单！");
                return new ItemResult<>(contractOrderAgainDTO);
            }
            TrContractGoodsDTO trContractGoodsDTO = contractGoodsMap.get(orderItem.getResourceId());
            //修改商品单价
            orderItem.setOriginUnitPrice(trContractGoodsDTO.getOutFactoryPrice());
            orderItem.setActualResUnitPrice(trContractGoodsDTO.getOutFactoryPrice());
            orderItem.setActualResDiscPrice(trContractGoodsDTO.getOutFactoryPrice());
            if (CollectionUtils.isEmpty(orderItem.getOrderItemAdds()) ||
                    CollectionUtils.isEmpty(contractDTO.getContractAdditemDTOS())) continue;
            //使用最新的加价项
            List<OrderItemAddDTO> orderAddItemList = Lists.newArrayList();
            contractDTO.getContractAdditemDTOS().stream().forEach(contractAdditem -> {
                        OrderItemAddDTO orderItemAddDTO = new OrderItemAddDTO();
                        BeanConvertUtils.copyProperties(contractAdditem, orderItemAddDTO);
                        orderAddItemList.add(orderItemAddDTO);
                    }
            );
            orderItem.setOrderItemAdds(orderAddItemList);
        }
        return new ItemResult<>(contractOrderAgainDTO);
    }

    @Operation(summary = "复制订单")
    @PostMapping(value = "/copyOrder")
    public ItemResult<OrderDTO> copyOrder(@Parameter(hidden = true)LoginInfo loginInfo,
                                          @Parameter(name = "orderId", description = "订单id") @RequestParam String orderId) {
        ItemResult<OrderDTO> orderDTO = iOrderService.getOrderAndItemInfo(orderId);
        if (orderDTO != null && orderDTO.getData() != null) {
            orderDTO.getData().setOrderCode(null);
            orderDTO.getData().setOrderId(null);
        }
        return orderDTO;
    }

    @Operation(summary = "未开票订单分页列表")
    @PostMapping(value = "/pageUninvoicedOrderList")
    public ItemResult<PageInfo<UninvoicedOrderDTO>> pageUninvoicedOrderList(@Parameter(hidden = true)LoginInfo loginInfo,
                                                                            @Parameter(name = "uninvoicedOrderReqDTO", description = "未开票订单分页列表入参") @RequestBody UninvoicedOrderReqDTO uninvoicedOrderReqDTO) {
        log.info("====pageUninvoicedOrderList====>{}", JSON.toJSONString(uninvoicedOrderReqDTO));
        uninvoicedOrderReqDTO.setBuyerId(loginInfo.getMemberId());
        return iOrderService.pageUninvoicedOrderList(uninvoicedOrderReqDTO);
    }

    @Operation(summary = "申请开票结算")
    @PostMapping(value = "/applyOrderInvoice")
    public ItemResult<ApplyOrderInvoiceResponseDTO> applyOrderInvoice(@Parameter(hidden = true)LoginInfo loginInfo,
                                                                      @Parameter(name = "applyOrderInvoiceRequestDTO", description = "申请开票结算入参对象") @RequestBody ApplyOrderInvoiceRequestDTO applyOrderInvoiceRequestDTO) {
        log.info("====applyOrderInvoice====>{}", JSON.toJSONString(applyOrderInvoiceRequestDTO));
        return iOrderService.applyOrderInvoice(applyOrderInvoiceRequestDTO, loginInfo.getAccountId());
    }

    @Operation(summary = "确认开票")
    @PostMapping(value = "/confirmOrderInvoice")
    public ItemResult<Boolean> confirmOrderInvoice(@Parameter(hidden = true)LoginInfo loginInfo,
                                                   @Parameter(name = "reqDTO", description = "确认订单开票DTO") @RequestBody ConfirmOrderInvoiceReqDTO reqDTO) {
        log.info("====confirmOrderInvoice====>{}", JSON.toJSONString(reqDTO));
        reqDTO.setCreateUser(loginInfo.getAccountId());
        return iOrderService.confirmOrderInvoice(reqDTO);
    }

    @Operation(summary = "根据ID查询退款详情")
    @GetMapping(value = "/findOrderRefundByRefundId")
    public ItemResult<OrderRefundDTO> findOrderRefundByRefundId(@Parameter(name = "refundId", description = "退款id") @RequestParam String refundId) {
        return orderPayinfoService.findOrderRefundByRefundId(refundId);
    }

    @Operation(summary = "分页查询退款请求单信息")
    @PostMapping(value = "/pageOrderRefund")
    public ItemResult<PageInfo<OrderRefundDTO>> pageOrderRefund(@Parameter(hidden = true) LoginInfo loginInfo,
                                      @Parameter(name = "queryDTO", description = "订单退款查询DTO") @RequestBody OrderRefundQueryDTO queryDTO) {
        queryDTO.setSellerId(loginInfo.getMemberId());
        return orderPayinfoService.pageOrderRefund(queryDTO);
    }

    @Operation(summary = "确认线下退款")
    @PostMapping(value = "/confirmUnderLineRefund")
    public ItemResult<Boolean> confirmUnderLineRefund(@Parameter(hidden = true) LoginInfo loginInfo,
                                             @Parameter(name = "paymentConfirmDTO", description = "线下付款确认对象") @RequestBody UnderlinePaymentConfirmDTO paymentConfirmDTO) {
        paymentConfirmDTO.setOperatorId(loginInfo.getAccountId());
        paymentConfirmDTO.setOperatorName(loginInfo.getAccountName());
        ItemResult<Void> result = orderPayinfoService.confirmUnderLineRefund(paymentConfirmDTO);
        return new ItemResult<>(result.isSuccess());
    }

    @Operation(summary = "下载卖家订单列表")
    @PostMapping(value = "/downloadSellerOrder")
    public ItemResult<String> downloadSellerOrder(@Parameter(hidden = true)LoginInfo loginInfo,
                                    @RequestBody OrderExportCondDTO orderExportCondDTO) {
        if (loginInfo.getSalesman()) {
            orderExportCondDTO.setSalesmanId(loginInfo.getAccountId());
        }
        orderExportCondDTO.setBuyerId(loginInfo.getMemberId());
        //只有企业子账号受权限控制
        orderExportCondDTO.setRegionCodeList(null);
        orderExportCondDTO.setMdmCodeList(null);
        if (loginInfo.getAccountType() == AccountDTO.ACCOUNT_TYPE_MEMBER_SUB) {
            //设置买家行政区域权限
            if (CollectionUtils.isNotEmpty(loginInfo.getAccountRegionAdCodeList())) {
                log.info("downloadSellerOrder_regionCode:" + loginInfo.getAccountRegionAdCodeList());
                orderExportCondDTO.setRegionCodeList(loginInfo.getAccountRegionAdCodeList());
            }
            //设置ERP账户权限
            if (CollectionUtils.isNotEmpty(loginInfo.getErpAccountList())) {
                log.info("downloadSellerOrder_mdmCode:" + loginInfo.getErpAccountList());
                List<String> mdmCodeList = loginInfo.getErpAccountList().stream().map(ERPAccountInfo::getMdmCode)
                        .toList();
                orderExportCondDTO.setMdmCodeList(mdmCodeList);
            }
        }
        //设置买家查询时间限制,开始时间2021-03-01
        orderExportCondDTO.setQueryStartTime(DateUtil.parse("2021-03-01", "yyyy-MM-dd"));

        orderExportCondDTO.setFromBuyerFlag(true);
        orderExportCondDTO.setOperatorId(loginInfo.getAccountId());
        final OrderExportCondDTO queryDTO = orderExportCondDTO;

        String key = OrderRedisKeys.ORDER_EXPORT + loginInfo.getAccountId();
        if(redisService.hasKey(key)){
            redisService.del(key);
        }
        //订单导出操作改为异步
        CompletableFuture.runAsync(() -> {
            try {
                orderExportService.exportOrderList(queryDTO);
            } catch (Exception e) {
                log.error("导出订单单异常:{}", e);
            }
        });
        return new ItemResult<>("OK");
    }

    @Operation(summary = "轮询订单下载结果")
    @PostMapping(value = "/getDownloadOrder")
    public ItemResult<String> getDownloadWaybill(@Parameter(hidden = true)LoginInfo info, @RequestBody List<String> selectHeaders) {
        //从redis中查询
        String key = OrderRedisKeys.ORDER_EXPORT + info.getAccountId();
        String result = "";
        if(redisService.hasKey(key)){
            result = redisService.get(key);
        }
        if (CsStringUtils.isBlank(result)) {
            //缓存中没有报表数据，返回前端0000，让前端继续轮询
            return new ItemResult<>("0000");
        }
        if (CsStringUtils.equals(result, "error")) {
            //查询报表数据时报错，返回前端0001，让前端停止轮询
            return new ItemResult<>("0001");
        }
        if (CsStringUtils.equals(result, "success")) {
            //查询报表数据成功，但上次轮询还未返回前端数据，返回0002让前端停止轮询
            return new ItemResult<>("0002");
        }
        redisService.set(key,"success");
        log.info("开始json转换=============");
        List<OrderExportDTO> resultDTOList = JSON.parseObject(result, new TypeReference<List<OrderExportDTO>>() {});
        log.info("json转换结束=============");
        ResCheckDTO resCheckDTO = new ResCheckDTO();
        resCheckDTO.setAccountId(info.getAccountId());
        resCheckDTO.setAppName(AppNames.WEB_SERVICE_BUYER.getPlatform());
        resCheckDTO.setResourceType(ResourceTypeEnum.BUTTON.getName());
        //订单列表价格显示的按钮
        resCheckDTO.setCode("3202B01-09");
        //有资源就要隐藏
        boolean needHidePrice = !roleService.checkRes(resCheckDTO);
        return new ItemResult<>(OrderHtmlExcelUtil.htmlExcelFormat(resultDTOList, selectHeaders, needHidePrice));
    }

    @Operation(summary = "获取报表字段")
    @GetMapping("/getReportHeaders")
    public ItemResult<List<String>> getReportHeaders() {
        return new ItemResult<>(OrderHtmlExcelUtil.getHeaders());
    }

    @Operation(summary = "统计订单状态")
    @PostMapping(value = "/statisticOrderStatus")
    public ItemResult<List<OrderStatusStatisticDTO>> statisticOrderStatus(@Parameter(hidden = true)LoginInfo loginInfo,
                                                                          @RequestBody OrderQueryDTO orderQuery) {
        orderQuery.setBuyerId(loginInfo.getMemberId());
        //只有企业子账号受权限控制
        orderQuery.setRegionCodeList(null);
        orderQuery.setMdmCodeList(null);
        if (loginInfo.getAccountType() == AccountDTO.ACCOUNT_TYPE_MEMBER_SUB) {
            //设置买家行政区域权限
            if (CollectionUtils.isNotEmpty(loginInfo.getAccountRegionAdCodeList())) {
                log.info("downloadSellerOrder_regionCode:" + loginInfo.getAccountRegionAdCodeList());
                orderQuery.setRegionCodeList(loginInfo.getAccountRegionAdCodeList());
            }
            //设置ERP账户权限
            if (CollectionUtils.isNotEmpty(loginInfo.getErpAccountList())) {
                log.info("downloadSellerOrder_mdmCode:" + loginInfo.getErpAccountList());
                List<String> mdmCodeList = loginInfo.getErpAccountList().stream().map(ERPAccountInfo::getMdmCode)
                        .toList();
                orderQuery.setMdmCodeList(mdmCodeList);
            }
        }
        return iOrderService.statisticOrderStatus(orderQuery);
    }

    @Operation(summary = "买家关闭自提订单")
    @PostMapping(value = "/closeOrderAndDoRefund")
    public ItemResult<Boolean> closeOrderAndDoRefund(@Parameter(hidden = true) LoginInfo loginInfo,
                                            @RequestBody OrderRefundDTO orderRefundDTO) {
        orderRefundDTO.setOperatorTypeEnum(OperatorTypeEnum.BUYER.code());
        orderRefundDTO.setCreateUser(loginInfo.getAccountId());
        ItemResult<OrderRefundDTO> result = orderPayinfoService.closeOrderAndDoRefund(orderRefundDTO);
        return new ItemResult<>(result.isSuccess());
    }

    @Operation(summary = "查询订单可退款信息")
    @GetMapping(value = "/findOrderAbleRefundInfo")
    public ItemResult<OrderRefundDTO> findOrderAbleRefundInfo(@RequestParam String orderId) {
        return orderPayinfoService.findOrderAbleRefundInfo(orderId);
    }

    @Operation(summary = "查询订单物流提货信息")
    @PostMapping(value = "/queryOrderLogisticsTakeInfo")
    public ItemResult<OrderLogisticsResultDTO> queryOrderLogisticsTakeInfo(@RequestBody OrderLogisticsQueryDTO orderLogisticsQueryDTO) {
        return iOrderService.queryOrderLogisticsTakeInfo(orderLogisticsQueryDTO);
    }
    
    @Operation(summary = "模糊查询订单地址详情")
    @PostMapping(value = "/queryAddressDetailByMemberId")
    public ItemResult<List<String>> queryAddressDetailByMemberId(@Parameter(hidden = true)LoginInfo loginInfo,@RequestBody OrderQueryDTO queryDTO) {
		queryDTO.setBuyerId(loginInfo.getMemberId());
        return iOrderService.queryAddressDetailByMemberId(queryDTO);
    }
}
