package com.ecommerce.web.buyer.controller;

import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.exception.BusinessOperationException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.pickingbill.AssignDispatchBillDTO;
import com.ecommerce.logistics.api.dto.pickingbill.AssignWaybillDTO;
import com.ecommerce.logistics.api.dto.pickingbill.PickingBillListDTO;
import com.ecommerce.logistics.api.dto.pickingbill.PickingBillListQueryDTO;
import com.ecommerce.logistics.api.service.IPickingBillService;
import com.ecommerce.member.api.session.LoginInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Description: 提货单服务
 * @Date: Create in 上午11:07 20/9/7
 */
@Slf4j
@Tag(name = "PickingBillController", description = "提货单服务")
@RestController
@RequestMapping("/pickingBill")
public class PickingBillController {

    @Autowired
    private IPickingBillService pickingBillService;

    @Operation(summary = "获取提货单列表")
    @PostMapping("/queryPickingBillList")
    public ItemResult<PageData<PickingBillListDTO>> queryPickingBillList(@Parameter(hidden = true)LoginInfo loginInfo,
                                                                         @Parameter(name = "pageQuery", description = "提货单列表查询对象分页查询对象") @RequestBody PageQuery<PickingBillListQueryDTO> pageQuery) {
        ItemResult<PageData<PickingBillListDTO>> itemResult = new ItemResult<>();
        pageQuery.getQueryDTO().setBuyerId(loginInfo.getMemberId());
        pageQuery.getQueryDTO().setRegionCodeList(loginInfo.getAccountRegionAdCodeList());
        try {
            ItemResult<PageData<PickingBillListDTO>> listResult = pickingBillService.queryPickingBillList(pageQuery);
            if (!listResult.isSuccess()) {
                throw new BizException(BasicCode.UNDEFINED_ERROR, listResult.getDescription());
            }
            itemResult.setData(listResult.getData());
            itemResult.setSuccess(Boolean.TRUE);
        } catch (Exception e) {
            itemResult.setSuccess(Boolean.FALSE);
            itemResult.setDescription("获取提货单列表失败:" + e.toString());
        }

        return itemResult;
    }

    @Operation(summary = "指派生成调度单")
    @PostMapping("/assignCreateDispatchBill")
    public ItemResult<Void> assignCreateDispatchBill(@Parameter(hidden = true)LoginInfo loginInfo,
                                                     @Parameter(name = "assignDispatchBillDTO", description = "指派调度单对象") @RequestBody AssignDispatchBillDTO assignDispatchBillDTO) {
        ItemResult<Void> itemResult = new ItemResult<>(null);
        try {
            assignDispatchBillDTO.setOperatorUserId(loginInfo.getAccountId());
            assignDispatchBillDTO.setOperatorUserName(loginInfo.getAccountName());
            ItemResult<Void> assignResult = pickingBillService.assignCreateDispatchBill(assignDispatchBillDTO);
            if (!assignResult.isSuccess()) {
                throw new BizException(BasicCode.UNDEFINED_ERROR, assignResult.getDescription());
            }
        } catch (Exception e) {
            itemResult.setSuccess(Boolean.FALSE);
            itemResult.setDescription("指派提货单异常:" + e.toString());
        }

        return itemResult;
    }

    @Operation(summary = "买家指派车辆生成运单")
    @PostMapping(value="/assignVehicleCreateWaybill")
    public ItemResult<Void> assignVehicleCreateWaybill(@Parameter(hidden = true)LoginInfo loginInfo,@RequestBody AssignWaybillDTO assignWaybillDTO)throws Exception{
        assignWaybillDTO.setOperatorUserId(loginInfo.getAccountId());
        assignWaybillDTO.setOperatorUserName(loginInfo.getAccountName());
        assignWaybillDTO.setUserId(loginInfo.getMemberId());
        assignWaybillDTO.setUserName(loginInfo.getMemberName());
        return pickingBillService.assignVehicleCreateWaybill(assignWaybillDTO);
    }
}
