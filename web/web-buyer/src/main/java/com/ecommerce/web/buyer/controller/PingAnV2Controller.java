package com.ecommerce.web.buyer.controller;

import com.alibaba.fastjson.JSON;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.member.api.dto.member.MemberDTO;
import com.ecommerce.member.api.dto.member.MemberDetailDTO;
import com.ecommerce.member.api.service.IMemberService;
import com.ecommerce.member.api.session.LoginInfo;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@RestController
@Tag(name = "PingAnV2", description = "平安见证宝相关接口")
@RequestMapping("/v2/pingan")
public class PingAnV2Controller
{
    private static Logger logger = LoggerFactory.getLogger(PingAnV2Controller.class);

    @Autowired
    private IMemberService memberService;

    @GetMapping("/getRecordInfo")
    public ItemResult<Boolean> getRecordInfo(LoginInfo loginInfo)
    {

        MemberDetailDTO memberDetailDTO = memberService.findMemberById(loginInfo.getMemberId());
        boolean enterprise = memberDetailDTO.getMemberType().startsWith(MemberDTO.ENTERPRISE_TYPE_PRE);

        logger.info("{},{}", enterprise, JSON.toJSONString(memberDetailDTO));

        if(!enterprise)
        {
            logger.info("个人账户认证查询");
            return new ItemResult<>(true);
        }


        boolean isOk = true;
        if (CsStringUtils.isBlank(memberDetailDTO.getLegalName()) || CsStringUtils.isBlank(memberDetailDTO.getLegalCertificateCode()))
        {
            logger.info("企业账户认证查询");
            isOk = false;
        }

        return new ItemResult<>(isOk);
    }
}
