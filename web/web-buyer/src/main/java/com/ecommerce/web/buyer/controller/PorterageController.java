package com.ecommerce.web.buyer.controller;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.logistics.api.dto.porterage.PorterageZoneListDTO;
import com.ecommerce.logistics.api.dto.porterage.PorterageZoneQueryDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;


/**
 * 搬运费规则服务
 *
 * <AUTHOR>
 */
@Slf4j
@Tag(name = "PorterageController", description = "搬运费规则服务")
@RestController
@RequestMapping("/porterage")
public class PorterageController {


    @Operation(summary = "区域范围查询搬运费列表")
    @PostMapping(value = "/queryZonePorterageList")
    public ItemResult<List<PorterageZoneListDTO>> queryZonePorterageList(@Parameter(name = "porterageZoneQueryDTO", description = "区域范围搬运费查询对象") @RequestBody PorterageZoneQueryDTO porterageZoneQueryDTO) {
        return new ItemResult<>(Collections.emptyList());
    }
}
