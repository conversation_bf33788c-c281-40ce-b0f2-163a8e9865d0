package com.ecommerce.web.buyer.controller;

import com.alibaba.fastjson.JSON;
import com.ecommerce.base.api.dto.ReceivingAddressCreateDTO;
import com.ecommerce.base.api.dto.ReceivingAddressDTO;
import com.ecommerce.base.api.dto.ReceivingAddressQueryDTO;
import com.ecommerce.base.api.dto.ReceivingAddressUpdateDTO;
import com.ecommerce.base.api.dto.warehouse.PageQuery;
import com.ecommerce.base.api.dto.wharf.WharfErpInfoListDTO;
import com.ecommerce.base.api.dto.wharf.WharfErpInfoQueryDTO;
import com.ecommerce.base.api.dto.wharf.WharfMaPQueryDTO;
import com.ecommerce.base.api.dto.wharf.WharfMapListDTO;
import com.ecommerce.base.api.service.IReceivingAddressService;
import com.ecommerce.base.api.service.IWharfService;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.member.api.dto.relation.MemberRelationDTO;
import com.ecommerce.member.api.service.IMemberRelationService;
import com.ecommerce.member.api.session.LoginInfo;
import com.ecommerce.web.common.PageQueryUtil;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;


/**
 * 收货地址相关service
 *
 * <AUTHOR>
 */

@Slf4j
@Tag(name = "ReceivingAddressController", description = "收货地址")
@RestController
@RequestMapping("/receivingAddress")
public class ReceivingAddressController {

    @Autowired
    private IReceivingAddressService iReceivingAddressService;

    @Autowired
    private IWharfService wharfService;

    @Autowired
    private IMemberRelationService memberRelationService;

    @Operation(summary = "修改收货地址")
    @PostMapping(value = "/update")
    public ItemResult<Object> update(@Parameter(hidden = true) LoginInfo loginInfo,
                             @Parameter(name = "receivingAddressUpdateDTO", description = "收货地址更新DTO") @Valid @RequestBody ReceivingAddressUpdateDTO receivingAddressUpdateDTO) {
        log.info("=======>json : " + JSON.toJSONString(receivingAddressUpdateDTO));
        if (CsStringUtils.isBlank(receivingAddressUpdateDTO.getId())) {
            return new ItemResult<>("id不能为空");
        }
        if (!checkPermission(receivingAddressUpdateDTO.getId(), loginInfo.getMemberId())) {
            return new ItemResult<>("无权限修改地址");
        }
        receivingAddressUpdateDTO.setOperator(loginInfo.getAccountId());
        receivingAddressUpdateDTO.setMemberId(loginInfo.getMemberId());
        iReceivingAddressService.update(receivingAddressUpdateDTO);
        return new ItemResult<>(new Object());
    }

    @Operation(summary = "创建收货地址")
    @PostMapping(value = "/create")
    public ItemResult<Object> create(@Parameter(hidden = true) LoginInfo loginInfo,
                             @Parameter(name = "receivingAddressCreateDTO", description = "收货地址创建DTO") @Valid @RequestBody ReceivingAddressCreateDTO receivingAddressCreateDTO) {
        receivingAddressCreateDTO.setOperator(loginInfo.getAccountId());
        receivingAddressCreateDTO.setMemberId(loginInfo.getMemberId());
        ItemResult<String> baseResult = iReceivingAddressService.create(receivingAddressCreateDTO);
        if (baseResult == null || !baseResult.isSuccess()) {
            log.error("添加地址失败:{}", baseResult);
            return ItemResult.fail(baseResult.getDescription());
        }
        return new ItemResult<>(new Object());
    }

    @Operation(summary = "查询当前会员的所有收货地址")
    @PostMapping(value = "/findByMemberId")
    public ItemResult<PageInfo<ReceivingAddressDTO>> findByMemberId(@Parameter(hidden = true)LoginInfo loginInfo,
                                                                    @Parameter(name = "pageSize", description = "每页条数") @RequestParam(required = false) Integer pageSize,
                                                                    @Parameter(name = "pageNum", description = "当前页码") @RequestParam(required = false) Integer pageNum,
                                                                    @Parameter(name = "keyWords", description = "关键字") @RequestParam(required = false) String keyWords,
                                                                    @Parameter(name = "type", description = "类型") @RequestParam(required = false) Integer type) {
        PageQuery<ReceivingAddressQueryDTO> pageQuery = new PageQuery<>();
        PageQueryUtil.initPage(pageQuery, pageNum, pageSize);
        ReceivingAddressQueryDTO receivingAddressQueryDTO = new ReceivingAddressQueryDTO();
        receivingAddressQueryDTO.setMemberId(loginInfo.getMemberId());
        if (CsStringUtils.isNotBlank(keyWords)) {
            receivingAddressQueryDTO.setKeyWords(keyWords);
        }
        if (type != null) {
            receivingAddressQueryDTO.setType(type);
        }
        pageQuery.setQueryDTO(receivingAddressQueryDTO);
        return new ItemResult<>(iReceivingAddressService.findPageByMemberId(pageQuery));
    }

    @Operation(summary = "根据id删除收货地址")
    @PostMapping(value = "/deleteById")
    public ItemResult<Object> deleteById(@Parameter(hidden = true) LoginInfo loginInfo,
                                 @Parameter(name = "id", description = "收货地址id") @RequestParam String id) {
        if (!checkPermission(id, loginInfo.getMemberId())) {
            return new ItemResult<>("无权限删除地址");
        }
        iReceivingAddressService.deleteById(id, loginInfo.getAccountId());
        return new ItemResult<>(new Object());
    }

    @Operation(summary = "根据id查找收货地址")
    @PostMapping(value = "/findById")
    public ItemResult<ReceivingAddressDTO> findById(@Parameter(hidden = true)LoginInfo loginInfo,
                                                    @Parameter(name = "id", description = "收货地址id") @RequestParam String id) {
        if (!checkPermission(id, loginInfo.getMemberId())) {
            return ItemResult.fail("无权限查看地址");
        }
        return new ItemResult<>(iReceivingAddressService.findById(id));
    }

    @Operation(summary = "设置默认收货地址")
    @PostMapping(value = "/setDefaultById")
    public ItemResult<Object> setDefaultById(@Parameter(hidden = true) LoginInfo loginInfo,
                                     @Parameter(name = "id", description = "收货地址id") @RequestParam String id) {
        if (!checkPermission(id, loginInfo.getMemberId())) {
            return new ItemResult<>("无权限设置默认地址");
        }
        iReceivingAddressService.setDefaultById(id, loginInfo.getMemberId(), loginInfo.getAccountId());
        return new ItemResult<>(new Object());
    }

    @Operation(summary = "根据会员id获取默认收货地址（企业员工则根据企业id获取默认收货地址）")
    @PostMapping(value = "/getDefaultByMemberId")
    public ItemResult<ReceivingAddressDTO> getDefaultByMemberId(@Parameter(hidden = true)LoginInfo loginInfo) {
        return new ItemResult<>(iReceivingAddressService.getDefaultByMemberId(loginInfo.getMemberId()));
    }

    @Operation(summary = "查询电商收货码头")
    @PostMapping(value = "/queryEcWharfName")
    public ItemResult<Map<String, String>> queryEcWharfName(@Parameter(hidden = true)LoginInfo loginInfo) {
        WharfMaPQueryDTO wharfMaPQueryDTO = new WharfMaPQueryDTO();
        wharfMaPQueryDTO.setMemberId(loginInfo.getMemberId());
        wharfMaPQueryDTO.setType("0");
        return wharfService.queryEcWharfName(wharfMaPQueryDTO);
    }

    @Operation(summary = "根据电商码头名称查询映射关系")
    @PostMapping(value = "/queryWharfMapList")
    public ItemResult<List<WharfMapListDTO>> queryWharfMapList(@Parameter(hidden = true)LoginInfo loginInfo, @Parameter(name = "ecWharfName", description = "电商码头名称") @RequestParam String ecWharfName) {
        WharfMaPQueryDTO wharfMaPQueryDTO = new WharfMaPQueryDTO();
        wharfMaPQueryDTO.setMemberId(loginInfo.getMemberId());
        wharfMaPQueryDTO.setEcWharfName(ecWharfName);
        wharfMaPQueryDTO.setType("0");
        return wharfService.queryWharfMapList(wharfMaPQueryDTO);
    }

    @Operation(summary = "查询erp收货码头信息")
    @PostMapping(value = "/findErpInfoList")
    public ItemResult<List<WharfErpInfoListDTO>> findErpInfoList(@Parameter(hidden = true)LoginInfo loginInfo,
                                                                 @Parameter(name = "memberId", description = "erp卖家Id") @RequestParam String memberId) {
        log.info("findErpInfoList : 厂商：{}, 当前会员：{}", memberId, loginInfo.getMemberId());
        WharfErpInfoQueryDTO wharfErpInfoQueryDTO = new WharfErpInfoQueryDTO();
        wharfErpInfoQueryDTO.setMemberId(memberId);
        if (!CsStringUtils.equals(memberId, loginInfo.getMemberId())) {
            MemberRelationDTO relationDTO = memberRelationService.getMemberRelationDetailByMemberIdAndCustomerId(memberId, loginInfo.getMemberId());
            log.info("MemberRelationDTO : {}", relationDTO);
            if (relationDTO != null) {
                wharfErpInfoQueryDTO.setMemCode(relationDTO.getMdmCode());
            }
        }
        wharfErpInfoQueryDTO.setLoadingWhart("0");
        return wharfService.findErpInfoList(wharfErpInfoQueryDTO);
    }

    private Boolean checkPermission(String id, String memberId) {
        ReceivingAddressDTO receivingAddressDTO = iReceivingAddressService.findById(id);
        if (receivingAddressDTO != null && receivingAddressDTO.getMemberId().equals(memberId)) {
            return true;
        }
        return false;
    }


}
