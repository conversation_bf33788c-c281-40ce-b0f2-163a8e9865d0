package com.ecommerce.web.buyer.controller;

import com.ecommerce.base.api.dto.saleregion.SaleRegionDTO;
import com.ecommerce.base.api.dto.saleregion.SaleRegionOption;
import com.ecommerce.base.api.dto.saleregion.SaleRegionRelationDTO;
import com.ecommerce.base.api.dto.saleregion.SaleRegionSampleDTO;
import com.ecommerce.base.api.dto.saleregion.SaleRegionTreeDTO;
import com.ecommerce.base.api.dto.saleregion.SaleStoreDTO;
import com.ecommerce.base.api.service.ISaleRegionService;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.member.api.dto.exception.MemberCode;
import com.ecommerce.member.api.session.LoginInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * 1、销售区域为一个树行结构数据,表：ba_sale_region<br/>
 *
 * <AUTHOR>
 *                                                    2、每个销售区域节点下关联一个或者多个仓库信息，表：ba_sale_region_store<br/>
 *                                                    2、每个销售区域节点下关联一个或者多个行政区域信息，表：ba_sale_region_relation<br/>
 */

@RestController
@Tag(name = "SaleRegionController", description = "销售区域")
@RequestMapping("/saleRegion")
public class SaleRegionController {

	@Autowired
	private ISaleRegionService iSaleRegionService;

	@Operation(summary = "根据销售区域id 查询其下和所有子区域下的行政区域列表（注意过滤重复）")
	@GetMapping(value = "/findAllRgionRelationById")
	public ItemResult<List<SaleRegionRelationDTO>> findAllRgionRelationById(@Parameter(hidden = true)LoginInfo loginInfo,
			@Parameter(name = "id", description = "销售区域id") String id) {
		checkLogin(loginInfo);
		isBlank("id", id);
		return new ItemResult<>(iSaleRegionService.findAllRgionRelationById(id));
	}

	@Operation(summary = "查询当前会员下的所有销售区域(不含仓库信息)")
	@GetMapping(value = "/findAllSampleByMemberId")
	public ItemResult<List<SaleRegionSampleDTO>> findAllSampleByMemberId(@Parameter(hidden = true)LoginInfo loginInfo) {
		checkLogin(loginInfo);
		return new ItemResult<>(iSaleRegionService.findAllSampleByMemberId(loginInfo.getMemberId()));
	}

	@Operation(summary = "根据会员ID查询所有销售区域(不含仓库信息)")
	@GetMapping(value = "/queryTreeByMemberId")
	public ItemResult<List<SaleRegionTreeDTO>> queryTreeByMemberId(
			@Parameter(name = "memberId", description = "会员id") @RequestParam String memberId) {
		return new ItemResult<>(iSaleRegionService.findTreeByMemberId(memberId));
	}

	@Operation(summary = "查询当前会员下的所有销售区域(不含仓库信息)")
	@GetMapping(value = "/findTreeByMemberId")
	public ItemResult<List<SaleRegionTreeDTO>> findTreeByMemberId(@Parameter(hidden = true)LoginInfo loginInfo) {
		checkLogin(loginInfo);
		List<SaleRegionTreeDTO> list = iSaleRegionService.findTreeByMemberId(loginInfo.getMemberId());
		return new ItemResult<>(list);
	}

	@Operation(summary = "查询当前会员下的所有销售区域(含仓库信息)")
	@GetMapping(value = "/findAllDetailByMemberId")
	public ItemResult<List<SaleRegionDTO>> findAllDetailByMemberId(@Parameter(hidden = true)LoginInfo loginInfo) {
		checkLogin(loginInfo);
		return new ItemResult<>(iSaleRegionService.findAllDetailByMemberId(loginInfo.getMemberId()));
	}

	@Operation(summary = "根据销售区域id 查询其下的行政区域列表")
	@GetMapping(value = "/findRgionRelationById")
	public ItemResult<List<SaleRegionRelationDTO>> findRgionRelationById(@Parameter(hidden = true)LoginInfo loginInfo,
			@Parameter(name = "id", description = "销售区域id") String id) {
		checkLogin(loginInfo);
		isBlank("id", id);
		return new ItemResult<>(iSaleRegionService.findRgionRelationById(id));
	}

	@Operation(summary = "查询指定区域下一级的区域信息(不含仓库信息)")
	@GetMapping(value = "/findOnlyChildsById")
	public ItemResult<List<SaleRegionSampleDTO>> findOnlyChildsById(@Parameter(hidden = true)LoginInfo loginInfo,
			@Parameter(name = "id", description = "销售区域id") String id) {
		checkLogin(loginInfo);
		isBlank("id", id);
		return new ItemResult<>(iSaleRegionService.findOnlyChildsById(id));
	}

	@Operation(summary = "获取默认仓库")
	@GetMapping(value = "/getDefaultStore")
	public ItemResult<SaleStoreDTO> getDefaultStore(@Parameter(hidden = true)LoginInfo loginInfo,
			@Parameter(name = "id", description = "销售区域id") String id) {
		checkLogin(loginInfo);
		isBlank("id", id);
		return new ItemResult<>(iSaleRegionService.getDefaultStore(id));
	}

	@Operation(summary = "查询单个销售区域信息(不含0个或多个仓库和行政信息)")
	@GetMapping(value = "/findSampleById")
	public ItemResult<SaleRegionSampleDTO> findSampleById(@Parameter(hidden = true)LoginInfo loginInfo,
			@Parameter(name = "id", description = "销售区域id") String id) {
		checkLogin(loginInfo);
		isBlank("id", id);
		return new ItemResult<>(iSaleRegionService.findSampleById(id));
	}

	@Operation(summary = "查询指定区域下一级的区域信息(含仓库信息)")
	@GetMapping(value = "/findChildsById")
	public ItemResult<List<SaleRegionDTO>> findChildsById(@Parameter(hidden = true)LoginInfo loginInfo,
			@Parameter(name = "id", description = "销售区域id") String id) {
		checkLogin(loginInfo);
		isBlank("id", id);
		return new ItemResult<>(iSaleRegionService.findChildsById(id));
	}

	@Operation(summary = "根据销售区域id 查询其下的仓库列表")
	@GetMapping(value = "/findStoreById")
	public ItemResult<List<SaleStoreDTO>> findStoreById(@Parameter(hidden = true)LoginInfo loginInfo,
			@Parameter(name = "id", description = "销售区域id") String id) {
		checkLogin(loginInfo);
		isBlank("id", id);
		return new ItemResult<>(iSaleRegionService.findStoreById(id));
	}

	@Operation(summary = "表：ba_account_sale_region 查询人员-销售区域关系表关系")
	@GetMapping(value = "/findByAccountId")
	public ItemResult<List<SaleRegionDTO>> findByAccountId(@Parameter(hidden = true)LoginInfo loginInfo,
			@Parameter(name = "accountId", description = "账号id") String accountId) {
		checkLogin(loginInfo);
		isBlank("accountId", accountId);
		return new ItemResult<>(iSaleRegionService.findByAccountId(accountId));
	}

	@Operation(summary = "根据销售区域id 查询其下和所有子区域下的仓库列表（注意过滤重复）")
	@GetMapping(value = "/findAllStoreById")
	public ItemResult<List<SaleStoreDTO>> findAllStoreById(@Parameter(hidden = true)LoginInfo loginInfo,
			@Parameter(name = "storeId", description = "销售区域id") String storeId) {
		checkLogin(loginInfo);
		isBlank("storeId", storeId);
		return new ItemResult<>(iSaleRegionService.findAllStoreById(storeId));
	}

	@Operation(summary = "查询单个销售区域信息(含0个或多个仓库和行政信息)")
	@GetMapping(value = "/findById")
	public ItemResult<SaleRegionDTO> findById(@Parameter(hidden = true)LoginInfo loginInfo,
			@Parameter(name = "id", description = "销售区域id") String id) {
		checkLogin(loginInfo);
		isBlank("id", id);
		return new ItemResult<>(iSaleRegionService.findById(id));
	}

	@Operation(summary = "根据名称模糊查询一级销售区域")
    @PostMapping(value="/querySaleRegionBySaleRegionName")
    public List<SaleRegionOption> querySaleRegionBySaleRegionName(@Parameter(hidden = true)LoginInfo loginInfo,@RequestBody SaleRegionOption dto) {
		checkLogin(loginInfo);
        return iSaleRegionService.querySaleRegionBySaleRegionName(dto);
    }

	private void checkLogin(LoginInfo loginInfo) {
        if (loginInfo == null || CsStringUtils.isBlank(loginInfo.getAccountId())) {
			throw new BizException(MemberCode.LOGIN_ERROR, "你还没有登录，请先登录");
		}
	}

	private void isBlank(String paramName, String paramValue) {
        if (CsStringUtils.isBlank(paramValue)) {
			throw new BizException(BasicCode.INVALID_PARAM, "参数[" + paramName + "]不可为空");
		}
	}
}
