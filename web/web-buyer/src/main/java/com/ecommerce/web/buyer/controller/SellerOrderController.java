package com.ecommerce.web.buyer.controller;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.order.api.dto.OrderDTO;
import com.ecommerce.order.api.service.IOrderService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


/**
 * 订单服务
 *
 * <AUTHOR>
 */
@Slf4j
@Tag(name = "SellerOrderController", description = "订单服务")
@RestController
@RequestMapping("/sellerOrder")
public class SellerOrderController {

    @Autowired
    private IOrderService iOrderService;

    @Operation(summary = "订单详情ByTradeBillId")
    @PostMapping(value = "/getOrderByTradeBillId")
    public ItemResult<OrderDTO> getOrderByTradeBillId(@Parameter(name = "tradeBillId", description = "支付系统tradeId") @RequestParam String tradeBillId) {
        return iOrderService.getOrderByTradeBillId(tradeBillId);
    }

}
