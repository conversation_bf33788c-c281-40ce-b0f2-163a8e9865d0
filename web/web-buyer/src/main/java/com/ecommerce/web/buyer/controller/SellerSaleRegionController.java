package com.ecommerce.web.buyer.controller;

import com.ecommerce.base.api.dto.saleregion.SaleRegionDTO;
import com.ecommerce.base.api.service.ISaleRegionService;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.member.api.session.LoginInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * <AUTHOR>
 * @created 10:57 22/06/2019
 * @description TODO
 */
@RestController
@Tag(name = "SellerSaleRegionController", description = "销售区域")
@RequestMapping("/sellerSaleRegion")
public class SellerSaleRegionController {

    @Autowired
    private ISaleRegionService iSaleRegionService;

    @Operation(summary = "查询当前会员下的所有销售区域(含仓库信息)")
    @GetMapping(value = "/findAllDetailByMemberId")
    public ItemResult<List<SaleRegionDTO>> findAllDetailByMemberId(@Parameter(hidden = true)LoginInfo loginInfo,
                                                                   @Parameter(name = "memberId", description = "会员id") @RequestParam(required = false) String memberId) {
        List<SaleRegionDTO> res;
        if (CsStringUtils.isNotBlank(memberId)) {
            res = iSaleRegionService.findAllDetailByMemberId(memberId);
        } else {
            res = iSaleRegionService.findAllDetailByMemberId(loginInfo.getMemberId());
        }
        return new ItemResult<>(res);
    }
}
