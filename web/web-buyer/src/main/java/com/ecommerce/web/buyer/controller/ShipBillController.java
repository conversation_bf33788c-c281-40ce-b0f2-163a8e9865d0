package com.ecommerce.web.buyer.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.ecommerce.base.api.dto.authRes.ResCheckDTO;
import com.ecommerce.base.api.enums.ResourceTypeEnum;
import com.ecommerce.base.api.service.IRoleService;
import com.ecommerce.common.enums.AppNames;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.exception.BusinessOperationException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.common.service.RedisService;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.logistics.api.dto.GoodsInfoDTO;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.shipbill.CancelShipBillDTO;
import com.ecommerce.logistics.api.dto.shipbill.CloseShipBillDTO;
import com.ecommerce.logistics.api.dto.shipbill.QrCodeDTO;
import com.ecommerce.logistics.api.dto.shipbill.ReassignShipBillDTO;
import com.ecommerce.logistics.api.dto.shipbill.ShipBillListDTO;
import com.ecommerce.logistics.api.dto.shipbill.ShipBillQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.CompleteShipBillDTO;
import com.ecommerce.logistics.api.dto.waybill.QueryGoodsDTO;
import com.ecommerce.logistics.api.dto.waybill.TradeWaybillDetailDTO;
import com.ecommerce.logistics.api.enums.ShipBillStatusEnum;
import com.ecommerce.logistics.api.service.IShipBillService;
import com.ecommerce.member.api.dto.account.AccountDTO;
import com.ecommerce.member.api.session.ERPAccountInfo;
import com.ecommerce.member.api.session.LoginInfo;
import com.ecommerce.report.api.dto.ReportShipBillQueryDTO;
import com.ecommerce.report.api.dto.ReportWaybillResultDTO;
import com.ecommerce.report.api.dto.redis.ReportRedisKeys;
import com.ecommerce.report.api.service.IReportService;
import com.ecommerce.web.buyer.utils.BuyerWaybillExcelUtil;
import com.google.common.collect.Lists;
import feign.Response;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.InputStreamSource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


@Slf4j
@Tag(name = "ShipBillController", description = "运单服务接口")
@RestController
@RequestMapping("/shipBill")
public class ShipBillController {

    @Autowired
    private IShipBillService shipBillService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private IReportService reportService;

    @Autowired
    private IRoleService roleService;
    
    private static final String LICENSE_PLATE_NUMBER_ONE = "(^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼]{1})";

    @Operation(summary = "运单列表分页查询")
    @PostMapping(value = "/queryShipBillList")
    public ItemResult<PageData<ShipBillListDTO>> queryShipBillList(@Parameter(hidden = true)LoginInfo loginInfo, @RequestBody PageQuery<ShipBillQueryDTO> pageQuery) {
        ShipBillQueryDTO queryDTO = pageQuery.getQueryDTO() == null ? new ShipBillQueryDTO() : pageQuery.getQueryDTO();
        queryDTO.setBuyerIdFrom(loginInfo.getMemberId());
        queryDTO.setQueryAppName(AppNames.WEB_SERVICE_BUYER.getCode());
        pageQuery.setQueryDTO(queryDTO);
        if (CsStringUtils.isBlank(queryDTO.getStatus())) {
            List<String> statusLists = this.getShipBillStatusList().getData();
            statusLists.remove("");
            queryDTO.setStatusList(statusLists);
        }
        //只有企业子账号受权限控制
        queryDTO.setRegionCodeList(null);
        queryDTO.setMdmCodeList(null);
        if (loginInfo.getAccountType() == AccountDTO.ACCOUNT_TYPE_MEMBER_SUB) {
            //设置买家行政区域权限
            if (CollectionUtils.isNotEmpty(loginInfo.getAccountRegionAdCodeList())) {
                log.info("queryShipBillList_regionCode:" + loginInfo.getAccountRegionAdCodeList());
                queryDTO.setRegionCodeList(loginInfo.getAccountRegionAdCodeList());
            }
            //设置ERP账户权限
            if (CollectionUtils.isNotEmpty(loginInfo.getErpAccountList())) {
                log.info("queryShipBillList_mdmCode:" + loginInfo.getErpAccountList());
                List<String> mdmCodeList = loginInfo.getErpAccountList().stream().map(ERPAccountInfo::getMdmCode)
                        .toList();
                queryDTO.setMdmCodeList(mdmCodeList);
            }
        }

        //护网行动
        if (CsStringUtils.isBlank(queryDTO.getBeginCreateTime()) || queryDTO.getBeginCreateTime().compareTo("2021-03-01") < 0) {
            queryDTO.setBeginCreateTime("2021-03-01");
        }

        return shipBillService.queryShipBillList(pageQuery);
    }

    @Operation(summary = "获取运单状态列表")
    @GetMapping(value = "/getShipBillStatusList")
    public ItemResult<List<String>> getShipBillStatusList() {
        List<String> statusList = new ArrayList<>();
        statusList.add("");
        statusList.add(ShipBillStatusEnum.WAIT_AUDIT.getCode());
        statusList.add(ShipBillStatusEnum.WAIT_RECEIVE.getCode());
        statusList.add(ShipBillStatusEnum.WAIT_ASSIGN.getCode());
        statusList.add(ShipBillStatusEnum.CANCELED.getCode());
        statusList.add(ShipBillStatusEnum.WAIT_CONFIRM.getCode());
        statusList.add(ShipBillStatusEnum.WAIT_DELIVERY.getCode());
        statusList.add(ShipBillStatusEnum.DELIVERING.getCode());
        statusList.add(ShipBillStatusEnum.SIGNED.getCode());
        statusList.add(ShipBillStatusEnum.COMPLETE.getCode());
        statusList.add(ShipBillStatusEnum.CLOSED.getCode());
        statusList.add(ShipBillStatusEnum.REFUND.getCode());
        return new ItemResult<>(statusList);
    }

    @Operation(summary = "取消运单")
    @PostMapping(value = "/cancelShipBill")
    public ItemResult<Void> cancelShipBill(@Parameter(hidden = true)LoginInfo loginInfo, @RequestBody CancelShipBillDTO cancelShipBillDTO) {
        cancelShipBillDTO.setOperateUserId(loginInfo.getAccountId());
        cancelShipBillDTO.setOperateUserName(loginInfo.getAccountName());
        return shipBillService.cancelShipBill(cancelShipBillDTO);
    }

    @Operation(summary = "取消运单的重新指派")
    @PostMapping(value = "/assignCanceledShipBill")
    public ItemResult<Void> assignCanceledShipBill(@Parameter(hidden = true)LoginInfo loginInfo, @RequestBody ReassignShipBillDTO reassignShipBillDTO) {
        reassignShipBillDTO.setOperateUserId(loginInfo.getAccountId());
        reassignShipBillDTO.setOperateUserName(loginInfo.getAccountName());
        return shipBillService.assignCanceledShipBill(reassignShipBillDTO);
    }

    @Operation(summary = "完成运单")
    @PostMapping(value = "/completeShipBill")
    public ItemResult<Void> completeShipBill(@Parameter(hidden = true)LoginInfo loginInfo, @RequestBody CompleteShipBillDTO completeShipBillDTO) {
        completeShipBillDTO.setOperationUserId(loginInfo.getAccountId());
        completeShipBillDTO.setOperationUserName(loginInfo.getAccountName());
        return shipBillService.completeShipBill(completeShipBillDTO);
    }

    @Operation(summary = "查询运单详情")
    @GetMapping("/queryTradeWaybillDetail")
    public ItemResult<TradeWaybillDetailDTO> queryTradeWaybillDetail(@Parameter(name = "waybillId", description = "运单id") @RequestParam String waybillId) {
        return shipBillService.queryTradeWaybillDetail(waybillId);
    }

    @Operation(summary = "下载运单列表")
    @PostMapping("/downloadWaybillList")
    public ItemResult<String> downloadWaybillList(@Parameter(hidden = true)LoginInfo loginInfo, @RequestBody ShipBillQueryDTO shipBillQueryDTO) {
        ReportShipBillQueryDTO reportWaybillQueryDTO = new ReportShipBillQueryDTO();
        BeanUtils.copyProperties(shipBillQueryDTO,reportWaybillQueryDTO);
        reportWaybillQueryDTO.setWaybillNum(shipBillQueryDTO.getWaybillBillNum());

        reportWaybillQueryDTO.setOperatorId(loginInfo.getAccountId());
        reportWaybillQueryDTO.setBuyerId(loginInfo.getMemberId());
        reportWaybillQueryDTO.setQueryAppName(AppNames.WEB_SERVICE_BUYER.getCode());
        if (CsStringUtils.isBlank(reportWaybillQueryDTO.getStatus())) {
            List<String> statusLists = this.getShipBillStatusList().getData();
            statusLists.remove("");
            reportWaybillQueryDTO.setStatusList(statusLists);
        }
        //只有企业子账号受权限控制
        reportWaybillQueryDTO.setRegionCodeList(null);
        reportWaybillQueryDTO.setMdmCodeList(null);
        if (loginInfo.getAccountType() == AccountDTO.ACCOUNT_TYPE_MEMBER_SUB) {
            //设置买家行政区域权限
            if (CollectionUtils.isNotEmpty(loginInfo.getAccountRegionAdCodeList())) {
                log.info("downloadSellerOrder_regionCode:" + loginInfo.getAccountRegionAdCodeList());
                reportWaybillQueryDTO.setRegionCodeList(loginInfo.getAccountRegionAdCodeList());
            }
            //设置ERP账户权限
            if (CollectionUtils.isNotEmpty(loginInfo.getErpAccountList())) {
                log.info("downloadSellerOrder_mdmCode:" + loginInfo.getErpAccountList());
                List<String> mdmCodeList = loginInfo.getErpAccountList().stream().map(ERPAccountInfo::getMdmCode)
                        .toList();
                reportWaybillQueryDTO.setMdmCodeList(mdmCodeList);
            }
        }

        //护网行动
        reportWaybillQueryDTO.setBeginCreateTime("2021-03-01");

        final ReportShipBillQueryDTO queryDTO = reportWaybillQueryDTO;

        String key = ReportRedisKeys.WAYBILL_EXPORT + loginInfo.getAccountId();
        if(redisService.hasKey(key)){
            redisService.del(key);
        }

        //运单导出操作改为异步
        CompletableFuture.runAsync(() -> {
            try {
                reportService.exportPublishedWaybillList(queryDTO);
            } catch (Exception e) {
                log.error("导出运单异常:{}", e);
            }
        });

        return new ItemResult<>("OK");
    }

    @Operation(summary = "轮询运单下载结果")
    @PostMapping("/getDownloadWaybill")
    public ItemResult<String> getDownloadWaybill(@Parameter(hidden = true)LoginInfo info, @RequestBody List<String> selectHeaders){
        //从redis中查询
        String key = ReportRedisKeys.WAYBILL_EXPORT + info.getAccountId();
        String result = "";
        if(redisService.hasKey(key)){
            result = redisService.get(key);
        }
        if (CsStringUtils.isBlank(result)) {
            //缓存中没有报表数据，返回前端0000，让前端继续轮询
            return new ItemResult<>("0000");
        }
        if (CsStringUtils.equals(result, "error")) {
            //查询报表数据时报错，返回前端0001，让前端停止轮询
            return new ItemResult<>("0001");
        }
        if (CsStringUtils.equals(result, "success")) {
            //查询报表数据成功，但上次轮询还未返回前端数据，返回0002让前端停止轮询
            return new ItemResult<>("0002");
        }
        redisService.set(key,"success");
        log.info("开始json转换=============");
        List<ReportWaybillResultDTO> resultDTOList = JSON.parseObject(result, new TypeReference<List<ReportWaybillResultDTO>>() {});
        log.info("json转换结束=============");
        ResCheckDTO resCheckDTO = new ResCheckDTO();
        resCheckDTO.setAccountId(info.getAccountId());
        resCheckDTO.setAppName(AppNames.WEB_SERVICE_BUYER.getPlatform());
        resCheckDTO.setResourceType(ResourceTypeEnum.BUTTON.getName());
        //订单列表价格显示的按钮
        resCheckDTO.setCode("3202B01-09");
        //有资源就要隐藏
        boolean needHidePrice = !roleService.checkRes(resCheckDTO);
        return new ItemResult<>(BuyerWaybillExcelUtil.htmlExcelFormat(resultDTOList, selectHeaders, needHidePrice));
    }

    @Operation(summary = "运单下载2")
    @PostMapping("/downloadWaybill")
    public ResponseEntity downloadWaybill(@Parameter(hidden = true)LoginInfo loginInfo, @RequestBody ShipBillQueryDTO shipBillQueryDTO){
        String key = AppNames.WEB_SERVICE_SELLER.getPlatform()+":downloadWaybill:"+loginInfo.getAccountId();
        try{
            if( redisService.hasKey(key)){
                throw new BizException(BasicCode.CUSTOM_ERROR,"已有运单下载任务请勿重复请求，或30秒后再试");
            }
            redisService.set(key,"1");
            redisService.setTimeout(key,6, TimeUnit.SECONDS);
            ResCheckDTO resCheckDTO = new ResCheckDTO();
            resCheckDTO.setAccountId(loginInfo.getAccountId());
            resCheckDTO.setAppName(AppNames.WEB_SERVICE_BUYER.getPlatform());
            resCheckDTO.setResourceType(ResourceTypeEnum.BUTTON.getName());
            //订单列表价格显示的按钮
            resCheckDTO.setCode("3202B01-09");
            //有资源就要隐藏
            boolean needHidePrice = !roleService.checkRes(resCheckDTO);
            ReportShipBillQueryDTO reportWaybillQueryDTO = new ReportShipBillQueryDTO();
            BeanUtils.copyProperties(shipBillQueryDTO,reportWaybillQueryDTO);
            reportWaybillQueryDTO.setNeedHidePrice(needHidePrice);
            reportWaybillQueryDTO.setQueryAppName(AppNames.WEB_SERVICE_BUYER.getCode());

            reportWaybillQueryDTO.setWaybillNum(shipBillQueryDTO.getWaybillBillNum());

            reportWaybillQueryDTO.setOperatorId(loginInfo.getAccountId());
            reportWaybillQueryDTO.setBuyerId(loginInfo.getMemberId());
            reportWaybillQueryDTO.setQueryAppName(AppNames.WEB_SERVICE_BUYER.getCode());
            if (CsStringUtils.isBlank(reportWaybillQueryDTO.getStatus())) {
                List<String> statusLists = this.getShipBillStatusList().getData();
                statusLists.remove("");
                reportWaybillQueryDTO.setStatusList(statusLists);
            }
            //只有企业子账号受权限控制
            reportWaybillQueryDTO.setRegionCodeList(null);
            reportWaybillQueryDTO.setMdmCodeList(null);
            if (loginInfo.getAccountType() == AccountDTO.ACCOUNT_TYPE_MEMBER_SUB) {
                //设置买家行政区域权限
                if (CollectionUtils.isNotEmpty(loginInfo.getAccountRegionAdCodeList())) {
                    log.info("downloadSellerOrder_regionCode:" + loginInfo.getAccountRegionAdCodeList());
                    reportWaybillQueryDTO.setRegionCodeList(loginInfo.getAccountRegionAdCodeList());
                }
                //设置ERP账户权限
                if (CollectionUtils.isNotEmpty(loginInfo.getErpAccountList())) {
                    log.info("downloadSellerOrder_mdmCode:" + loginInfo.getErpAccountList());
                    List<String> mdmCodeList = loginInfo.getErpAccountList().stream().map(ERPAccountInfo::getMdmCode)
                            .toList();
                    reportWaybillQueryDTO.setMdmCodeList(mdmCodeList);
                }
            }
            //护网行动
            reportWaybillQueryDTO.setBeginCreateTime("2021-03-01");

            Response response1 = reportService.exportPublishedWaybill(reportWaybillQueryDTO);
            Map<String, Collection<String>> headers = response1.headers();

            HttpHeaders httpHeaders = new HttpHeaders();
            if( headers!= null && !headers.isEmpty()) {
                headers.forEach((k, v) -> {
                    List<String> values = Lists.newLinkedList();
                    values.addAll(v);
                    httpHeaders.put(k, values);
                });
            }
            Response.Body body = response1.body();
            InputStream inputStream = body.asInputStream();
            InputStreamSource resource = new InputStreamResource(inputStream);
            return ResponseEntity
                    .ok()
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .headers(httpHeaders)
                    .body(resource);
        }catch (Exception e){
            log.error(e.getMessage(),e);
            throw new BizException(BasicCode.CUSTOM_ERROR,"运单下载出错");
        }finally {
            redisService.del(key);
        }
    }

    @Operation(summary = "获取报表字段")
    @GetMapping("/getReportHeaders")
    public ItemResult<List<String>> getReportHeaders() {
        return new ItemResult<>(BuyerWaybillExcelUtil.getHeaders());
    }


    @Operation(summary = "重新创建ERP运单")
    @PostMapping(value = "/againCreateExternalWaybill")
    public ItemResult<ShipBillListDTO> againCreateExternalWaybill(@Parameter(hidden = true)LoginInfo loginInfo,
                                                                  @RequestBody ReassignShipBillDTO reassignShipBillDTO) {
        log.info("web_againCreateExternalWaybill入参：{}", reassignShipBillDTO.getWaybillId());
        return shipBillService.againCreateExternalWaybill(reassignShipBillDTO.getWaybillId(), loginInfo.getAccountId(), loginInfo.getAccountName());
    }


    @Operation(summary = "关闭运单")
    @PostMapping(value = "/discardShipBill")
    public ItemResult<Void> discardShipBill(@Parameter(hidden = true)LoginInfo loginInfo, @RequestBody CloseShipBillDTO closeShipBillDTO) {
        closeShipBillDTO.setOperateUserId(loginInfo.getAccountId());
        closeShipBillDTO.setOperateUserName(loginInfo.getAccountName());
        return shipBillService.discardShipBill(closeShipBillDTO);
    }


    @Operation(summary = "获取商品种类下拉列表")
    @PostMapping(value = "/queryGoodsNameListDropDownBox")
    public ItemResult<List<GoodsInfoDTO>> queryGoodsNameListDropDownBox(@Parameter(hidden = true)LoginInfo loginInfo, @RequestBody QueryGoodsDTO queryGoodsDTO){
        queryGoodsDTO.setBuyerId(loginInfo.getMemberId());
        return shipBillService.queryGoodsNameListDropDownBox(queryGoodsDTO);
    }
    
    @Operation(summary = "通过车牌号、船名查询")
    @PostMapping(value="/queryVehicleOrShippingListByName")
    public ItemResult<List<String>> queryVehicleOrShippingListByName(@Parameter(hidden = true)LoginInfo loginInfo,@Parameter(name = "arg0", description = "船名/车牌号") @RequestParam String arg0)throws Exception{
    	ShipBillQueryDTO queryDTO = new ShipBillQueryDTO();
    	
        queryDTO.setBuyerId(loginInfo.getMemberId());
        queryDTO.setVehicleNum(arg0);
		return shipBillService.queryVehicleOrShippingListByName(queryDTO);
    }

    /**
     * 查询订单下待发货的二维码信息
     * @param orderCode
     * @return
     */
    @Operation(summary = "查询订单下待发货的二维码信息")
    @PostMapping(value="/queryWDQrCodeByOrderCode")
    public ItemResult<List<QrCodeDTO>> queryWDQrCodeByOrderCode(@RequestParam String orderCode) {
        return shipBillService.queryWDQrCodeByOrderCode(orderCode);
    }
    
}
