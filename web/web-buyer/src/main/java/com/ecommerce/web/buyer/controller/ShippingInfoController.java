package com.ecommerce.web.buyer.controller;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.shipping.AuthorizeSellerDTO;
import com.ecommerce.logistics.api.dto.shipping.BindCaptainDTO;
import com.ecommerce.logistics.api.dto.shipping.EditServerTypeDTO;
import com.ecommerce.logistics.api.dto.shipping.ShippingBatchDTO;
import com.ecommerce.logistics.api.dto.shipping.ShippingInfoDetailsDTO;
import com.ecommerce.logistics.api.dto.shipping.ShippingInfoListDTO;
import com.ecommerce.logistics.api.dto.shipping.ShippingInfoListQueryDTO;
import com.ecommerce.logistics.api.dto.shipping.ShippingInfoSaveDTO;
import com.ecommerce.logistics.api.enums.ShippingEnum;
import com.ecommerce.logistics.api.service.IShippingInfoService;
import com.ecommerce.member.api.service.IMemberService;
import com.ecommerce.member.api.session.LoginInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 船舶管理服务
 * <AUTHOR>
 */
@Slf4j
@Tag(name = "ShippingInfo", description = "船舶管理服务")
@RestController
@RequestMapping("/shippingInfo")
public class ShippingInfoController {

    @Autowired
    private IShippingInfoService shippingInfoService;

    @Autowired
    private IMemberService memberService;

    /**
     * 保存，不需要做数据必填校验
     */
    @Operation(summary = "保存，不需要做数据必填校验")
    @PostMapping(value="/saveShippingInfo")
    public ItemResult<String> saveShippingInfo(@Parameter(hidden = true)LoginInfo loginInfo, @RequestBody ShippingInfoSaveDTO dto) throws Exception{
        dto.setOperatorId(loginInfo.getAccountId());
        dto.setOperatorName(loginInfo.getAccountName());
        dto.setManagerMemberId(loginInfo.getMemberId());
        dto.setManagerMemberName(loginInfo.getMemberName());
        //默认为买家引入
        dto.setOwnership(ShippingEnum.OwnershipEnum.BUYER_IMPORT.getCode()+"");
        //经纪人类型,10-企业承运商，11-个体船东承运商，20-卖家，30-买家
        dto.setManagerMemberType("30");
        log.info("saveShippingInfo_web提交的参数：{}",dto);
        return shippingInfoService.saveShippingInfo(dto);
    }

    /**
     * 删除船舶
     */
    @Operation(summary = "删除船舶")
    @PostMapping(value="/delShippingInfo")
    public ItemResult<Void> delShippingInfo(@Parameter(hidden = true)LoginInfo loginInfo, @RequestBody @Valid ShippingBatchDTO dto) throws Exception{
        dto.setOperatorId(loginInfo.getAccountId());
        dto.setOperatorName(loginInfo.getAccountName());
        dto.setManagerMemberId(loginInfo.getMemberId());
        log.info("delShippingInfo_web提交的参数：{}",dto);
        return shippingInfoService.delShippingInfo(dto);
    }

    /**
     * 提交，需要做数据必填校验
     */
    @Operation(summary = "提交，需要做数据必填校验")
    @PostMapping(value="/submitShippingInfo")
    public ItemResult<Void> submitShippingInfo(@Parameter(hidden = true)LoginInfo loginInfo, @RequestBody @Valid ShippingInfoSaveDTO dto) {
        dto.setOperatorId(loginInfo.getAccountId());
        dto.setOperatorName(loginInfo.getAccountName());
        dto.setManagerMemberId(loginInfo.getMemberId());
        dto.setManagerMemberName(loginInfo.getMemberName());
        //默认为买家引入
        dto.setOwnership(ShippingEnum.OwnershipEnum.BUYER_IMPORT.getCode()+"");
        //经纪人类型,10-企业承运商，11-个体船东承运商，20-卖家，30-买家
        dto.setManagerMemberType("30");
        log.info("submitShippingInfo_web提交的参数：{}",dto);
        return shippingInfoService.submitShippingInfo(dto);
    }


    /**
     * 查询船舶列表
     */
    @Operation(summary = "查询船舶列表")
    @PostMapping(value="/queryShippingInfoList")
    public ItemResult<PageData<ShippingInfoListDTO>> queryShippingInfoList(@Parameter(hidden = true)LoginInfo loginInfo, @RequestBody PageQuery<ShippingInfoListQueryDTO> pageQuery) {
        if (pageQuery == null || pageQuery.getQueryDTO() == null) {
            pageQuery = new PageQuery<>();
            ShippingInfoListQueryDTO dto = new ShippingInfoListQueryDTO();
            dto.setMemberId(loginInfo.getMemberId());
            pageQuery.setQueryDTO(dto);
        } else {
            pageQuery.getQueryDTO().setMemberId(loginInfo.getMemberId());
        }
        //经纪人类型,10-企业承运商，11-个体船东承运商，20-卖家，30-买家
        pageQuery.getQueryDTO().setManagerMemberType("30");
        log.info("queryShippingInfoList_web提交的参数：{}",pageQuery);
        PageData<ShippingInfoListDTO> pageDataItemResult = shippingInfoService.queryShippingInfoListBySeller(pageQuery).getData();

        if (pageDataItemResult != null && CollectionUtils.isNotEmpty(pageDataItemResult.getList()) && pageQuery.getQueryDTO().getHasCaptain()) {
            pageDataItemResult.setList(pageDataItemResult.getList().stream().filter(shippingInfoListDTO -> CsStringUtils.isNotBlank(shippingInfoListDTO.getCaptainName())).toList());
        }
        return new ItemResult<>(pageDataItemResult);
    }

    /**
     * 查询船舶详情
     */
    @Operation(summary = "查询船舶详情")
    @PostMapping(value="/queryShippingInfoDetails")
    public ItemResult<ShippingInfoDetailsDTO> queryShippingInfoDetails(@Parameter(hidden = true)LoginInfo loginInfo, @RequestParam String shippingId) {
        return shippingInfoService.queryShippingInfoDetailsByBuyer(shippingId);
    }

    /**
     * 批量船舶检修
     */
    @Operation(summary = "批量船舶检修")
    @PostMapping(value="/shippingInfoOverhaul")
    public ItemResult<Void> shippingInfoOverhaul(@Parameter(hidden = true)LoginInfo loginInfo, @RequestBody @Valid ShippingBatchDTO dto) {
        dto.setOperatorId(loginInfo.getAccountId());
        dto.setOperatorName(loginInfo.getAccountName());
        log.info("shippingInfoOverhaul_web提交的参数：{}",dto);
        return shippingInfoService.shippingInfoOverhaul(dto);
    }

    /**
     * 绑定船长
     */
    @Operation(summary = "绑定船长")
    @PostMapping(value="/bindCaptain")
    public ItemResult<Void> bindCaptain(@Parameter(hidden = true)LoginInfo loginInfo, @RequestBody @Valid BindCaptainDTO dto) {
        dto.setOperatorId(loginInfo.getAccountId());
        dto.setOperatorName(loginInfo.getAccountName());
        log.info("bindCaptain_web提交的参数：{}",dto);
        return shippingInfoService.bindCaptain(dto);
    }


    /**
     * 船舶名称校验
     */
    @Operation(summary = "船舶名称校验")
    @PostMapping(value="/shippingNameValid")
    public ItemResult<Boolean> shippingNameValid(@RequestParam String shippingName) {
        return shippingInfoService.shippingNameValid(shippingName);
    }


    /**
     * 查询已经授权的卖家列表
     */
    @Operation(summary = "查询已经授权的卖家列表")
    @PostMapping(value="/getAuthorizeSellerList")
    public ItemResult<List<AuthorizeSellerDTO>> getAuthorizeSellerList(@Parameter(hidden = true)LoginInfo loginInfo, @RequestParam String shippingId) {
        log.info("getAuthorizeSellerList_web提交的参数：{}",shippingId);
        return shippingInfoService.getAuthorizeSellerList(shippingId);
    }

    /**
     * 批量恢复运输
     */
    @Operation(summary = "批量恢复运输")
    @PostMapping(value="/batchRecoveryTransport")
    public ItemResult<Void> batchRecoveryTransport(@Parameter(hidden = true)LoginInfo loginInfo, @RequestBody @Valid ShippingBatchDTO dto) {
        dto.setOperatorId(loginInfo.getAccountId());
        dto.setOperatorName(loginInfo.getAccountName());
        log.info("batchRecoveryTransport_web提交的参数：{}",dto);
        return shippingInfoService.batchRecoveryTransport(dto);
    }

    /**
     * 修改单个服务类型
     */
    @Operation(summary = "修改单个服务类型")
    @PostMapping(value="/updateServerType")
    public ItemResult<Void> updateServerType(@Parameter(hidden = true)LoginInfo loginInfo, @RequestBody @Valid EditServerTypeDTO dto) {
        dto.setOperatorId(loginInfo.getAccountId());
        dto.setOperatorName(loginInfo.getAccountName());
        dto.setAuthorizeUserId(loginInfo.getMemberId());
        log.info("updateServerType_web提交的参数：{}",dto);
        return shippingInfoService.updateServerType(dto);
    }

    /**
     * 批量修改服务类型
     */
    @Operation(summary = "批量修改服务类型")
    @PostMapping(value="/batchFixedTemporary")
    public ItemResult<Void> batchFixedTemporary(@Parameter(hidden = true)LoginInfo loginInfo, @RequestBody @Valid ShippingBatchDTO dto) {
        dto.setOperatorId(loginInfo.getAccountId());
        dto.setOperatorName(loginInfo.getAccountName());
        dto.setAuthorizeUserId(loginInfo.getMemberId());
        log.info("batchFixedTemporary_web提交的参数：{}",dto);
        return shippingInfoService.batchFixedTemporary(dto);
    }
}
