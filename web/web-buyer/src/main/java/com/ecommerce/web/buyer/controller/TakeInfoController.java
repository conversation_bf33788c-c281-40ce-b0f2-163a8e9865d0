package com.ecommerce.web.buyer.controller;

import com.alibaba.fastjson.JSON;
import com.ecommerce.base.api.dto.ReceivingAddressDTO;
import com.ecommerce.base.api.dto.warehouse.WarehouseDetailsDTO;
import com.ecommerce.base.api.service.IReceivingAddressService;
import com.ecommerce.base.api.service.IWarehouseService;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.utils.ArithUtils;
import com.ecommerce.common.utils.BeanConvertUtils;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.goods.api.dto.GoodsDTO;
import com.ecommerce.goods.api.dto.UnitConverDTO;
import com.ecommerce.goods.api.service.IGoodsService;
import com.ecommerce.logistics.api.dto.vehicle.VehicleBaseDataDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleDefaultCondDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleDefaultResDTO;
import com.ecommerce.logistics.api.dto.waybill.TradeWaybillDTO;
import com.ecommerce.logistics.api.enums.ShipBillItemStatusEnum;
import com.ecommerce.logistics.api.enums.TransportToolTypeEnum;
import com.ecommerce.logistics.api.enums.UserRoleEnum;
import com.ecommerce.logistics.api.service.IShipBillService;
import com.ecommerce.logistics.api.service.IVehicleService;
import com.ecommerce.logistics.api.service.IWaybillService;
import com.ecommerce.member.api.session.LoginInfo;
import com.ecommerce.order.api.dto.MonitorJudgeCondDTO;
import com.ecommerce.order.api.dto.MonitorJudgeResDTO;
import com.ecommerce.order.api.dto.OrderDTO;
import com.ecommerce.order.api.dto.OrderItemDTO;
import com.ecommerce.order.api.dto.TakeInfoDTO;
import com.ecommerce.order.api.dto.TakeItemDTO;
import com.ecommerce.order.api.dto.take.NeedBuyerPayDTO;
import com.ecommerce.order.api.enums.TakeStatus;
import com.ecommerce.order.api.service.IMonitorJudgeService;
import com.ecommerce.order.api.service.IOrderService;
import com.ecommerce.order.api.service.ITakeInfoService;
import com.ecommerce.web.buyer.utils.CommonUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Slf4j
@Tag(name = "TakeInfoController", description = "发货单服务")
@RestController
@RequestMapping("/takeinfo")
public class TakeInfoController {

    @Autowired
    private ITakeInfoService iTakeInfoService;

    @Autowired
    private IWaybillService iWaybillService;

    @Autowired
    private IOrderService orderService;

    @Autowired
    private IWarehouseService warehouseService;

    @Autowired
    private IGoodsService iGoodsService;

    @Autowired
    private IVehicleService vehicleService;

    @Autowired
    private IReceivingAddressService receivingAddressService;

    @Autowired
    private IMonitorJudgeService iMonitorJudgeService;

    @Autowired
    private IShipBillService shipBillService;

    @Operation(summary = "买家-取消发货单")
    @PostMapping(value = "/doBuyerCancel")
    public ItemResult<Boolean> doBuyerCancel(@Parameter(hidden = true)LoginInfo loginInfo,
                                             @Parameter(name = "takeId", description = "发货单id") @RequestParam String takeId) {
        if (loginInfo == null) {
            return new ItemResult<>(false);
        }
        iTakeInfoService.doBuyerCancelTakeInfo(takeId, loginInfo.getMemberId(), loginInfo.getAccountId());
        return new ItemResult<>(true);
    }

    @Operation(summary = "买家创建发货单")
    @PostMapping(value = "/doBuyerCreateTakeInfo")
    public ItemResult<Boolean> doBuyerCreateTakeInfo(@Parameter(hidden = true)LoginInfo loginInfo,
                                                     @Parameter(name = "takeInfoDTOs", description = "发货单对象列表") @Valid @RequestBody List<TakeInfoDTO> takeInfoDTOs) {
        log.info("doBuyerCreateTakeInfo:" + takeInfoDTOs.toString());
        if (loginInfo == null) {
            return new ItemResult<>(false);
        }
        ItemResult<Boolean> result = new ItemResult<>(true);
        List<TakeInfoDTO> takeInfoDTOS = iTakeInfoService.doBuyerCreateTakeInfo(takeInfoDTOs, loginInfo.getMemberId(), loginInfo.getAccountId());
        if (CollectionUtils.isNotEmpty(takeInfoDTOS)) {
            log.info("takeInfoDTOS.size : {}", takeInfoDTOS.size());
            takeInfoDTOS.stream().findFirst().ifPresent(takeInfoDTO -> result.setDescription(takeInfoDTO.getTakeId()));
        }
        return result;
    }


    @Operation(summary = "买家创建发货单获取基本信息")
    @PostMapping(value = "/getTakeInfoWhenBuyerCreate")
    public ItemResult<TakeInfoDTO> getTakeInfoWhenBuyerCreate(@Parameter(hidden = true)LoginInfo loginInfo,
                                                              @Parameter(name = "orderId", description = "订单id") @RequestParam String orderId) {
        if (loginInfo == null) {
            return new ItemResult<>(null);
        }
        return new ItemResult<>(iTakeInfoService.getTakeInfoForCreate(orderId, loginInfo.getMemberId()));
    }

    @Operation(summary = "校验支付单是否需要支付,如果有返回最新的支付单金额,按最新金额支付")
    @PostMapping(value = "/needBuyerPay")
    public ItemResult<NeedBuyerPayDTO> needBuyerPay(@Parameter(hidden = true)LoginInfo loginInfo, @RequestBody NeedBuyerPayDTO needBuyerPayDTO) {
        if (CsStringUtils.isBlank(needBuyerPayDTO.getOrderId()) || CsStringUtils.isBlank(needBuyerPayDTO.getTakeId())) {
            throw new BizException(BasicCode.CUSTOM_ERROR,"入参不正确");
        }
        needBuyerPayDTO.setOperatorId(loginInfo.getAccountId());
        return new ItemResult<>(iTakeInfoService.needBuyerPay(needBuyerPayDTO));
    }

    @Operation(summary = "买家-删除发货单")
    @PostMapping(value = "/doBuyerDeleteTakeInfo")
    public ItemResult<Boolean> doBuyerDeleteTakeInfo(@Parameter(hidden = true)LoginInfo loginInfo,
                                                     @Parameter(name = "takeId", description = "发货单id") @RequestParam String takeId) {
        if (loginInfo == null) {
            return new ItemResult<>(false);
        }
        iTakeInfoService.doBuyerDeleteTakeInfo(takeId, loginInfo.getAccountId());
        return new ItemResult<>(true);
    }

    @Operation(summary = "买家-获取发货单详情")
    @PostMapping(value = "/getBuyerTakeInfoDetail")
    public ItemResult<TakeInfoDTO> getBuyerTakeInfoDetail(@Parameter(hidden = true)LoginInfo loginInfo,
                                                          @Parameter(name = "takeId", description = "发货单id") @RequestParam String takeId) {
        if (loginInfo == null) {
            return new ItemResult<>(null);
        }
        TakeInfoDTO takeInfo = iTakeInfoService.getBuyerTakeInfoDetail(takeId, loginInfo.getMemberId(), loginInfo.getAccountId());
        if (takeInfo == null) {
            return null;
        }
        ReceivingAddressDTO address = receivingAddressService.findById(takeInfo.getAddressId());
        if (address != null) {
            takeInfo.setReceiverName(address.getConsigneeName());
        }
        String orderId = takeInfo.getOrderId();

        OrderDTO orderDTO = this.orderService.getOrderAndItemInfo(orderId).getData();

        takeInfo.getTakeItems().forEach(item -> {
            orderDTO.getOrderItems().forEach(orderItem -> {
                if (item.getOrderItemId().equals(orderItem.getOrderItemId())) {
                    item.setCanRequestQuantity(orderItem.getItemcantakeQuantity());
                    //如果是，则加上润管砂浆的量
                    item.setOrderItemQuantity(ArithUtils.add(orderItem.getItemQuantity(),orderDTO.getOrderInfoExtDTO().getLubricityQuantity()));
                    if (CsStringUtils.isNullOrBlank(item.getResourceName())) {
                        item.setResourceName(orderItem.getGoodsName());
                    }
                }
            });
        });

        if (!CsStringUtils.isEmpty(takeInfo.getStoreId())) {
            WarehouseDetailsDTO store = warehouseService.queryWarehouseDetails(takeInfo.getStoreId());
            if (store != null) {
                StringBuilder builder = new StringBuilder();
                builder.append(store.getProvince()).append(" ").append(store.getCity()).append(" ")
                        .append(store.getDistrict()).append(" ").append(" ").append(store.getAddress()).append(" ")
                        .append(store.getName()).append(" ");
                takeInfo.setStoreAddressShow(builder.toString());
            }
        }
        return new ItemResult<>(takeInfo);
    }

    @Operation(summary = "买家-订单详情-物流详情")
    @PostMapping(value = "/getBuyerOrderLogisticDetails")
    public ItemResult<Map<String, Object>> getBuyerOrderLogisticDetails(@Parameter(hidden = true)LoginInfo loginInfo,
                                                                        @Parameter(name = "orderId", description = "订单id") @RequestParam String orderId) {
        if (loginInfo == null) {
            return new ItemResult<>(null);
        }
        Map<String, List<UnitConverDTO>> goodsUnits = new HashMap<>();
        Map<String, String> itemUnits = new HashMap<>();
        log.info("入参orderId:" + orderId);
        log.info("入参loginInfo:" + loginInfo);
        List<TakeInfoDTO> takeInfoDTOs = iTakeInfoService.getBuyerOrderTakeInfo(orderId, loginInfo.getAccountId());

        Map<String, Object> resultMap = new HashMap<>();
        OrderDTO orderDTO = this.orderService.getOrderAndItemInfo(orderId).getData();
        if (takeInfoDTOs != null && takeInfoDTOs.size() > 0) {
            for (TakeInfoDTO takeInfoDTO : takeInfoDTOs) {
                for (TakeItemDTO takeItem : takeInfoDTO.getTakeItems()) {
                    if (!goodsUnits.containsKey(takeItem.getResourceId())) {
                        List<UnitConverDTO> result = iGoodsService.getUnitConverInfo(takeItem.getGoodsId()).getData();
                        goodsUnits.put(takeItem.getResourceId(), result);
                        goodsUnits.put(takeItem.getOrderItemId(), result);
                    }
                    if (!itemUnits.containsKey(takeItem.getResourceId())) {
                        itemUnits.put(takeItem.getResourceId(), takeItem.getUnits());
                        itemUnits.put(takeItem.getOrderItemId(), takeItem.getUnits());
                    }
                }
            }
        }
        if (orderDTO != null) {
            String storeId = null;
            TakeInfoDTO baseTakeInfoDTO = new TakeInfoDTO();
            BeanConvertUtils.copyProperties(orderDTO, baseTakeInfoDTO);
            for (OrderItemDTO orderItem : orderDTO.getOrderItems()) {
                if (CsStringUtils.isNotEmpty(orderItem.getStoreId())) {
                    storeId = orderItem.getStoreId();
                    break;
                }
            }

            if (!CsStringUtils.isEmpty(storeId)) {
                WarehouseDetailsDTO store = warehouseService.queryWarehouseDetails(storeId);
                if (store != null) {
                    StringBuilder builder = new StringBuilder();
                    builder.append(store.getProvince()).append(" ").append(store.getCity()).append(" ")
                            .append(store.getDistrict()).append(" ").append(" ").append(store.getAddress()).append(" ")
                            .append(store.getName()).append(" ");
                    baseTakeInfoDTO.setStoreAddressShow(builder.toString());
                }
            }
            resultMap.put("baseTakeInfo", baseTakeInfoDTO);
        }

        log.info("getBuyerOrderLogisticDetails_takeInfoDTOs:" + JSON.toJSONString(takeInfoDTOs));
        if (takeInfoDTOs != null && !takeInfoDTOs.isEmpty()) {

            takeInfoDTOs.stream().forEach(takeInfo -> {
                ItemResult<List<TradeWaybillDTO>> tradeWaybillDTOs = shipBillService.queryWaybillListByDeliveryNum(takeInfo.getTakeCode());
                log.info("tradeWaybillDTOs : {}", JSON.toJSONString(tradeWaybillDTOs));
                if (tradeWaybillDTOs != null && tradeWaybillDTOs.getData() != null) {
                    //bugfix 历史原因tradeWaybill中的resourceId可能是resourceId也可能是orderitemId，这里统一改为orderItemId
                    for (TradeWaybillDTO tradeWaybillDTO : tradeWaybillDTOs.getData()) {
                        for (TakeItemDTO takeItemDTO : takeInfo.getTakeItems()) {
                            if (takeItemDTO.getResourceId().equals(tradeWaybillDTO.getResourceId())) {
                                tradeWaybillDTO.setResourceId(takeItemDTO.getOrderItemId());
                                break;
                            }
                        }
                    }
                    takeInfo.setTradeWaybillDTOs(convertTradeWaybill(tradeWaybillDTOs.getData()));
                    takeInfo.getTradeWaybillDTOs().forEach(takeBill -> {
                        // 运单状态
                        ShipBillItemStatusEnum shipBillItemStatusEnum = ShipBillItemStatusEnum.valueOfCode(takeBill.getItemStatus());
                        if (shipBillItemStatusEnum != null) {
                            takeBill.setWaybillStatus(shipBillItemStatusEnum.getDesc());
                        }

                        List<UnitConverDTO> result = goodsUnits.get(takeBill.getResourceId());
                        log.info("===unitConverDTO===->{}", JSON.toJSONString(result));
                        String usedUnit = itemUnits.get(takeBill.getResourceId());
                        if (result == null || result.isEmpty()) {
                            log.info(" 无法获取单位转换信息 {}", takeBill);
                            return;
                        }
                        UnitConverDTO unitConverDTO = result.get(0);
                        if ((result.size() == 1)
                                || ((unitConverDTO.isDefault() && (usedUnit.equals(unitConverDTO.getUnitId1())
                                || usedUnit.equals(unitConverDTO.getUnit1())))
                                || (usedUnit.equals(unitConverDTO.getUnitId2())
                                || usedUnit.equals(unitConverDTO.getUnit2())))) {
                            return;
                        } else {
                            if (!unitConverDTO.isDefault()) {
                                takeBill.setShippingQuantity((new BigDecimal(takeBill.getShippingQuantity())
                                        .divide(unitConverDTO.getRatio(), 2, RoundingMode.HALF_UP).toString()));
                                takeBill.setActualShippingQuantity((new BigDecimal(takeBill.getActualShippingQuantity())
                                        .divide(unitConverDTO.getRatio(), 2, RoundingMode.HALF_UP).toString()));
                                if (CsStringUtils.isNotEmpty(takeBill.getSignQuantity()) && !CsStringUtils.equals(takeBill.getSignQuantity(), "-")) {
                                    takeBill.setSignQuantity((new BigDecimal(takeBill.getSignQuantity())
                                            .divide(unitConverDTO.getRatio(), 2, RoundingMode.HALF_UP).toString()));
                                }
                            } else {
                                takeBill.setShippingQuantity((new BigDecimal(takeBill.getShippingQuantity())
                                        .multiply(unitConverDTO.getRatio())).setScale(2, RoundingMode.HALF_UP).toString());
                                takeBill.setActualShippingQuantity((new BigDecimal(takeBill.getActualShippingQuantity())
                                        .multiply(unitConverDTO.getRatio())).setScale(2, RoundingMode.HALF_UP).toString());
                                if (CsStringUtils.isNotEmpty(takeBill.getSignQuantity()) && !CsStringUtils.equals(takeBill.getSignQuantity(), "-")) {
                                    takeBill.setSignQuantity((new BigDecimal(takeBill.getSignQuantity())
                                            .multiply(unitConverDTO.getRatio())).setScale(2, RoundingMode.HALF_UP).toString());
                                }
                            }
                        }
                    });
                } else {
                    log.info(" 未找到运单信息 {}", takeInfo);
                }

                takeInfo.setStatusName(TakeStatus.getByCode(takeInfo.getTakeStatus()).getMessage());
            });
            resultMap.put("takeInfos", takeInfoDTOs);
        }

        return new ItemResult<>(resultMap);
    }

    private List<com.ecommerce.order.api.dto.logistics.TradeWaybillDTO> convertTradeWaybill(List<TradeWaybillDTO> tradeWaybillDTOS) {
        if (tradeWaybillDTOS == null) {
            return null;
        }
        List<com.ecommerce.order.api.dto.logistics.TradeWaybillDTO> tradeWaybillList = new ArrayList<>();
        for (TradeWaybillDTO tradeWaybillDTO : tradeWaybillDTOS) {
            com.ecommerce.order.api.dto.logistics.TradeWaybillDTO tradeWaybill = new com.ecommerce.order.api.dto.logistics.TradeWaybillDTO();
            BeanUtils.copyProperties(tradeWaybillDTO, tradeWaybill);
            // 船运 设置船舶名称为车牌号
            if (CsStringUtils.equals(TransportToolTypeEnum.WATER_TRANSPORT.getCode(), tradeWaybillDTO.getTransportToolType())) {
                tradeWaybill.setVehicleNum(tradeWaybillDTO.getShippingName());
            }
            tradeWaybillList.add(tradeWaybill);
        }
        return tradeWaybillList;
    }

    @Operation(summary = "买家-获取发货单关联买家车辆信息")
    @PostMapping(value = "/getBuyerCars")
    public ItemResult<TakeInfoDTO> getBuyerCars(@Parameter(hidden = true)LoginInfo loginInfo,
                                                @Parameter(name = "takeCode", description = "发货单编号") @RequestParam String takeCode) {
        ItemResult<TakeInfoDTO> result = new ItemResult<>();
        result.setSuccess(false);
        if (loginInfo == null) {

            result.setDescription("请登陆");
            return result;
        }

        TakeInfoDTO takeInfo = iTakeInfoService.getTakeInfoByCode(takeCode);
        String buyerId = loginInfo.getMemberId();

        if (takeInfo == null || takeInfo.getBuyerId() == null || !takeInfo.getBuyerId().equals(buyerId)) {
            result.setDescription("请求有误");
            return result;
        }


        List<TakeItemDTO> takeItems = takeInfo.getTakeItems();
        if (CollectionUtils.isNotEmpty(takeItems)) {
            List<String> goodsIdList = takeItems.stream().map(TakeItemDTO::getGoodsId).filter(CsStringUtils::isNotBlank).distinct().toList();
            ItemResult<List<GoodsDTO>> goodsSimpleResult = iGoodsService.selectSimpleGoodsInfoByIds(goodsIdList);
            if (goodsSimpleResult != null && CollectionUtils.isNotEmpty(goodsSimpleResult.getData())) {
                List<String> transportCategoryIdList = goodsSimpleResult.getData().stream().map(GoodsDTO::getLogistics).filter(CsStringUtils::isNotBlank).distinct().toList();
                ItemResult<List<VehicleBaseDataDTO>> buyerCars = vehicleService.queryVehicleBaseData(takeInfo.getBuyerId());
                if (buyerCars != null && buyerCars.getData() != null && !buyerCars.getData().isEmpty()) {
                    List<VehicleBaseDataDTO> vehicleBaseDataDTOS = buyerCars.getData();
                    if (CollectionUtils.isNotEmpty(transportCategoryIdList)) {
                        vehicleBaseDataDTOS.removeIf(
                                vehicle ->
                                        CollectionUtils.isEmpty(vehicle.getTransportCategoryIdList()) ||
                                        !vehicle.getTransportCategoryIdList().containsAll(transportCategoryIdList) ||
                                        !CommonUtils.isSame(vehicle.getUserType(), UserRoleEnum.BUYER.getCode()) ||
                                        CommonUtils.isSame(vehicle.getDisableFlg(), 1)
                        );
                    }
                    //含管控商品运单，排除不能收到GPS信号的车辆
                    Integer ecFlowControlCount = (int)takeItems.stream().filter(TakeItemDTO::getNeedECMonitor).count();
                    if(ecFlowControlCount > 0){
                        vehicleBaseDataDTOS.removeIf(vehicle -> !integerEqual(vehicle.getIsGpsDevice(), 1) || !integerEqual(vehicle.getSignalFlag(), 1));
                    }
                    takeInfo.setBuyerCars(convertVehicleBaseData(vehicleBaseDataDTOS));
                }
            }
        }
        return new ItemResult<>(takeInfo);
    }

    @Operation(summary = "获取创建自提发货单时默认的车辆信息")
    @GetMapping(value = "/getDefaultBuyerCars")
    public ItemResult<VehicleDefaultResDTO> getDefaultBuyerCars(@RequestParam String orderId) {
        MonitorJudgeCondDTO condDTO = new MonitorJudgeCondDTO();
        condDTO.setOrderId(orderId);
        condDTO.setQueryTransportCategory(1);
        ItemResult<MonitorJudgeResDTO> monitorJudgeResDTOItemResult = iMonitorJudgeService.monitorJudge(condDTO);
        if (monitorJudgeResDTOItemResult == null || monitorJudgeResDTOItemResult.getData() == null) {
            throw new BizException(BasicCode.UNKNOWN_ERROR);
        }
        MonitorJudgeResDTO monitorJudgeResDTO = monitorJudgeResDTOItemResult.getData();
        VehicleDefaultCondDTO vehicleDefaultCondDTO = new VehicleDefaultCondDTO();
        if (CommonUtils.isSame(monitorJudgeResDTO.getNeedMonitor(), 1)) {
            vehicleDefaultCondDTO.setIsGpsDevice(1);
            vehicleDefaultCondDTO.setSignalFlag(1);
            vehicleDefaultCondDTO.setMustDriverId(1);
        }
        vehicleDefaultCondDTO.setTransportCategoryIdList(monitorJudgeResDTO.getTransportCategoryList());
        vehicleDefaultCondDTO.setUserId(monitorJudgeResDTO.getBuyerId());
        vehicleDefaultCondDTO.setUserType(UserRoleEnum.BUYER.getCode());
        vehicleDefaultCondDTO.setMinVersion(1L);

        return vehicleService.findDefaultAssignVehicle(vehicleDefaultCondDTO);
    }

    private List<com.ecommerce.order.api.dto.logistics.VehicleBaseDataDTO> convertVehicleBaseData(List<VehicleBaseDataDTO> vehicleBaseDataDTOS){
        if(vehicleBaseDataDTOS == null){
            return null;
        }
        List<com.ecommerce.order.api.dto.logistics.VehicleBaseDataDTO> vehicleBaseDataList = new ArrayList<>();
        for(VehicleBaseDataDTO vehicleBaseDataDTO : vehicleBaseDataDTOS){
            com.ecommerce.order.api.dto.logistics.VehicleBaseDataDTO vehicleBaseData = new com.ecommerce.order.api.dto.logistics.VehicleBaseDataDTO();
            BeanUtils.copyProperties(vehicleBaseDataDTO,vehicleBaseData);
            vehicleBaseDataList.add(vehicleBaseData);
        }
        return vehicleBaseDataList;
    }

    private boolean integerEqual(Integer one, Integer two) {
        if (one == null || two == null) {
            return one == null && two ==null;
        }
        return one.intValue() == two.intValue();
    }
}
