package com.ecommerce.web.buyer.controller;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.waybill.WaybillStatusOption;
import com.ecommerce.logistics.api.service.IWaybillService;
import com.ecommerce.member.api.session.LoginInfo;
import com.ecommerce.trace.api.dto.vehicle.VehicleLocationDTO;
import com.ecommerce.trace.api.dto.vehicle.VehicleLocationQueryDTO;
import com.ecommerce.trace.api.service.ITracePositionService;
import com.google.common.collect.Lists;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * @Created锛�Wed Sep 12 10:46:09 CST 2018
 * <AUTHOR>
 * @Version:2
 * @Description:: ITracePositionService
 */

@RestController
@Tag(name = "TracePositionController", description = "位置信息处理服务")
@RequestMapping("/tracePosition")
public class TracePositionController {

    @Autowired
    private ITracePositionService iTracePositionService;

    @Autowired
    private IWaybillService lgsWaybillService;

    @PostMapping(value = "/getVehicleLocation")
    public ItemResult<VehicleLocationDTO> getVehicleLocation(LoginInfo loginInfo,
                                                             @Parameter(name = "waybillNum", description = "运单编号") @RequestParam String waybillNum) {
        VehicleLocationQueryDTO vehicleLocationQueryDTO = new VehicleLocationQueryDTO();
        vehicleLocationQueryDTO.setBuyerId(loginInfo.getMemberId());
        vehicleLocationQueryDTO.setWaybillNumList(Lists.newArrayList(waybillNum));
        ItemResult<PageData<VehicleLocationDTO>> result = iTracePositionService.getVehicleLocation(vehicleLocationQueryDTO);
        if (result.isSuccess() && result.getData() != null) {
            List<VehicleLocationDTO> list = result.getData().getList();
            VehicleLocationDTO locationDTO = list.get(0);
            ItemResult<List<WaybillStatusOption>> statusResult = lgsWaybillService.queryWaybillStatusByNum(Lists.newArrayList(waybillNum));
            if (statusResult != null && CollectionUtils.isNotEmpty(statusResult.getData())) {
                locationDTO.setStatus(statusResult.getData().get(0).getStatus());
            }
            return new ItemResult<>(locationDTO);
        }
        return new ItemResult<>(null);
    }
}
