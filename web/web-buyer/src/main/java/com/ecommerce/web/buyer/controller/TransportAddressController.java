package com.ecommerce.web.buyer.controller;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.transportaddress.TransportAddressDeleteDTO;
import com.ecommerce.logistics.api.dto.transportaddress.TransportAddressDetailDTO;
import com.ecommerce.logistics.api.dto.transportaddress.TransportAddressListDTO;
import com.ecommerce.logistics.api.dto.transportaddress.TransportAddressQueryDTO;
import com.ecommerce.logistics.api.dto.transportaddress.TransportAddressSaveDTO;
import com.ecommerce.logistics.api.dto.transportaddress.TransportAddressSearchDTO;
import com.ecommerce.logistics.api.enums.UserRoleEnum;
import com.ecommerce.logistics.api.service.ITransportAddressService;
import com.ecommerce.member.api.session.LoginInfo;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * <AUTHOR>
 * Description:
 * Date: Create in 下午5:26 18/12/21
 */
@RestController
@Tag(name = "TransportAddressController", description = "运输地址")
@RequestMapping("/transportAddress")
public class TransportAddressController {

    @Autowired
    private ITransportAddressService transportAddressService;

    @PostMapping(value = "/addTransportAddress")
    public ItemResult<String> addTransportAddress(@Parameter(hidden = true)LoginInfo loginInfo,
                                                  @Parameter(name = "transportAddressSaveDTO", description = "运输地址保存DTO对象") @RequestBody TransportAddressSaveDTO transportAddressSaveDTO) {
        transportAddressSaveDTO.setUserId(loginInfo.getMemberId());
        transportAddressSaveDTO.setUserName(loginInfo.getMemberName());
        transportAddressSaveDTO.setUserType(UserRoleEnum.BUYER.getCode());
        transportAddressSaveDTO.setOperatorUserId(loginInfo.getAccountId());
        transportAddressSaveDTO.setOperatorUserName(loginInfo.getAccountName());

        return transportAddressService.addTransportAddress(transportAddressSaveDTO);
    }

    @PostMapping(value = "/deleteTransportAddress")
    public ItemResult<Void> deleteTransportAddress(@Parameter(hidden = true)LoginInfo loginInfo,
                                                   @Parameter(name = "transportAddressDeleteDTO", description = "运输地址删除DTO对象") @RequestBody TransportAddressDeleteDTO transportAddressDeleteDTO) {
        transportAddressDeleteDTO.setOperatorUserId(loginInfo.getAccountId());
        transportAddressDeleteDTO.setOperatorUserName(loginInfo.getAccountName());

        return transportAddressService.deleteTransportAddress(transportAddressDeleteDTO);
    }

    @PostMapping(value = "/editTransportAddress")
    public ItemResult<Void> editTransportAddress(@Parameter(hidden = true)LoginInfo loginInfo,
                                                 @Parameter(name = "transportAddressSaveDTO", description = "运输地址保存DTO对象") @RequestBody TransportAddressSaveDTO transportAddressSaveDTO) {
        transportAddressSaveDTO.setOperatorUserId(loginInfo.getAccountId());
        transportAddressSaveDTO.setOperatorUserName(loginInfo.getAccountName());
        return transportAddressService.editTransportAddress(transportAddressSaveDTO);
    }

    @PostMapping(value = "/queryTransportAddressDetail")
    public ItemResult<TransportAddressDetailDTO> queryTransportAddressDetail(@Parameter(name = "transportAddressId", description = "运输地址ID") @RequestParam String transportAddressId) {
        return transportAddressService.queryTransportAddressDetail(transportAddressId);
    }

    @PostMapping(value = "/queryTransportAddressList")
    public ItemResult<PageData<TransportAddressListDTO>> queryTransportAddressList(@Parameter(hidden = true)LoginInfo loginInfo,
                                                                                   @Parameter(name = "pageQuery", description = "运输地址查询DTO对象分页查询对象") @RequestBody PageQuery<TransportAddressQueryDTO> pageQuery) {
        if (pageQuery.getQueryDTO() == null) {
            pageQuery.setQueryDTO(new TransportAddressQueryDTO());
        }
        pageQuery.getQueryDTO().setUserId(loginInfo.getMemberId());
        pageQuery.getQueryDTO().setUserType(UserRoleEnum.BUYER.getCode());
        return transportAddressService.queryTransportAddressList(pageQuery);
    }

    @PostMapping(value = "/searchTransportAddressList")
    public ItemResult<List<TransportAddressListDTO>> searchTransportAddressList(@Parameter(hidden = true)LoginInfo loginInfo,
                                                                                @Parameter(name = "transportAddressSearchDTO", description = "运输地址搜索DTO对象") @RequestBody TransportAddressSearchDTO transportAddressSearchDTO) {
        transportAddressSearchDTO.setUserId(loginInfo.getMemberId());
        transportAddressSearchDTO.setUserType(UserRoleEnum.BUYER.getCode());
        return transportAddressService.searchTransportAddressList(transportAddressSearchDTO);
    }
}
