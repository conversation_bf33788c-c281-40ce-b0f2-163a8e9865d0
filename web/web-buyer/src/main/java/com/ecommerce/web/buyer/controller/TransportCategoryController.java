package com.ecommerce.web.buyer.controller;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.transportcategory.*;
import com.ecommerce.logistics.api.service.ITransportCategoryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <AUTHOR>
 * @Date: 13/09/2018 12:24
 * @Description:
 */
@RestController
@Tag(name = "TransportCategoryController", description = "运输品类")
@RequestMapping("/transportCategory")
public class TransportCategoryController {

    @Autowired
    private ITransportCategoryService iTransportCategoryService;

    @Operation(summary = "新增运输品类")
    @PostMapping(value = "/addTransportCategory")
    public ItemResult<Void> addTransportCategory(@Parameter(name = "arg0", description = "TransportCategoryAddDTO - 运输品类新增DTO对象") @RequestBody TransportCategoryAddDTO arg0) {
        return iTransportCategoryService.addTransportCategory(arg0);
    }

    @Operation(summary = "null")
    @PostMapping(value = "/queryTransportCategory")
    public ItemResult<TransportCategoryDetailDTO> queryTransportCategory(@Parameter(name = "arg0", description = "运输品类Id") @RequestParam String arg0) {
        return iTransportCategoryService.queryTransportCategory(arg0);
    }

    @Operation(summary = "修改运输品类")
    @PostMapping(value = "/modifyTransportCategory")
    public ItemResult<Void> modifyTransportCategory(@Parameter(name = "arg0", description = "TransportCategoryEditDTO - 运输品类编辑DTO对象") @RequestBody TransportCategoryEditDTO arg0) {
        return iTransportCategoryService.modifyTransportCategory(arg0);
    }

    @Operation(summary = "获取运输品类列表")
    @PostMapping(value = "/queryTransportCategoryList")
    public ItemResult<PageData<TransportCategoryListDTO>> queryTransportCategoryList(@Parameter(name = "arg0", description = "PageQuery<TransportCategoryListQueryDTO> - 运输品类列表查询DTO对象分页查询对象") @RequestBody PageQuery<TransportCategoryListQueryDTO> arg0) {
        return iTransportCategoryService.queryTransportCategoryList(arg0);
    }

    @Operation(summary = "逻辑删除运输品类")
    @PostMapping(value = "/removeTransportCategory")
    public ItemResult<Void> removeTransportCategory(@Parameter(name = "arg0", description = "TransportCategoryRemoveDTO - 运输品类删除DTO对象") @RequestBody TransportCategoryRemoveDTO arg0) {
        return iTransportCategoryService.removeTransportCategory(arg0);
    }

    @Operation(summary = "运输品类合并过滤")
    @PostMapping(value = "/transportCategoryMergeFilter")
    public ItemResult<List<String>> transportCategoryMergeFilter(@Parameter(name = "arg0", description = "List<String> - 运输品类id列表") @RequestBody List<String> arg0) {
        return iTransportCategoryService.transportCategoryMergeFilter(arg0);
    }

    @Operation(summary = "获取品类Options")
    @PostMapping(value = "/queryOptions")
    public ItemResult<List<TransportOption>> queryOptions() {
        return iTransportCategoryService.queryOptions();
    }

    @Operation(summary = "查询所有品类")
    @PostMapping(value = "/queryAll")
    public ItemResult<List<TransportCategoryDTO>> queryAll() {
        return iTransportCategoryService.queryAll();
    }
}
