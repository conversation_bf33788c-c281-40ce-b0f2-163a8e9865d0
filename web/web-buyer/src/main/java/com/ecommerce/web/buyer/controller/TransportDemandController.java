package com.ecommerce.web.buyer.controller;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.transportdemand.TransportDemandDeleteDTO;
import com.ecommerce.logistics.api.dto.transportdemand.TransportDemandDetailDTO;
import com.ecommerce.logistics.api.dto.transportdemand.TransportDemandListDTO;
import com.ecommerce.logistics.api.dto.transportdemand.TransportDemandQueryDTO;
import com.ecommerce.logistics.api.dto.transportdemand.TransportDemandSaveDTO;
import com.ecommerce.logistics.api.enums.UserRoleEnum;
import com.ecommerce.logistics.api.service.ITransportDemandService;
import com.ecommerce.member.api.session.LoginInfo;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 * Description:
 * Date: Create in 下午5:04 18/12/21
 */
@RestController
@Tag(name = "TransportDemandController", description = "运输需求")
@RequestMapping("/transportDemand")
public class TransportDemandController {

    @Autowired
    private ITransportDemandService transportDemandService;

    @PostMapping(value = "/addTransportDemand")
    public ItemResult<String> addTransportDemand(@Parameter(hidden = true)LoginInfo loginInfo,
                                                 @Parameter(name = "transportDemandSaveDTO", description = "运输需求保存DTO对象") @RequestBody TransportDemandSaveDTO transportDemandSaveDTO) {
        transportDemandSaveDTO.setUserId(loginInfo.getMemberId());
        transportDemandSaveDTO.setUserName(loginInfo.getMemberName());
        transportDemandSaveDTO.setUserType(UserRoleEnum.BUYER.getCode());
        transportDemandSaveDTO.setOperatorUserId(loginInfo.getAccountId());
        transportDemandSaveDTO.setOperatorUserName(loginInfo.getAccountName());

        return transportDemandService.addTransportDemand(transportDemandSaveDTO);
    }

    @PostMapping(value = "/deleteTransportDemand")
    public ItemResult<Void> deleteTransportDemand(@Parameter(hidden = true)LoginInfo loginInfo,
                                                  @Parameter(name = "transportDemandDeleteDTO", description = "运输需求删除DTO对象") @RequestBody TransportDemandDeleteDTO transportDemandDeleteDTO) {
        transportDemandDeleteDTO.setOperatorUserId(loginInfo.getAccountId());
        transportDemandDeleteDTO.setOperatorUserName(loginInfo.getAccountName());
        return transportDemandService.deleteTransportDemand(transportDemandDeleteDTO);
    }

    @PostMapping(value = "/editTransportDemand")
    public ItemResult<Void> editTransportDemand(@Parameter(hidden = true)LoginInfo loginInfo,
                                                @Parameter(name = "transportDemandSaveDTO", description = "运输需求保存DTO对象") @RequestBody TransportDemandSaveDTO transportDemandSaveDTO) {
        transportDemandSaveDTO.setOperatorUserId(loginInfo.getAccountId());
        transportDemandSaveDTO.setOperatorUserName(loginInfo.getAccountName());
        return transportDemandService.editTransportDemand(transportDemandSaveDTO);
    }

    @PostMapping(value = "/queryTransportDemandDetail")
    public ItemResult<TransportDemandDetailDTO> queryTransportDemandDetail(@Parameter(name = "transportDemandId", description = "运输需求id") @RequestParam String transportDemandId) {
        return transportDemandService.queryTransportDemandDetail(transportDemandId);
    }

    @PostMapping(value = "/queryTransportDemandList")
    public ItemResult<PageData<TransportDemandListDTO>> queryTransportDemandList(@Parameter(hidden = true)LoginInfo loginInfo,
                                                                                 @Parameter(name = "pageQuery", description = "运输需求查询DTO对象分页查询对象") @RequestBody PageQuery<TransportDemandQueryDTO> pageQuery) {
        if (pageQuery.getQueryDTO() == null) {
            pageQuery.setQueryDTO(new TransportDemandQueryDTO());
        }
        pageQuery.getQueryDTO().setUserId(loginInfo.getMemberId());
        pageQuery.getQueryDTO().setUserType(UserRoleEnum.BUYER.getCode());
        return transportDemandService.queryTransportDemandList(pageQuery);
    }
}
