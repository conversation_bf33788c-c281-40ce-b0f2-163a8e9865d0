package com.ecommerce.web.buyer.controller;

import cn.hutool.core.text.CharSequenceUtil;
import com.ecommerce.base.api.dto.cloud.AttachmentinfoDTO;
import com.ecommerce.base.api.service.IAttachmentService;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.exception.BusinessOperationException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.logistics.api.dto.attachment.AttListDTO;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.shipping.ShippingInfoListQueryDTO;
import com.ecommerce.logistics.api.dto.vehicle.UserVehicleListDTO;
import com.ecommerce.logistics.api.dto.vehicle.UserVehicleListQueryDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleAddDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleAppAddDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleAppDataDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleAppEditDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleBaseDataDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleBatchAddResultDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleBuyerTakeQueryDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleDetailDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleDisableFlgEditDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleEditDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleImportTemplateOptionDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleListDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleListQueryDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleRemoveDTO;
import com.ecommerce.logistics.api.enums.UserRoleEnum;
import com.ecommerce.logistics.api.service.IShippingInfoService;
import com.ecommerce.logistics.api.service.IVehicleService;
import com.ecommerce.member.api.session.LoginInfo;
import com.ecommerce.trace.api.dto.vehicle.TraceVehicleBatchDeleteDTO;
import com.ecommerce.trace.api.dto.vehicle.TraceVehicleDTO;
import com.ecommerce.trace.api.dto.vehicle.TraceVehicleEditDTO;
import com.ecommerce.trace.api.dto.vehicle.TraceVehicleStatusDTO;
import com.ecommerce.trace.api.service.ITraceVehicleService;
import com.ecommerce.web.buyer.dto.logistics.BatchAddVehicleReadExcelResultDTO;
import com.ecommerce.web.buyer.dto.logistics.BatchAddVehicleResultDTO;
import com.ecommerce.web.buyer.dto.logistics.ExtVehicleListDTO;
import com.ecommerce.web.buyer.utils.VehicleExcelUtil;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @Date: 22/08/2018 09:12
 * @Description:
 */
@RestController
@RequestMapping(value = "/vehicle")
@CrossOrigin
@Slf4j
@Tag(name = "VehicleController", description = "车辆")
public class   VehicleController {

    @Autowired
    private IVehicleService vehicleService;

    @Autowired
    private ITraceVehicleService traceVehicleService;

    @Autowired
    private IAttachmentService attachmentService;

    @Autowired
    private IShippingInfoService shippingInfoService;
    
    private static final String LICENSE_PLATE_NUMBER_ONE = "(^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼]{1})";

    @PostMapping("/queryVehicleList")
    public ItemResult<PageData<ExtVehicleListDTO>> queryVehicleList(@Parameter(hidden = true)LoginInfo loginInfo,
                                                                    @Parameter(name = "pageQuery", description = "车辆列表查询对象分页查询对象") @RequestBody PageQuery<VehicleListQueryDTO> pageQuery) {
        pageQuery.getQueryDTO().setUserId(loginInfo.getMemberId());
        pageQuery.getQueryDTO().setUserType(UserRoleEnum.BUYER.getCode());
        ItemResult<PageData<ExtVehicleListDTO>> result = new ItemResult<>(new PageData<>(new PageInfo<>(Lists.newArrayList())));
        ItemResult<PageData<VehicleListDTO>> pageDataItemResult = vehicleService.queryVehicleList(pageQuery);
        if (pageDataItemResult == null || pageDataItemResult.getData() == null || CollectionUtils.isEmpty(pageDataItemResult.getData().getList())) {
            return result;
        }
        List<VehicleListDTO> vehicleListDTOS = pageDataItemResult.getData().getList();
        List<TraceVehicleStatusDTO> traceVehicleStatusDTOS = traceVehicleService.selectVehicleSignalStatus(vehicleListDTOS.stream().map(VehicleListDTO::getNumber).toList());
        Map<String, String> plateNumber2SignalStatusMap = traceVehicleStatusDTOS.stream().collect(Collectors.toMap(TraceVehicleStatusDTO::getPlateNumber, TraceVehicleStatusDTO::getSignalStatus));
        List<ExtVehicleListDTO> extVehicleListDTOList = Lists.newArrayList();
        for (VehicleListDTO lgsDto : vehicleListDTOS) {
            ExtVehicleListDTO extVehicleListDTO = new ExtVehicleListDTO();
            BeanUtils.copyProperties(lgsDto, extVehicleListDTO);
            extVehicleListDTO.setSignalStatus(plateNumber2SignalStatusMap.get(lgsDto.getNumber()));
            extVehicleListDTOList.add(extVehicleListDTO);
        }

        BeanUtils.copyProperties(pageDataItemResult, result, "data");

        PageData<ExtVehicleListDTO> extPage = new PageData<>();
        BeanUtils.copyProperties(pageDataItemResult.getData(), extPage, "list");
        extPage.setList(extVehicleListDTOList);
        result.setData(extPage);
        return result;
    }

    @PostMapping("/modifyVehicle")
    public ItemResult<Void> modifyVehicle(@Parameter(hidden = true)LoginInfo loginInfo,
                                          @Parameter(name = "vehicleEditDTO", description = "车辆编辑DTO对象") @RequestBody VehicleEditDTO vehicleEditDTO) {
        TraceVehicleEditDTO traceVehicleEditDTO = new TraceVehicleEditDTO();
        vehicleEditDTO.setUpdateUser(loginInfo.getAccountId());
        BeanUtils.copyProperties(vehicleEditDTO, traceVehicleEditDTO);
        if (vehicleEditDTO.getAxles() != null) {
            traceVehicleEditDTO.setAxle(vehicleEditDTO.getAxles());
        }
        if (vehicleEditDTO.getGpsDeviceNumber() != null) {
            traceVehicleEditDTO.setGpsSerial(vehicleEditDTO.getGpsDeviceNumber());
        }
        try {
            traceVehicleEditDTO.setUserId(loginInfo.getMemberId());
            traceVehicleEditDTO.setUserName(loginInfo.getMemberName());
            traceVehicleService.updateVehicle(traceVehicleEditDTO);
        } catch (Exception e) {
            log.error("修改车辆同步trace异常:{}", traceVehicleEditDTO, e);
        }
        vehicleEditDTO.setOperationUserId(loginInfo.getAccountId());
        vehicleEditDTO.setOperationUserName(loginInfo.getAccountName());
        return vehicleService.modifyVehicle(vehicleEditDTO);
    }

    @Operation(summary = "删除车辆")
    @PostMapping(value = "/removeVehicle", consumes = "application/json;charset=UTF-8")
    public ItemResult<Void> removeVehicle(@Parameter(hidden = true)LoginInfo loginInfo,
                                          @Parameter(name = "list", description = "车辆删除实体列表") @RequestBody List<VehicleRemoveDTO> list) {
        TraceVehicleBatchDeleteDTO traceVehicleBatchDeleteDTO = new TraceVehicleBatchDeleteDTO();
        List<String> ids = new ArrayList<>();
        for (VehicleRemoveDTO dto : list) {
            dto.setUpdateUser(loginInfo.getAccountId());
            dto.setOperationUserId(loginInfo.getAccountId());
            dto.setOperationUserName(loginInfo.getAccountName());
            ids.add(dto.getVehicleId());
        }
        traceVehicleBatchDeleteDTO.setVehicleIds(ids);
        traceVehicleBatchDeleteDTO.setUserId(loginInfo.getMemberId());
        try {
            traceVehicleService.batchDeleteVehicle(traceVehicleBatchDeleteDTO);
        } catch (Exception e) {
            log.error("删除车辆同步trace异常:{}", traceVehicleBatchDeleteDTO, e);
        }
        return vehicleService.removeVehicle(list);
    }

    @Operation(summary = "添加车辆")
    @PostMapping(value = "/addVehicle")
    public ItemResult<String> addVehicle(@Parameter(hidden = true)LoginInfo loginInfo,
                                         @Parameter(name = "vehicleAddDTO", description = "车辆新增DTO对象") @RequestBody VehicleAddDTO vehicleAddDTO) {
        vehicleAddDTO.setCreateUser(loginInfo.getAccountId());
        vehicleAddDTO.setUserName(loginInfo.getMemberName());
        vehicleAddDTO.setUserId(loginInfo.getMemberId());
        vehicleAddDTO.setUserType(UserRoleEnum.BUYER.getCode());
        vehicleAddDTO.setOperationUserId(loginInfo.getAccountId());
        vehicleAddDTO.setOperationUserName(loginInfo.getAccountName());
        ItemResult<String> itemResult = vehicleService.addVehicle(vehicleAddDTO);

        TraceVehicleDTO traceVehicleDTO = new TraceVehicleDTO();
        BeanUtils.copyProperties(vehicleAddDTO, traceVehicleDTO);
        traceVehicleDTO.setVehicleId(itemResult.getData());
        traceVehicleDTO.setGpsSerial(vehicleAddDTO.getGpsDeviceNumber());
        try {
            traceVehicleService.addVehicle(traceVehicleDTO);
        } catch (Exception e) {
            log.error("添加车辆同步trace异常:{}", traceVehicleDTO, e);
        }
        return new ItemResult<>(null);
    }

    @Operation(summary = "查询车辆详情")
    @PostMapping(value = "/queryVehicle")
    public ItemResult<VehicleDetailDTO> queryVehicle(@Parameter(name = "vehicleId", description = "车辆id") @RequestParam String vehicleId) {
        ItemResult<VehicleDetailDTO> itemResult = vehicleService.queryVehicle(vehicleId);
        for (AttListDTO dto : itemResult.getData().getAttListDTOS()) {
            List<AttachmentinfoDTO> list = attachmentService.getAttachmentByBID(dto.getBid());
            if (CollectionUtils.isNotEmpty(list)) {
                dto.setFileUrl(list.get(0).getAttcPath());
            } else {
                return new ItemResult<>(null);
            }
        }
        return itemResult;
    }

    @Operation(summary = "根据Id 查询")
    @PostMapping(value = "/selectVehicleById")
    public ItemResult<VehicleDTO> selectVehicleById(@Parameter(name = "vehicleId", description = "车辆id") @RequestParam String vehicleId) {
        return vehicleService.selectVehicleById(vehicleId);
    }

    @Operation(summary = "通过用户ID(买家，卖家，承运商)")
    @PostMapping(value = "/queryVehicleListByUserId")
    public ItemResult<PageData<UserVehicleListDTO>> queryVehicleListByUserId(@Parameter(hidden = true)LoginInfo loginInfo,
                                                                             @Parameter(name = "pageQuery", description = "用户车辆列表查询对象分页查询对象") @RequestBody PageQuery<UserVehicleListQueryDTO> pageQuery) {
        if (pageQuery == null) {
            pageQuery = new PageQuery<>();
        }
        UserVehicleListQueryDTO dto = pageQuery.getQueryDTO() == null ? new UserVehicleListQueryDTO() : pageQuery.getQueryDTO();
        dto.setUserId(loginInfo.getMemberId());
        dto.setUserType(UserRoleEnum.BUYER.getCode());
        pageQuery.setQueryDTO(dto);
        return vehicleService.queryVehicleListByUserId(pageQuery);
    }

    @Operation(summary = "查询App端的车辆数据")
    @PostMapping(value = "/queryAppVehicle")
    public ItemResult<List<VehicleAppDataDTO>> queryAppVehicle(@Parameter(name = "userId", description = "用户id") @RequestParam String userId) {
        ItemResult<List<VehicleAppDataDTO>> itemResult = vehicleService.queryAppVehicleByUserId(userId);
        log.info("----调用附件查询接口查询附件数据:{}", itemResult.getData());
        for (VehicleAppDataDTO vehicles : itemResult.getData()) {
            for (AttListDTO dto : vehicles.getAttListDTOS()) {
                String bid = dto.getBid();
                List<AttachmentinfoDTO> list = attachmentService.getAttachmentByBID(bid);
                if (CollectionUtils.isEmpty(list)) {
                    itemResult.setSuccess(false);
                    itemResult.setDescription("获取附件信息失败");
                    log.error("获取附件失败");
                } else {
                    dto.setFileUrl(list.get(0).getAttcPath());
                }
            }
        }
        return itemResult;
    }

    @Operation(summary = "小程序修改车辆")
    @PostMapping(value = "/modifyAppVehicle")
    public ItemResult<Void> modifyAppVehicle(@Parameter(hidden = true)LoginInfo loginInfo,
                                             @Parameter(name = "arg0", description = "VehicleAppEditDTO - 更新非管控车辆实体") @RequestBody VehicleAppEditDTO arg0) {
        TraceVehicleEditDTO traceVehicleEditDTO = new TraceVehicleEditDTO();
        if (loginInfo != null) {
            arg0.setOperationUserId(loginInfo.getAccountId());
            arg0.setOperationUserName(loginInfo.getAccountName());
            arg0.setUpdateUser(loginInfo.getAccountId());
            traceVehicleEditDTO.setUserId(loginInfo.getMemberId());
            traceVehicleEditDTO.setUserName(loginInfo.getMemberName());
        }
        BeanUtils.copyProperties(arg0, traceVehicleEditDTO);
        traceVehicleService.updateVehicle(traceVehicleEditDTO);
        return vehicleService.modifyAppVehicle(arg0);
    }

    @Operation(summary = "添加非管控车辆")
    @PostMapping(value = "/addAppVehicle")
    public ItemResult<String> addAppVehicle(@Parameter(hidden = true)LoginInfo loginInfo,
                                            @Parameter(name = "arg0", description = "VehicleAppAddDTO - 非管控车辆添加实体") @RequestBody VehicleAppAddDTO arg0) {
        arg0.setUserId(loginInfo.getMemberId());
        arg0.setUserName(loginInfo.getMemberName());
        arg0.setCreateUser(loginInfo.getAccountId());
        arg0.setUserType(UserRoleEnum.BUYER.getCode());
        arg0.setOperationUserId(loginInfo.getAccountId());
        arg0.setOperationUserName(loginInfo.getAccountName());
        TraceVehicleDTO traceVehicleDTO = new TraceVehicleDTO();
        BeanUtils.copyProperties(arg0, traceVehicleDTO);
        ItemResult<String> itemResult = vehicleService.addAppVehicle(arg0);
        traceVehicleDTO.setVehicleId(itemResult.getData());
        try {
            traceVehicleService.addVehicle(traceVehicleDTO);
        } catch (Exception e) {
            log.error("添加非管控车辆同步trace异常:{}", traceVehicleDTO, e);
        }
        return itemResult;
    }

    @Operation(summary = "Excel批量导入车辆")
    @PostMapping(value = "/addVehicleByExcel")
    public ItemResult<BatchAddVehicleResultDTO> addVehicleByExcel(@Parameter(hidden = true)LoginInfo loginInfo, @RequestBody MultipartFile file) throws IOException {
        if (file == null || file.isEmpty()) {
            throw new BizException(BasicCode.INVALID_PARAM, ":上传失败，请选择文件");
        }
        if (CsStringUtils.isEmpty(file.getOriginalFilename()) || !CharSequenceUtil.endWith(file.getOriginalFilename(), ".xlsx")) {
            throw new BizException(BasicCode.INVALID_PARAM, ":导入文件格式不正确，请下载正确模板！");
        }
        // 读取Excel
        BatchAddVehicleReadExcelResultDTO resultDTO = VehicleExcelUtil.readExcel(new XSSFWorkbook(file.getInputStream()), loginInfo);

        List<VehicleAddDTO> vehicleAddDTOList = resultDTO.getVehicleAddDTOList();
        List<VehicleAppAddDTO> vehicleAppAddDTOList = resultDTO.getVehicleAppAddDTOList();
        log.info("vehicleAddDTOList.size : {}", vehicleAddDTOList.size());
        log.info("vehicleAppAddDTOList.size : {}", vehicleAppAddDTOList.size());
        if (vehicleAddDTOList.size() == 0 && vehicleAppAddDTOList.size() == 0) {
            throw new BizException(BasicCode.INVALID_PARAM, ":导入文件无数据，请修改后重试！");
        }

        BatchAddVehicleResultDTO result = new BatchAddVehicleResultDTO();

        // 管控车辆
        if (vehicleAddDTOList.size() > 0) {
            ItemResult<VehicleBatchAddResultDTO> itemResult = vehicleService.batchAddVehicle(vehicleAddDTOList);
            log.info("批量添加管控车辆ItemResult : {}", itemResult);
            result.setControlVehicle(itemResult.getData());
        }

        // 非管控车辆
        if (vehicleAppAddDTOList.size() > 0) {
            ItemResult<VehicleBatchAddResultDTO> itemResult = vehicleService.batchAddAppVehicle(vehicleAppAddDTOList);
            log.info("批量添加非管控车辆ItemResult : {}", itemResult);
            result.setNonControlVehicle(itemResult.getData());
        }


        return new ItemResult<>(result);
    }

    @Operation(summary = "生成车辆导入模板Excel")
    @PostMapping(value = "/generateVehicleImportTemplate")
    public void generateVehicleImportTemplate(@Parameter(hidden = true)HttpServletResponse response) {
        VehicleImportTemplateOptionDTO optionDTO = vehicleService.queryVehicleImportTemplateOptions().getData();
        VehicleExcelUtil.exportExcel(optionDTO, response);
    }

    @Operation(summary = "批量启用或禁用车辆")
    @PostMapping(value = "/updateVehicleDisableFlg")
    public ItemResult<Void> updateVehicleDisableFlg(@Parameter(hidden = true)LoginInfo loginInfo,
                                                    @Parameter(name = "list", description = "修改车辆是否禁用DTO列表") @RequestBody List<VehicleDisableFlgEditDTO> list) {
        for (VehicleDisableFlgEditDTO dto : list) {
            dto.setUpdateUser(loginInfo.getAccountId());
            dto.setOperationUserId(loginInfo.getAccountId());
            dto.setOperationUserName(loginInfo.getAccountName());
        }
        ItemResult<Void> itemResult = vehicleService.updateVehicleDisableFlg(list);
        if (!itemResult.isSuccess()) {
            log.error("批量启用或禁用车辆失败！" + itemResult.getDescription());
            throw new BizException(BasicCode.UNKNOWN_ERROR, ":批量启用或禁用车辆失败！" + itemResult.getDescription());
        }
        return new ItemResult<>(null);
    }

    @Operation(summary = "获取买家自提车辆")
    @PostMapping(value = "/getBuyerTakeCars")
    public ItemResult<List<VehicleBaseDataDTO>> getBuyerTakeCars(@Parameter(hidden = true)LoginInfo loginInfo,
                                                                 @Parameter(name = "queryDTO", description = "买家自提车辆列表查询对象") @RequestBody VehicleBuyerTakeQueryDTO queryDTO) {
        queryDTO.setUserId(loginInfo.getMemberId());
        return vehicleService.getBuyerTakeCars(queryDTO);
    }

    @Operation(summary = "通过用户ID查询车辆下拉列表")
    @PostMapping(value="/queryVehicleListDropDownBox")
    public ItemResult<List<UserVehicleListDTO>> queryVehicleListDropDownBox(@Parameter(hidden = true)LoginInfo loginInfo,
                                                                            @RequestBody UserVehicleListQueryDTO queryDTO)throws Exception{
        queryDTO.setUserId(loginInfo.getMemberId());
        //用户类型 1：买家 2：卖家 3：承运商
        queryDTO.setUserType(1);
        return vehicleService.queryVehicleListDropDownBox(queryDTO);
    }

    @Operation(summary = "车辆导出")
    @PostMapping(value = "/exportVehicle")
    public void exportVehicle(@Parameter(hidden = true)LoginInfo loginInfo, @Parameter(hidden = true)HttpServletResponse response,@RequestBody VehicleListQueryDTO queryDTO) {
        queryDTO.setUserId(loginInfo.getMemberId());
        queryDTO.setUserType(1);
        VehicleImportTemplateOptionDTO optionDTO = vehicleService.queryVehicleImportTemplateOptions().getData();
        List<VehicleListDTO> vehicleListDTOList = vehicleService.queryExportVehicleList(queryDTO).getData();
        if (CollectionUtils.isEmpty(vehicleListDTOList)) {
            log.info( "未查询到车辆");
            return;
        }
        VehicleExcelUtil.exportVehicleExcel(response, optionDTO, vehicleListDTOList);
    }
    
    @Operation(summary = "通过车牌号、船名查询")
    @PostMapping(value="/queryVehicleOrShippingListByName")
    public ItemResult<List<String>> queryVehicleOrShippingListByName(@Parameter(hidden = true)LoginInfo loginInfo,@Parameter(name = "arg0", description = "船名/车牌号") @RequestParam String arg0)throws Exception{
    	boolean isVehicleNumber = isVehicleNumber(arg0);
    	if (isVehicleNumber) {
    		VehicleListQueryDTO queryDTO = new VehicleListQueryDTO();
    		queryDTO.setUserId(loginInfo.getMemberId());
            //用户类型 1：买家 2：卖家 3：承运商
            queryDTO.setUserType(1);
            queryDTO.setNumber(arg0);
    		return vehicleService.selectVehicleNumber(queryDTO);
		}else {
			ShippingInfoListQueryDTO queryDTO = new ShippingInfoListQueryDTO();
			queryDTO.setMemberId(loginInfo.getMemberId());
			queryDTO.setShippingName(arg0);
//			10-企业承运商，11-个体船东承运商，20-卖家，30-买家
			queryDTO.setManagerMemberType("30");
			return shippingInfoService.selectShippingInfoName(queryDTO);
		}
    }

    private boolean isVehicleNumber(String arg0) {
		log.info("识别入参：{} 是否为车牌号", arg0);
		return Pattern.matches(LICENSE_PLATE_NUMBER_ONE, Character.toString(arg0.charAt(0)));
	}
}
