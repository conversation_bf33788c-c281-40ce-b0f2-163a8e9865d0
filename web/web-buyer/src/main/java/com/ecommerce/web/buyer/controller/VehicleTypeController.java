package com.ecommerce.web.buyer.controller;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.vehicletype.*;
import com.ecommerce.logistics.api.service.IVehicleTypeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <AUTHOR>
 * @Date: 27/08/2018 10:16
 * @Description:
 */
@RestController
@RequestMapping(value = "/vehicleType")
@CrossOrigin
@Slf4j
@Tag(name = "VehicleTypeController", description = "车辆类型管理")
public class VehicleTypeController {

    @Autowired
    private IVehicleTypeService iVehicleTypeService;

    @Operation(summary = "逻辑删除车辆类型")
    @PostMapping(value = "/removeVehicleType")
    public ItemResult<Void> removeVehicleType(@Parameter(name = "arg0", description = "VehicleTypeRemoveDTO - 车辆类型删除实体") @RequestBody VehicleTypeRemoveDTO arg0) {
        return iVehicleTypeService.removeVehicleType(arg0);
    }

    @Operation(summary = "编辑车辆类型")
    @PostMapping(value = "/modifyVehicleType")
    public ItemResult<Void> modifyVehicleType(@Parameter(name = "arg0", description = "VehicleTypeEditDTO - 车辆类型编辑DTO对象") @RequestBody VehicleTypeEditDTO arg0) {
        return iVehicleTypeService.modifyVehicleType(arg0);
    }

    @Operation(summary = "获取车辆类型列表")
    @PostMapping(value = "/queryVehicleTypeList")
    public ItemResult<PageData<VehicleTypeListDTO>> queryVehicleTypeList(@Parameter(name = "arg0", description = "PageQuery<VehicleTypeListQueryDTO> - 车辆类型列表查询对象分页查询对象") @RequestBody PageQuery<VehicleTypeListQueryDTO> arg0) {
        return iVehicleTypeService.queryVehicleTypeList(arg0);
    }

    @Operation(summary = "获取车辆类型Options")
    @PostMapping(value = "/queryOptions")
    public ItemResult<List<VehicleTypeOptionDTO>> queryOptions() {
        return iVehicleTypeService.queryOptions();
    }

    @Operation(summary = "新增车辆类型")
    @PostMapping(value = "/addVehicleType")
    public ItemResult<Void> addVehicleType(@Parameter(name = "arg0", description = "VehicleTypeAddDTO - 车辆类型新增DTO对象") @RequestBody VehicleTypeAddDTO arg0) {
        return iVehicleTypeService.addVehicleType(arg0);
    }

    @Operation(summary = "车辆类型的详细数据")
    @PostMapping(value = "/queryVehicleType")
    public ItemResult<VehicleTypeDetailDTO> queryVehicleType(@Parameter(name = "arg0", description = "车辆类型id") @RequestParam String arg0) {
        return iVehicleTypeService.queryVehicleType(arg0);
    }
}
