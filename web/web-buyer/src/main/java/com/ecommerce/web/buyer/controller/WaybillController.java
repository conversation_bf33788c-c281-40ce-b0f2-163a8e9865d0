package com.ecommerce.web.buyer.controller;


import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.common.service.RedisService;
import com.ecommerce.goods.api.service.IContractService;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.waybill.ArriveWarehouseDTO;
import com.ecommerce.logistics.api.dto.waybill.CancelReasonDTO;
import com.ecommerce.logistics.api.dto.waybill.CancelWaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.ChangeCarriageDTO;
import com.ecommerce.logistics.api.dto.waybill.ChangePriceDTO;
import com.ecommerce.logistics.api.dto.waybill.CheckWaybillListQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.CloseWaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.CompleteWaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.DistrictListDTO;
import com.ecommerce.logistics.api.dto.waybill.DriverConfirmDTO;
import com.ecommerce.logistics.api.dto.waybill.DriverWaybillAssignDTO;
import com.ecommerce.logistics.api.dto.waybill.DriverWaybillListQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.LeaveWarehouseDTO;
import com.ecommerce.logistics.api.dto.waybill.LocationDTO;
import com.ecommerce.logistics.api.dto.waybill.MergeWaybillListDTO;
import com.ecommerce.logistics.api.dto.waybill.MergeWaybillListQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.PassCheckDTO;
import com.ecommerce.logistics.api.dto.waybill.PublishWaybillListDTO;
import com.ecommerce.logistics.api.dto.waybill.PublishWaybillListQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.ReassignmentVehicleDTO;
import com.ecommerce.logistics.api.dto.waybill.RepublishWaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.SellerAssignWaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.SnatchWaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.TradeWaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.TradeWaybillDetailDTO;
import com.ecommerce.logistics.api.dto.waybill.UserSeckillWaybillListDTO;
import com.ecommerce.logistics.api.dto.waybill.UserSeckillWaybillQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.WaitCheckWaybillListDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillAssignDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillAssignListDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillAssignListQueryDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillDetailDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillLocationDTO;
import com.ecommerce.logistics.api.dto.waybill.WaybillLocationQueryDTO;
import com.ecommerce.logistics.api.enums.UserRoleEnum;
import com.ecommerce.logistics.api.service.IWaybillService;
import com.ecommerce.member.api.service.IAccountStoreRelationService;
import com.ecommerce.member.api.session.ERPAccountInfo;
import com.ecommerce.member.api.session.LoginInfo;
import com.ecommerce.order.api.service.ITakeInfoService;
import com.ecommerce.report.api.service.IReportService;
import com.ecommerce.trace.api.dto.alarm.WarnStatisticsResultDTO;
import com.ecommerce.trace.api.dto.waybill.TraceWaybillNumCondDTO;
import com.ecommerce.trace.api.dto.waybill.TraceWaybillTrackDTO;
import com.ecommerce.trace.api.service.ITraceWarnRecordService;
import com.ecommerce.trace.api.service.ITraceWaybillService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import com.ecommerce.common.utils.CsStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @Description:
 * @Date: Create in 下午8:32 18/9/10
 */
@RestController
@RequestMapping("/waybill")
@Slf4j
@Tag(name = "WaybillController", description = "运单")
public class WaybillController {

    @Autowired
    private IWaybillService waybillService;

    @Autowired
    private ITraceWarnRecordService traceWarnRecordService;

    @Autowired
    private ITraceWaybillService traceWaybillService;

    @Autowired
    private IAccountStoreRelationService accountStoreRelationService;

    @Autowired
    private IReportService reportService;

    @Autowired
    private IContractService contractService;

    @Autowired
    private ITakeInfoService takeInfoService;

    @Autowired
    private RedisService redisService;

    @PostMapping("/closeWaybill")
    public ItemResult<Void> closeWaybill(@Parameter(hidden = true)LoginInfo info, @RequestBody CloseWaybillDTO closeWaybillDTO) {
        closeWaybillDTO.setOperationUserId(info.getAccountId());
        closeWaybillDTO.setOperationUserName(info.getAccountName() == null ? "" : info.getAccountName());
        return waybillService.closeWaybill(closeWaybillDTO);
    }

    @GetMapping("/queryWaybillsByPickingBillIds")
    public ItemResult<List<WaybillDTO>> queryWaybillsByPickingBillIds(@Parameter(name = "pickingBillId", description = "提货单id") @RequestParam String pickingBillId) {
        return waybillService.queryWaybillsByPickingBillIds(Collections.singletonList(pickingBillId));
    }

    @PostMapping("/getLocation")
    public ItemResult<WaybillLocationDTO> getLocation(@Parameter(name = "waybillId", description = "运单id") @RequestParam String waybillId) {
        return waybillService.getLocation(waybillId);
    }

    @PostMapping("/passCheck")
    public ItemResult<Void> passCheck(@Parameter(hidden = true)LoginInfo info,
                                      @Parameter(name = "dto", description = "通过审核传入对象") @RequestBody PassCheckDTO dto) {
        dto.setUserId(info.getMemberId());
        dto.setUserName(info.getRealName());
        return waybillService.passCheck(dto);
    }

    @PostMapping("/changePrice")
    public ItemResult<Void> changePrice(@Parameter(hidden = true)LoginInfo info,
                                        @Parameter(name = "dto", description = "运价审核对象") @RequestBody ChangePriceDTO dto) {
        dto.setUserId(info.getMemberId());
        dto.setUserName(info.getRealName());
        return waybillService.changePrice(dto);
    }

    @PostMapping("/arriveWarehouse")
    public ItemResult<Void> arriveWarehouse(@Parameter(hidden = true)LoginInfo info,
                                            @Parameter(name = "dto", description = "车辆进站传输对象") @RequestBody ArriveWarehouseDTO dto) {
        dto.setUserId(info.getMemberId());
        dto.setUserName(info.getRealName());
        return waybillService.arriveWarehouse(dto);
    }

    @PostMapping("/getWaybillDetail")
    public ItemResult<WaybillDetailDTO> getWaybillDetail(@Parameter(name = "waybillId", description = "运单id") @RequestParam("waybillId") String arg0) {
        return waybillService.getWaybillDetail(arg0);
    }

    @PostMapping("/assignWaybill")
    public ItemResult<Void> assignWaybill(@Parameter(hidden = true)LoginInfo info,
                                          @Parameter(name = "dto", description = "运单指派对象") @RequestBody WaybillAssignDTO dto) {
        dto.setUserId(info.getMemberId());
        dto.setUserName(info.getRealName());
        return waybillService.assignWaybill(dto);
    }

    @PostMapping("/completeWaybill")
    public ItemResult<Void> completeWaybill(@Parameter(hidden = true)LoginInfo info,
                                            @Parameter(name = "dto", description = "完成运单对象") @RequestBody CompleteWaybillDTO dto) {
        dto.setOperationUserId(info.getMemberId());
        dto.setOperationUserName(info.getRealName());
        return waybillService.completeWaybill(dto);
    }

    @PostMapping("/getWarningCheck")
    public ItemResult<WarnStatisticsResultDTO> getWarningCheck(@Parameter(name = "waybillId", description = "运单id") @RequestParam String waybillId) {
        return traceWarnRecordService.statisticsWarn(waybillId);
    }

    @PostMapping("/changeCarriage")
    public ItemResult<Void> changeCarriage(@Parameter(hidden = true)LoginInfo info,
                                           @Parameter(name = "dto", description = "修改价格对象") @RequestBody ChangeCarriageDTO dto) {
        dto.setOperationUserId(info.getAccountId());
        dto.setOperationUserName(info.getAccountName() == null ? "" : info.getAccountName());
        return waybillService.changeCarriage(dto);
    }

    @PostMapping("/snatchWaybill")
    public ItemResult<Void> snatchWaybill(@Parameter(hidden = true)LoginInfo info,
                                          @Parameter(name = "dto", description = "抢单对象") @RequestBody SnatchWaybillDTO dto) {
        dto.setUserId(info.getMemberId());
        dto.setUserName(info.getRealName());
        dto.setUserPhone(info.getMobile());
        return waybillService.snatchWaybill(dto);
    }

    @PostMapping("/inputLeaveWarehouseQuantity")
    public ItemResult<Void> inputLeaveWarehouseQuantity(@Parameter(hidden = true)LoginInfo info,
                                                        @Parameter(name = "dto", description = "运单出库数据传输对象") @RequestBody LeaveWarehouseDTO dto) {
        dto.setUserId(info.getMemberId());
        dto.setUserName(info.getRealName());
        return waybillService.inputLeaveWarehouseQuantity(dto);
    }

    @PostMapping("/getWaybillLocationByCon")
    public ItemResult<List<LocationDTO>> getWaybillLocationByCon(@Parameter(name = "dto", description = "待整合运单附近运单搜索") @RequestBody WaybillLocationQueryDTO dto) {
        return waybillService.getWaybillLocationByCon(dto);
    }

    @PostMapping("/changeStatusToWaitReceiveById")
    public ItemResult<Void> changeStatusToWaitReceiveById(@Parameter(name = "waybillId", description = "运单id") @RequestParam String waybillId) {
        return waybillService.changeStatusToWaitReceiveById(waybillId);
    }

    @PostMapping("/changeCancelReason")
    public ItemResult<Void> changeCancelReason(@Parameter(hidden = true)LoginInfo loginInfo,
                                               @Parameter(name = "dto", description = "取消订单原因传输对象") @RequestBody CancelReasonDTO dto) {
        dto.setOperationUserId(loginInfo.getAccountId());
        dto.setOperationUserName(loginInfo.getAccountName() == null ? "" : loginInfo.getAccountName());
        return waybillService.changeCancelReason(dto);
    }

    @PostMapping("/changeStatusToCanceledById")
    public ItemResult<Void> changeStatusToCanceledById(@Parameter(name = "waybillId", description = "运单id") @RequestParam String waybillId) {
        return waybillService.changeStatusToCanceledById(waybillId);
    }

    @PostMapping("/queryWaybillsByPickingBillIds")
    public ItemResult<List<WaybillDTO>> queryWaybillsByPickingBillIds(@Parameter(name = "arg0", description = "提货单id列表") @RequestBody List<String> arg0) {
        return waybillService.queryWaybillsByPickingBillIds(arg0);
    }

    @PostMapping("/queryWaybillsByWaybillIds")
    public ItemResult<List<WaybillDTO>> queryWaybillsByWaybillIds(@Parameter(name = "arg0", description = "运单id列表") @RequestBody List<String> arg0) {
        return waybillService.queryWaybillsByWaybillIds(arg0);
    }

    @PostMapping("/querySellerWaybillList")
    public ItemResult<PageData<PublishWaybillListDTO>> querySellerWaybillList(@Parameter(hidden = true)LoginInfo info,
                                                                              @Parameter(name = "pageQuery", description = "已发布运单查询对象分页查询对象") @RequestBody PageQuery<PublishWaybillListQueryDTO> pageQuery) {
        PublishWaybillListQueryDTO dto = pageQuery.getQueryDTO() == null ? new PublishWaybillListQueryDTO() : pageQuery.getQueryDTO();
        dto.setSellerId(info.getMemberId());
        pageQuery.setQueryDTO(dto);
        return waybillService.querySellerWaybillList(pageQuery);
    }

    @PostMapping("/cancelWaybillByPlatform")
    public ItemResult<Void> cancelWaybillByPlatform(@Parameter(hidden = true)LoginInfo info,
                                                    @Parameter(name = "dto", description = "取消订单原因传输对象") @RequestBody CancelWaybillDTO dto) {
        dto.setUserId(info.getMemberId());
        dto.setUserName(info.getRealName());
        dto.setUserRole(UserRoleEnum.BUYER.getCode());
        return waybillService.cancelWaybillByPlatform(dto);
    }

    @PostMapping("/queryWaybillAssignList")
    public ItemResult<PageData<WaybillAssignListDTO>> queryWaybillAssignList(@Parameter(hidden = true)LoginInfo info,
                                                                             @Parameter(name = "pageQuery", description = "时间段内运单指派查询对象分页查询对象") @RequestBody PageQuery<WaybillAssignListQueryDTO> pageQuery) {
        WaybillAssignListQueryDTO dto = pageQuery.getQueryDTO() == null ? new WaybillAssignListQueryDTO() : pageQuery.getQueryDTO();
        dto.setCarrierId(info.getMemberId());
        pageQuery.setQueryDTO(dto);
        return waybillService.queryWaybillAssignList(pageQuery);
    }

    @PostMapping("/confirmWaybillByDriver")
    public ItemResult<Void> confirmWaybillByDriver(@Parameter(hidden = true)LoginInfo info,
                                                   @Parameter(name = "waybillId", description = "运单id") @RequestParam String waybillId) {
        DriverConfirmDTO driverConfirmDTO = new DriverConfirmDTO();
        driverConfirmDTO.setWaybillId(waybillId);
        driverConfirmDTO.setOperationUserId(info.getAccountId());
        driverConfirmDTO.setOperationUserName(info.getAccountName() == null ? "" : info.getAccountName());

        return waybillService.confirmWaybillByDriver(driverConfirmDTO);
    }

    @PostMapping("/changeArriveDestinationTime")
    public ItemResult<Void> changeArriveDestinationTime(@Parameter(name = "waybillId", description = "运单id") @RequestParam String waybillId) {
        return waybillService.changeArriveDestinationTime(waybillId);
    }

    @PostMapping("/selectSeckillDistrictsByUserId")
    public ItemResult<List<DistrictListDTO>> selectSeckillDistrictsByUserId(@Parameter(hidden = true)LoginInfo info) {
        return waybillService.selectSeckillDistrictsByUserId(info.getMemberId());
    }

    @PostMapping("/selectUserSeckillWaybillList")
    public ItemResult<PageData<UserSeckillWaybillListDTO>> selectUserSeckillWaybillList(@Parameter(hidden = true)LoginInfo info,
                                                                                        @Parameter(name = "pageQuery", description = "用户可抢单列表查询对象分页查询对象") @RequestBody PageQuery<UserSeckillWaybillQueryDTO> pageQuery) {
        UserSeckillWaybillQueryDTO dto = pageQuery.getQueryDTO() == null ? new UserSeckillWaybillQueryDTO() : pageQuery.getQueryDTO();
        dto.setUserId(info.getMemberId());
        pageQuery.setQueryDTO(dto);
        return waybillService.selectUserSeckillWaybillList(pageQuery);
    }

    @PostMapping("/getCarriersByType")
    public ItemResult<Void> getCarriersByType(@Parameter(name = "carrierType", description = "承运商类型") @RequestParam String carrierType) {
        return waybillService.getCarriersByType(carrierType);
    }

    @PostMapping("/changeBeginCarryTime")
    public ItemResult<Void> changeBeginCarryTime(@Parameter(name = "waybillId", description = "运单id") @RequestParam String waybillId) {
        return waybillService.changeBeginCarryTime(waybillId);
    }

    @PostMapping("/queryWaybillListByDeliveryNum")
    public ItemResult<List<TradeWaybillDTO>> queryWaybillListByDeliveryNum(@Parameter(name = "deliverySheetNum", description = "发货单号") @RequestParam String deliverySheetNum) {
        return waybillService.queryWaybillListByDeliveryNum(deliverySheetNum);
    }

    @PostMapping("/queryPublishWaybillList")
    public ItemResult<PageData<PublishWaybillListDTO>> queryPublishWaybillList(@Parameter(hidden = true)LoginInfo loginInfo,
                                                                               @Parameter(name = "pageQuery", description = "已发布运单查询对象分页查询对象") @RequestBody PageQuery<PublishWaybillListQueryDTO> pageQuery) {
        PublishWaybillListQueryDTO dto = pageQuery.getQueryDTO() == null ? new PublishWaybillListQueryDTO() : pageQuery.getQueryDTO();
        dto.setBuyerId(loginInfo.getMemberId());
        dto.setRegionCodeList(loginInfo.getAccountRegionAdCodeList());
        List<ERPAccountInfo> erpAccountList = loginInfo.getErpAccountList();
        if (CollectionUtils.isNotEmpty(erpAccountList)) {
            List<String> mdmCodeList = erpAccountList.stream()
                    .map(ERPAccountInfo::getMdmCode)
                    .filter(CsStringUtils::isNotBlank)
                    .distinct()
                    .toList();
            dto.setMdmCodeList(mdmCodeList);
        }

        pageQuery.setQueryDTO(dto);
        return waybillService.querySellerWaybillList(pageQuery);
    }

    @PostMapping("/queryMergeWaybillList")
    public ItemResult<PageData<MergeWaybillListDTO>> queryMergeWaybillList(@Parameter(name = "pageQuery", description = "待整合运单列表查询条件DTO分页查询对象") @RequestBody PageQuery<MergeWaybillListQueryDTO> pageQuery) {
        return waybillService.queryMergeWaybillList(pageQuery);
    }

    @PostMapping("/queryCheckWaybillList")
    public ItemResult<PageData<WaitCheckWaybillListDTO>> queryCheckWaybillList(@Parameter(name = "pageQuery", description = "待审核运单列表查询条件DTO分页查询对象") @RequestBody PageQuery<CheckWaybillListQueryDTO> pageQuery) {
        return waybillService.queryCheckWaybillList(pageQuery);
    }

    @PostMapping("/republishWaybillByPlatform")
    public ItemResult<Void> republishWaybillByPlatform(@Parameter(hidden = true)LoginInfo info,
                                                       @Parameter(name = "dto", description = "重新发布运单") @RequestBody RepublishWaybillDTO dto) {
        dto.setUserId(info.getMemberId());
        dto.setUserName(info.getRealName());
        return waybillService.republishWaybillByPlatform(dto);
    }

    @PostMapping("/assignWaybillBySeller")
    public ItemResult<Void> assignWaybillBySeller(@Parameter(hidden = true)LoginInfo info,
                                                  @Parameter(name = "dto", description = "卖家指派运单对象") @RequestBody SellerAssignWaybillDTO dto) {
        dto.setUserId(info.getMemberId());
        dto.setUserName(info.getRealName());
        return waybillService.assignWaybillBySeller(dto);
    }

    @PostMapping("/queryWaybillsByDispatchIds")
    public ItemResult<List<WaybillDTO>> queryWaybillsByDispatchIds(@Parameter(name = "dispatchBillIds", description = "调度单id列表") @RequestBody List<String> dispatchBillIds) {
        return waybillService.queryWaybillsByDispatchIds(dispatchBillIds);
    }

    @PostMapping("/selectUserSeckilledWaybillList")
    public ItemResult<PageData<UserSeckillWaybillListDTO>> selectUserSeckilledWaybillList(@Parameter(hidden = true)LoginInfo info,
                                                                                          @Parameter(name = "pageQuery", description = "司机已抢运单查询对象分页查询对象") @RequestBody PageQuery<DriverWaybillListQueryDTO> pageQuery) {
        DriverWaybillListQueryDTO dto = pageQuery.getQueryDTO() == null ? new DriverWaybillListQueryDTO() : pageQuery.getQueryDTO();
        dto.setUserId(info.getMemberId());
        pageQuery.setQueryDTO(dto);
        return waybillService.selectUserSeckilledWaybillList(pageQuery);
    }

    @PostMapping("/carrierAssignWaybillToDriver")
    public ItemResult<Void> carrierAssignWaybillToDriver(@Parameter(hidden = true)LoginInfo info,
                                                         @Parameter(name = "dto", description = "承运商指派运单给指定司机") @RequestBody DriverWaybillAssignDTO dto) {
        dto.setUserId(info.getMemberId());
        dto.setUserName(info.getRealName());
        return waybillService.carrierAssignWaybillToDriver(dto);
    }

    @GetMapping("/queryTradeWaybillDetail")
    public ItemResult<TradeWaybillDetailDTO> queryTradeWaybillDetail(@Parameter(name = "waybillId", description = "运单id") @RequestParam String waybillId) {
        return waybillService.queryTradeWaybillDetail(waybillId);
    }

    @PostMapping("/queryDetailByWaybillNum")
    public ItemResult<TraceWaybillTrackDTO> queryDetailByWaybillNum(@Parameter(hidden = true)LoginInfo info,
                                                                    @Parameter(name = "waybillNum", description = "运单号") @RequestParam String waybillNum) {
        TraceWaybillNumCondDTO traceWaybillNumCondDTO = new TraceWaybillNumCondDTO();
        traceWaybillNumCondDTO.setWaybillNum(waybillNum);
        return traceWaybillService.queryWaybillInfoForTrack(traceWaybillNumCondDTO);
    }

    @Operation(summary = "买家待配送运单重新指派车辆接口")
    @PostMapping(value="/reassignmentVehicle")
    public ItemResult<Void> reassignmentVehicle(@Parameter(hidden = true)LoginInfo info,@RequestBody ReassignmentVehicleDTO queryDTO){
        queryDTO.setUserId(info.getMemberId());
        queryDTO.setUserName(info.getRealName());
        queryDTO.setUserType(UserRoleEnum.BUYER.getCode());
        queryDTO.setAccountId(info.getAccountId());
        queryDTO.setAccountName(info.getAccountName());
        return waybillService.reassignmentVehicle(queryDTO);
    }
}
