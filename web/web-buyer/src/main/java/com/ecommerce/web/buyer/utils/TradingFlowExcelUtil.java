package com.ecommerce.web.buyer.utils;

import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.pay.api.v2.dto.bill.TradingFlowExportDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.VerticalAlignment;

import jakarta.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.IntStream;

import static org.apache.hc.core5.http.HttpHeaders.CONTENT_TYPE;

@Slf4j
public class TradingFlowExcelUtil {

    public static String getOrDefault(String s, String defaultValue) {
        return CsStringUtils.isNotBlank(s) ? s : defaultValue;
    }

    public static String bigDecimal2String(BigDecimal decimal, boolean isMoney) {
        return decimal != null ? decimal.compareTo(BigDecimal.ZERO) > 0 ? (isMoney ? "+" : "") + formatBigDecimal(decimal) : formatBigDecimal(decimal) : BigDecimal.ZERO.toString();
    }

    public static String formatBigDecimal(BigDecimal decimal) {
        return decimal.setScale(2, RoundingMode.HALF_UP).toString();
    }

    public static void exportExcel(List<TradingFlowExportDTO> tradingFlowExportDTOList, HttpServletResponse response, boolean isCredit) {
        // sheet名
        String sheetName = isCredit ? "卖家授信流水" : "卖家交易流水";
        // excel标题
        String[] title = {
                "订单号", "交易流水号", isCredit ? null : "第三方支付号", isCredit ? "支付时间" : "创建时间", "交易类型",
                "卖家名称", /*"商品品类", "商品名称", "商品数量",*/ "交易说明", "支付方式",
                "金额", "交易状态"};
        title = Arrays.stream(title).filter(Objects::nonNull).toArray(String[]::new);
        // excel文件名
        String fileName = (isCredit ? "creditTrading" : "trading") + DateTimeFormatter.ofPattern("_yyyy_MM_dd_HH_mm_ss").format(LocalDateTime.now()) + ".xls";

        String[][] content = null;
        if (CollectionUtils.isNotEmpty(tradingFlowExportDTOList)) {
            int contentLength = tradingFlowExportDTOList.size();
            content = new String[contentLength][];
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

            for (int i = 0; i < contentLength; i++) {
                content[i] = new String[title.length];
                TradingFlowExportDTO obj = tradingFlowExportDTOList.get(i);

                int index = 0;
                // 订单号
                content[i][index++] = getOrDefault(obj.getOrderNo(), "-");
                // 交易流水号
                content[i][index++] = getOrDefault(obj.getBillLogsNo(), "-");
                // 第三方支付号
                if (!isCredit) {
                    content[i][index++] = getOrDefault(obj.getThirdBillNo(), "-");
                }
                // 创建时间
                content[i][index++] = getOrDefault(format.format(obj.getCreateTime()), "-");
                // 交易类型
                content[i][index++] = getOrDefault(obj.getSubPayType(), "-");

                // 买家名称
                content[i][index++] = getOrDefault(obj.getTargetMemberName(), "-");
                content[i][index++] = getOrDefault(obj.getDetail(), "-");
                // 支付方式
                content[i][index++] = getOrDefault(obj.getChannelName(), "-");

                // 金额
                content[i][index++] = bigDecimal2String(obj.getActualPayAmount().negate(), true);
                // 交易状态
                content[i][index] = isCredit ? "成功" : "支付成功";
            }
        }

        HSSFWorkbook wb = getHSSFWorkbook(sheetName, title, content, null);

        // 响应到客户端
        try {
            response.setHeader(CONTENT_TYPE, "application/octet-stream;charset=utf-8");
            // TODO 解决中文显示为下划线问题
            log.info("fileName : {}", fileName);
            log.info("getBytes : {}", new String(fileName.getBytes(), "UTF-8"));
            response.setHeader("Content-Disposition", new String(fileName.getBytes(), "UTF-8"));
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            OutputStream os = response.getOutputStream();
            wb.write(os);
            os.flush();
            os.close();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 导出Excel
     *
     * @param sheetName sheet名称
     * @param title     标题
     * @param values    内容
     * @param wb        HSSFWorkbook对象
     * @return
     */
    public static HSSFWorkbook getHSSFWorkbook(String sheetName, String[] title, String[][] values, HSSFWorkbook wb) {

        // 第一步，创建一个HSSFWorkbook，对应一个Excel文件
        if (wb == null) {
            wb = new HSSFWorkbook();
        }

        // 第二步，在workbook中添加一个sheet,对应Excel文件中的sheet
        HSSFSheet sheet = wb.createSheet(sheetName);

        // 第三步，在sheet中添加表头第0行,注意老版本poi对Excel的行数列数有限制
        HSSFRow row = sheet.createRow(0);

        // 第四步，创建单元格，设置表头
        HSSFCellStyle titleStyle = wb.createCellStyle();
        titleStyle.setAlignment(HorizontalAlignment.CENTER); // 水平居中
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中
        titleStyle.setFillForegroundColor(IndexedColors.GREY_40_PERCENT.getIndex()); // 背景色
        titleStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND); // 背景色
        titleStyle.setBorderTop(BorderStyle.THIN); // 上边框
        titleStyle.setBorderBottom(BorderStyle.THIN); // 下边框
        titleStyle.setBorderLeft(BorderStyle.THIN); // 左边框
        titleStyle.setBorderRight(BorderStyle.THIN); // 右边框

        // 声明列对象
        HSSFCell cell = null;

        // 创建标题
        for (int i = 0; i < title.length; i++) {
            cell = row.createCell(i);
            cell.setCellValue(title[i]);
            cell.setCellStyle(titleStyle);
        }

        // 单元格样式
        HSSFCellStyle cellStyle = wb.createCellStyle();
        // cellStyle.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中
        cellStyle.setAlignment(HorizontalAlignment.CENTER);//居中
        cellStyle.setBorderTop(BorderStyle.THIN); // 上边框
        cellStyle.setBorderBottom(BorderStyle.THIN); // 下边框
        cellStyle.setBorderLeft(BorderStyle.THIN); // 左边框
        cellStyle.setBorderRight(BorderStyle.THIN); // 右边框


        // 创建内容
        if (values != null && values.length > 0) {
            for (int i = 0; i < values.length; i++) {
                row = sheet.createRow(i + 1);
                for (int j = 0; j < values[i].length; j++) {
                    // 将内容按顺序赋给对应的列对象
                    cell = row.createCell(j);
                    cell.setCellValue(values[i][j]);
                    cell.setCellStyle(cellStyle);
                }
            }
        }
        // 必须在单元格设值以后进行
        // 设置为根据内容自动调整列宽
        IntStream.range(0, title.length).forEach(sheet::autoSizeColumn);
        // 处理中文不能自动调整列宽的问题
        setSizeColumn(sheet, title.length);
        return wb;
    }

    // 自适应宽度(中文支持)
    private static void setSizeColumn(HSSFSheet sheet, int size) {
        for (int columnNum = 0; columnNum < size; columnNum++) {
            int columnWidth = sheet.getColumnWidth(columnNum) / 256;
            for (int rowNum = 0; rowNum < sheet.getLastRowNum(); rowNum++) {
                HSSFRow currentRow;
                // 当前行未被使用过
                if (sheet.getRow(rowNum) == null) {
                    currentRow = sheet.createRow(rowNum);
                } else {
                    currentRow = sheet.getRow(rowNum);
                }
                if (currentRow.getCell(columnNum) != null) {
                    HSSFCell currentCell = currentRow.getCell(columnNum);
                    if (currentCell.getCellType() == CellType.STRING) {
                        int length = currentCell.getStringCellValue().getBytes().length;
                        if (columnWidth < length) {
                            columnWidth = length;
                        }
                    }
                }
            }
            if (columnWidth > 255) {
                columnWidth = 255;
            }
            sheet.setColumnWidth(columnNum, columnWidth * 256);
        }
    }
}
