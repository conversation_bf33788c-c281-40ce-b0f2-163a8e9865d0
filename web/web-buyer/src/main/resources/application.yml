logging:
  config: classpath:log/logback-${spring.profiles.active}.xml
  level:
    com.netflix.discovery.DiscoveryClient: WARN
  pattern:
    # Logging pattern containing traceId and spanId; no longer provided through Sleuth by default
    level: "%5p [${spring.application.name:},%X{traceId:-},%X{spanId:-}]"
#先写死dev好跳转，各本地开发请自行屏蔽

spring:
  config:
    import: 'optional:configserver:'
  cloud:
    config:
      uri: http://config.ecome.com
      profile: dev
      label: asset-2025
      name: web-buyer,eureka,vip-server,kafka,rabbitmq,redis

  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true