<!--
系统全局公共javascript-
-->
<#--noinspection CssUnusedSymbol,JSUnresolvedVariable-->
<#-- @ftlvariable name="appServer" type="java.lang.String" -->
<!--jquery-->
<script src="${appServer}/scripts/jquery-2.1.1.min.js"></script>
<!--bootstrap-->
<script src="${appServer}/scripts/plugins/bootstrap/js/bootstrap.min.js"></script>
<!-- Bootstrap table -->
<script src="${appServer}/scripts/plugins/bootstrap-table/bootstrap-table.min.js"></script>
<script src="${appServer}/scripts/plugins/bootstrap-table/bootstrap-table-editable.min.js"></script>
<script src="${appServer}/scripts/plugins/bootstrap3-editable/js/bootstrap-editable.min.js"></script>
<script src="${appServer}/scripts/plugins/bootstrap-table/bootstrap-table-mobile.min.js"></script>
<script src="${appServer}/scripts/plugins/bootstrap-table/locale/bootstrap-table-zh-CN.min.js"></script>
<script src="${appServer}/scripts/plugins/bootstrap-select/bootstrap-select.min.js"></script>


<!-- Peity -->
<script src="${appServer}/scripts/plugins/peity/jquery.peity.min.js"></script>
<!-- layer -->
<script src="${appServer}/scripts/plugins/layer/layer.min.js"></script>
<!-- 自定义js -->
<script src="${appServer}/scripts/content.js"></script>
<!--jquery validate-->
<script src="${appServer}/scripts/plugins/validate/jquery.validate.min.js"></script>
<script src="${appServer}/scripts/plugins/validate/messages_zh.min.js"></script>
<!--<script src="${appServer}/scripts/plugins/layer/laydate/laydate.js"></script>-->
<script src="${appServer}/scripts/plugins/laydate/laydate.js"></script>
<!--serializejson-->
<script src="${appServer}/scripts/plugins/serializejson/jquery.serializejson.min.js"></script>
<!--common-->
<script src="${appServer}/scripts/common.js"></script>
<script>
    layer.config({
        skin: 'layui-layer-lan' //默认皮肤
    });
    jQuery.validator.addMethod("isRightfulString", function (value, element) {
        return this.optional(element) || /^[A-Za-z0-9_-]+$/.test(value);
    }, "判断是否为合法字符(a-zA-Z0-9-_)");
    jQuery.validator.addMethod("taxCode", function (value, element) {
        return this.optional(element) || /^[A-Z0-9]+$/.test(value);
    }, "格式非法");
    jQuery.validator.addMethod("tel", function (value, element) {
        return this.optional(element) || /^[0-9-]+$/.test(value);
    }, "格式非法");
    jQuery.validator.addMethod("zipCode", function (value, element) {
        var zip = /^[0-9]{6}$/;
        return this.optional(element) || (zip.test(value));
    }, "格式非法");
</script>
