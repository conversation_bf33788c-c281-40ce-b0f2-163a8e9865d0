<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="renderer" content="webkit">
    <title>君正集团招标采购平台</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    

</head>
<body class="fixed-sidebar full-height-layout gray-bg" style="overflow:hidden">

<div id="wrapper">
    <!--左侧导航开始-->
    <nav class="navbar-default navbar-static-side" role="navigation"
         style="background:url(${appServer}/service/member/images/frame/a14.png);background-repeat:no-repeat;">
        <#--<div class="nav-close"><i class="fa fa-times-circle"></i></div>-->
        <div class="sidebar-collapse">
            <ul class="nav" id="ul-menu">
                <li>
                    <div class="dropdown profile-element frame-4-logo">
                        <div class="frame-4-logo-img"></div>
                    </div>
                </li>
                <li>
                    <a class="J_menuItem" href="/service/member/worksheet.htm">
                        <i class="fa fa-home"></i>
                        <span class="nav-label">工作台</span>
                    </a>
                </li>
                <li>
                    <a class="J_menuItem" href="${appServer}/service/member/common_component.htm">
                        <i class="fa fa fa-cog"></i>
                        <span class="nav-label">公共组件</span>
                    </a>
                </li>

            </ul>
            <div id="real-menu" style="overflow: auto;">
                <ul class="nav" id="side-menu">
                    <li>
                    
                    </li>
                </ul>
            </div>
        </div>
        <div style="height:50px;width: 100%;bottom: 0;position: absolute;background:linear-gradient(270deg, rgba(54, 124, 183, 1) 0%, rgba(12, 35, 76, 1) 0%, rgba(54, 124, 183, 1) 100%, rgba(12, 35, 76, 1) 100%);"></div>
    </nav>
    <!--左侧导航结束-->

    <!--右侧部分开始-->
    <div id="page-wrapper" class="gray-bg dashbard-1">
        <!-- 顶部 -->
        <div class="row ">
            <nav class="navbar navbar-static-top frame-4-top" role="navigation">
                <!-- 顶部折叠菜单按钮 -->
                <div class="frame-4-foldMenu">
                    <div class="frame-4-foldMenu-img"></div>
                </div>
                <!-- 顶部平台名称 -->
                <div class="frame-4-platName">君正集团招标采购平台</div>
                <div style="float:right;">
                    <ul class="frame-4-topBtn">
                        <li style="width:150px;">
                            <div class="frame-4-userHead"></div>
                            <div class="frame-4-userName">您好！</div>
                        </li>
                        <li style="width:35px;cursor: pointer">
                            <div style="background: url('${appServer}/service/member/images/frame/a4.png');width:22px;height:22px"></div>
                            <div class="frame-index-msg-num">16</div>
                        </li>
                        <li>
                            <div class="frame-4-blank-line"></div>
                        </li>
                        <li style="width:35px;cursor: pointer;">
                            <img onclick="logout()" src="${appServer}/service/member/images/frame/a3.png">
                        </li>
                    </ul>
                </div>
            </nav>
            <!-- 顶部当前路径 -->
            <#--<div class="frame-4-route">-->
            <#--<div class="frame-4-route-currRoute">系統管理</div>-->
            <#--<div class="frame-4-route-realRoute">-->
            <#--<span style="color:#999999">您当前的位置：</span>&nbsp;&nbsp;<span style="color:#0068b2">基础功能设置</span>&nbsp;&nbsp;/&nbsp;&nbsp;<span style="color:#666666">系統管理</span>-->
            <#--</div>-->
            <#--</div>-->
        </div>
        <!--中部页面-->
        <div class="row J_mainContent frame-4-center">
            <div class="frame-4-center-container">
                <iframe id="J_iframe" name="iframe" width="100%" height="100%" src="${appServer}/service/member/worksheet.htm" frameborder="0" data-id="index.htm" seamless></iframe>
            </div>
        </div>

        <!--底部-->
        <div class="row frame-4-bottom">
            <span style="color:#ffffff">版权所有：内蒙古君正能源化工集团股份有限公司&nbsp;无ICP编号</span>
        </div>

    </div>
    <!--右侧部分结束-->
</div>

<!-- 全局js -->
<script src="${staticServer}/scripts/jquery.min.js"></script>
<script src="${staticServer}/scripts/bootstrap.min.js"></script>
<script src="${staticServer}/scripts/plugins/metisMenu/jquery.metisMenu.js"></script>
<script src="${staticServer}/scripts/plugins/slimscroll/jquery.slimscroll.min.js"></script>
<script src="${staticServer}/scripts/plugins/layer/layer.js"></script>
<script type="text/javascript" src="${staticServer}/scripts/index.js"></script>
<script type="text/javascript" src="${staticServer}/scripts/frame/frame-4-layout.js"></script>
<script>
    $(function () {
        $('#side-menu a').click(function () {
            var uri = $(this).data('href');
            $('#J_iframe').attr('src', uri);
        });

        //计算真实左侧菜单栏高度
        var height_body = $("body").height();
        var height_1 = getCssValue("#ul-menu", "height");
        var height_bottom = getCssValue(".frame-4-bottom", "height");
        var h = parseInt(height_body) - parseInt(height_1) - parseInt(height_bottom);
        $("#real-menu").css("height", h);


        $(".childDiv:gt(0)").each(function () {
            $(this).hide();
        })

    });

    function logout() {
        layer.confirm("确定登出？", function () {
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "${appServer}/service/member/login/logout",
                data: {},
                success: function (msg) {
                    if (msg.success == true) {
                        layer.msg(msg.data, {
                            time: 1000
                        }, function () {
                            window.location.reload();
                        });

                    } else {
                        layer.alert("操作失败:" + msg.data);
                    }
                }
            });
        });

    }

    function expandMenu(a) {
        var childId = $(a).attr("childId");
        var style = $("#"+childId).css('display');
        if(style == 'block'){//判断是否为展开状态
            $("#"+childId).slideUp();
        }else{
            $("#"+childId).slideDown();
        }
        $("#"+childId).siblings('.childDiv').slideUp();//只能有一个子元素为展开状态
        // $(".childDiv").each(function () {
        //     var id = $(this).attr("id");
        //     if (childId == id) {
        //         $(this).slideDown();
        //     } else {
        //         $(this).slideUp();
        //     }
        // })
        //$("#"+childId).slideToggle();
    }

    function getCssValue(ele, css) {
        var value = $(ele).css(css);
        if (value.indexOf("px") > -1) {
            value = value.substring(0, value.indexOf("px"));
        }
        return value;
    }
</script>
</body>
</html>