package com.ecommerce.web.emall.controller;

import com.alibaba.fastjson.JSON;
import com.ecommerce.base.api.dto.role.HasAnyRoleDTO;
import com.ecommerce.base.api.dto.role.RoleDTO;
import com.ecommerce.base.api.dto.saleregion.SaleRegionDTO;
import com.ecommerce.base.api.dto.saleregion.SaleRegionSampleDTO;
import com.ecommerce.base.api.enums.BaseRoleTypeEnum;
import com.ecommerce.base.api.service.IAccountRegionService;
import com.ecommerce.base.api.service.IRoleService;
import com.ecommerce.base.api.service.ISaleRegionService;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.common.utils.mail.CollectionKit;
import com.ecommerce.goods.api.dto.contract.TrContractDTO;
import com.ecommerce.goods.api.service.IContractService;
import com.ecommerce.member.api.dto.account.AccountBaseInfoUpdateDTO;
import com.ecommerce.member.api.dto.account.AccountBindingDTO;
import com.ecommerce.member.api.dto.account.AccountBindingSampleDTO;
import com.ecommerce.member.api.dto.account.AccountDTO;
import com.ecommerce.member.api.dto.account.AccountRegisterByMasterDTO;
import com.ecommerce.member.api.dto.account.AccountSearchDTO;
import com.ecommerce.member.api.dto.account.AccountWXBindingDTO;
import com.ecommerce.member.api.dto.account.ChangeAccountTypeDTO;
import com.ecommerce.member.api.dto.account.DeviceTokenDTO;
import com.ecommerce.member.api.dto.account.SubAccountUpdateDTO;
import com.ecommerce.member.api.dto.exception.MemberCode;
import com.ecommerce.member.api.dto.member.MemberDetailDTO;
import com.ecommerce.member.api.dto.member.MemberSimpleDTO;
import com.ecommerce.member.api.service.IAccountLoginInfoService;
import com.ecommerce.member.api.service.IAccountService;
import com.ecommerce.member.api.service.IMemberService;
import com.ecommerce.member.api.service.IOrgInfoService;
import com.ecommerce.member.api.session.LoginInfo;
import com.ecommerce.web.emall.util.IPUtils;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;


/**
 * web端控制器，账户相关功能对外服务接口
 *
 * <AUTHOR>
 */

@Slf4j
@Tag(name = "AccountInfoController", description = "登录后，用户获取和维护账户信息，该接口不可匿名访问")
@RestController
@RequestMapping("/account")
public class AccountController {
	
    @Autowired
    private IAccountService accountService;
    @Autowired
    private IMemberService memberService;
    @Autowired
    private IRoleService roleService;
    @Autowired
    private IOrgInfoService orgInfoService;
    @Autowired
    private IAccountRegionService accountRegionService;
    @Autowired
    private ISaleRegionService saleRegionService;
    @Autowired
    private IAccountLoginInfoService accountLoginInfoService;
    @Autowired
    private IContractService contractService;

    private static final String BINDING_ACCOUNT_ID_SET = "binding_account_id_set";

    @Operation(summary = "判断是否有角色(多个用,分割)")
    @GetMapping("/hasRole")
    public ItemResult<Boolean> haRole(@Parameter(hidden = true)LoginInfo loginInfo,@Parameter(name = "roleName", description = "角色名称") String roleName) {
        if (CsStringUtils.isBlank(roleName)) {
            return new ItemResult<Boolean>(false);
        }
        HasAnyRoleDTO dto = new HasAnyRoleDTO();
        dto.setAccountId(loginInfo.getAccountId());
        dto.setRoleNameList(Lists.newArrayList(roleName.split(",")));
        return new ItemResult<>(roleService.hasAnyRole(dto));
    }

    @Operation(summary = "主账号创建子账号")
    @PostMapping("/registerSubAccountByMaster")
    public ItemResult<AccountDTO> registerSubAccountByMaster(@Parameter(hidden = true)LoginInfo loginInfo, @Valid @RequestBody AccountRegisterByMasterDTO accountRegisterByMasterDTO) {
        accountRegisterByMasterDTO.setOperatorId(loginInfo.getAccountId());
        accountRegisterByMasterDTO.setMemberId(loginInfo.getMemberId());
        return new ItemResult<AccountDTO>(accountService.registerSubAccountByMaster(accountRegisterByMasterDTO));
    }

    @Operation(summary = "更新子账账户信息")
    @PostMapping(value="/updateSubAccountInfo")
    public ItemResult<Boolean> updateSubAccountInfo(@Parameter(hidden = true)LoginInfo loginInfo, @Valid @RequestBody SubAccountUpdateDTO subAccountUpdateDTO){
        isCurrMemberSubAccount(loginInfo,subAccountUpdateDTO.getAccountId(),null);
        subAccountUpdateDTO.setMemberId(loginInfo.getMemberId());
        accountService.updateSubAccountInfo(subAccountUpdateDTO);
        return new ItemResult<Boolean>(true);
    }

    @Operation(summary = "主账号给其它账户重置密码")
    @PostMapping("/resetPassword")
    public ItemResult<Boolean> resetPassword(@Parameter(hidden = true)LoginInfo loginInfo,@Parameter(name = "accountName", description = "用户名") @Valid String accountName,@Parameter(name = "newpassword", description = "新密码") String newpassword,@Parameter(hidden = true)HttpServletRequest request) {
        isCurrMemberSubAccount(loginInfo,null,accountName);
        AccountSearchDTO accountSearchDTO = new AccountSearchDTO();
        accountSearchDTO.setMemberId(loginInfo.getMemberId());
        accountSearchDTO.setAccountName(accountName);
        accountSearchDTO.setPageSize(1);
        accountSearchDTO.setPageNumber(0);
        ItemResult<Boolean> itemResult = new ItemResult<Boolean>(true);
        List<AccountDTO> list = accountService.findAll(accountSearchDTO).getList();
        if (list != null && !list.isEmpty() && CsStringUtils.equals(list.get(0).getAccountName(), accountName)) {
            accountService.resetPassword(accountName, newpassword, loginInfo.getAccountId(),IPUtils.getIpAddr(request));
            itemResult.setDescription("操作成功");
            return itemResult;
        }else{
            itemResult.setData(false);
            itemResult.setDescription("操作失败,账号不存在或者不是你的员工");
            return itemResult;
        }
    }

    @Operation(summary = "禁用账号")
    @PostMapping(value="/disabled")
    public ItemResult<Boolean> disabled(@Parameter(hidden = true)LoginInfo loginInfo,@Parameter(name = "reason", description = "禁用原因") @RequestParam String reason,@Parameter(name = "accountId", description = "账户Id") @RequestParam String accountId){
        isCurrMemberSubAccount(loginInfo,accountId,null);
        accountService.disabled(accountId,reason, loginInfo.getAccountId());
        return new ItemResult<Boolean>(true);
    }

    @Operation(summary = "启用账号")
    @PostMapping(value="/enabled")
    public ItemResult<Boolean> enabled(@Parameter(hidden = true)LoginInfo loginInfo,@Parameter(name = "accountId", description = "账户Id") @RequestParam String accountId){
        isCurrMemberSubAccount(loginInfo,accountId,null);
        accountService.enabled(accountId,loginInfo.getAccountId());
        return new ItemResult<Boolean>(true);
    }

    @Operation(summary = "设置为主账号")
    @PostMapping(value="/change2PrimaryAccount")
    public ItemResult<Boolean> change2PrimaryAccount(@Parameter(hidden = true)LoginInfo loginInfo, @Valid @RequestBody ChangeAccountTypeDTO changeAccountTypeDTO, HttpServletRequest request){
        isMaster(loginInfo);
        isCurrMemberSubAccount(loginInfo,changeAccountTypeDTO.getAccountId(),null);

        changeAccountTypeDTO.setMemberId(loginInfo.getMemberId());
        accountService.changeAccountType(changeAccountTypeDTO);
        if (!CsStringUtils.equals(loginInfo.getAccountId(), changeAccountTypeDTO.getAccountId())) {
            loginInfo.setAccountType(AccountDTO.ACCOUNT_TYPE_MEMBER_SUB);
            request.getSession().setAttribute(LoginInfo.SESSION_NAME,loginInfo);
        }

        return new ItemResult<Boolean>(true);
    }

    @Operation(summary = "登出(退出登录)")
    @GetMapping("/loginOut")
    public ItemResult<Boolean> loginOut(@Parameter(hidden = true)LoginInfo loginInfo, @Parameter(hidden = true)HttpServletRequest request) throws ServletException {
        checkLogin(loginInfo);
        String sessionId = request.getSession().getId();
        request.logout();
        accountLoginInfoService.offline(sessionId,"正常退出",loginInfo.getAccountId());
        return new ItemResult<>(true);
    }

    @Operation(summary = "获取登陆的当前用户信息(账号、绑定的账号、会员、组织机构、角色、权限、数据权限)")
    @GetMapping("/getCurrentUserInfo")
    public ItemResult<AccountDTO> getUserInfo(@Parameter(hidden = true)LoginInfo loginInfo, @Parameter(hidden = true)HttpServletRequest request) {
        checkLogin(loginInfo);
        AccountDTO accountDTO = accountService.findById(loginInfo.getAccountId());
        log.info("lucd===>最先获取accountDTO: {}", accountDTO);
        //组织机构
        accountDTO.setOrgInfo(orgInfoService.findOrgByAccountId(loginInfo.getAccountId()));
        //绑定的账号
        if (CsStringUtils.isNotBlank(accountDTO.getBindingId())) {
            List<AccountDTO> bindingAccountList = accountService.findBindingAccountById(loginInfo.getAccountId());
            if(bindingAccountList != null && !bindingAccountList.isEmpty()){
                List<AccountBindingSampleDTO> bindingSampleDTOList = Lists.newArrayList();
                Set<String> bindingAccountIdSet = Sets.newHashSet();
                bindingAccountList.forEach(item ->{
                    bindingAccountIdSet.add(item.getAccountId());
                    AccountBindingSampleDTO absDTO = new AccountBindingSampleDTO();
                    BeanUtils.copyProperties(item,absDTO);
                    bindingSampleDTOList.add(absDTO);
                });
                accountDTO.setBindingAccountList(bindingSampleDTOList);
                request.getSession().setAttribute(BINDING_ACCOUNT_ID_SET,bindingAccountIdSet);
            }
        }

        MemberSimpleDTO memberSimpleDTO = memberService.findMemberSimpleById(accountDTO.getMemberId());
        if(memberSimpleDTO == null || memberSimpleDTO.getMemberId() == null ){
            log.error("对应会员信息没有找到，memberId: {}",accountDTO.getMemberId());
        }else{
            //会员信息
            accountDTO.setMemberSimpleDTO(memberSimpleDTO);
            accountDTO.setHasErp(loginInfo.getHasErp());
        }
        //角色 销售区域 数据权限
        try {
            List<com.ecommerce.member.api.dto.base.RoleDTO> roleList = new ArrayList<>();
            for (com.ecommerce.base.api.dto.role.RoleDTO roleDTO : roleService.getRoleByAccountId(accountDTO.getAccountId())) {
                com.ecommerce.member.api.dto.base.RoleDTO baseRole = new com.ecommerce.member.api.dto.base.RoleDTO();
                BeanUtils.copyProperties(roleDTO, baseRole);
                roleList.add(baseRole);
            }
            accountDTO.setRoleList(roleList);
            if ( CollectionUtils.isNotEmpty(accountDTO.getRoleList()) ){
                List<String> roleTypeList = accountDTO.getRoleList().stream().filter(
                                item -> CsStringUtils.isNotBlank(item.getRoleType()))
                        .map(com.ecommerce.member.api.dto.base.RoleDTO::getRoleType)
                        .distinct().toList();
                accountDTO.setRoleTypeList(roleTypeList);
                if (memberSimpleDTO != null) {
                    if (roleTypeList.stream().anyMatch(item -> CsStringUtils.equals(item, "buyer3"))) {
                        memberSimpleDTO.setBuyerFlg(1);
                    } else {
                        memberSimpleDTO.setBuyerFlg(0);
                    }
                }
            }
            accountDTO.setSaleRegionInfoIdList(loginInfo.getSaleRegionIdList());
            accountDTO.setRegionAdCodeList(loginInfo.getAccountRegionAdCodeList());
            accountDTO.setAccountStoreIdList(loginInfo.getAccountStoreIdList());
            if (loginInfo.getSaleRegionIdList() != null) {
                List<Map<String,String>> listMap = new ArrayList<>();
                //剔重
                Set<String> ids = Sets.newHashSet(loginInfo.getSaleRegionIdList());
                log.info("===saleRegionIdList===->{}===", JSON.toJSONString(loginInfo.getSaleRegionIdList()));
                for(String id : ids){
                    SaleRegionSampleDTO saleRegionSampleDTO = saleRegionService.findSampleById(id);
                    Map<String,String> map = new HashMap<>();
                    if(saleRegionSampleDTO != null){
                        map.put("saleRegionId",id);
                        map.put("saleRegionName",saleRegionSampleDTO.getSaleRegionName());
                    }
                    listMap.add(map);
                }
                accountDTO.setSaleRegionMapList(listMap);
            }
        }catch(Exception e){
            log.error("获取权限、数据权限、销售区域信息出错：{},",e.getMessage(),e);
        }

        log.info("lucd===>当前用户的信息返回: {}", accountDTO);
        return new ItemResult<AccountDTO>(accountDTO);
    }

    @Operation(summary = "获取登陆的当前用户LoginInfo信息")
    @GetMapping("/getLoginInfo")
    public ItemResult<LoginInfo> getLoginInfo(@Parameter(hidden = true)LoginInfo loginInfo) {
        checkLogin(loginInfo);
        return new ItemResult<LoginInfo>(loginInfo);
    }

    @Operation(summary = "绑定用户切换")
    @GetMapping("/changeAccount")
    public ItemResult<LoginInfo> changeAccount(@Parameter(hidden = true)LoginInfo loginInfo,@Parameter(name = "accountId", description = "账户Id") String accountId, @Parameter(hidden = true)HttpServletRequest request) {
        checkLogin(loginInfo);
        Object object = request.getSession().getAttribute(BINDING_ACCOUNT_ID_SET);
        if(object != null && object instanceof Set){
            Set<String> bindingAccountIdSet = (Set<String>)object;
            if( bindingAccountIdSet.contains(accountId)){
                AccountDTO accountDTO = accountService.findById(accountId);
                if(accountDTO == null || accountDTO.getAccountId() == null ){
                    throw new BizException(BasicCode.DATA_NOT_EXIST,"绑定的账号不存在");
                }
                log.info("切换用户到: {}",accountDTO.getAccountName());
                return new ItemResult<>(getLoginInfo(accountDTO,request));
            }else{
                throw new BizException(BasicCode.DATA_NOT_EXIST,"绑定的账号id不存在");
            }
        }else{
            throw new BizException(BasicCode.DATA_NOT_EXIST,"没有找到绑定的账号");
        }
    }

    /**
     * 值集：
     * //        *********	平台自有
     * //        *********	平台签约
     * //        *********	社会运力
     * //        *********	平台门店
     * 返回值说明：
     * 0.其它
     * 1.个人司机
     * 2.社会承运商下的司机
     * 3.社会承运商的主账号
     * 4.平台门店承运商主账号
     * 5.平台门店承运商子账号
     * 6.平台自有运力承运商主账号
     * 7.平台自有运力承运商下的司机
     * 8.平台签约运力承运商主账号
     * 9.平台签约运力承运商下的司机
     * @param loginInfo
     * @return
     */
    // TODO: 重构此方法以降低认知复杂度 (当前: 46, 目标: ≤15)
    // 建议: 1) 提取子方法 2) 使用早期返回 3) 简化条件表达式
    @Operation(summary = "获取用户的司机信息(0.其它 1.个人司机 2.社会承运商下的司机 3.社会承运商的主账号 4.平台门店承运商主账号 5.平台门店承运商子账号 6.平台自有运力承运商主账号 7.平台自有运力承运商下的司机 8.平台签约运力承运商主账号 9.平台签约运力承运商下的司机)")
    @GetMapping("/getCurrentUserDriverInfo")
    public ItemResult<Integer> getCurrentUserDriverInfo(@Parameter(hidden = true)LoginInfo loginInfo) {
        checkLogin(loginInfo);

        MemberDetailDTO memberDTO = memberService.findMemberById(loginInfo.getMemberId());
        AccountDTO accountDTO = accountService.findDetailById(loginInfo.getAccountId());
//            0.其它
        Integer result = 0;
        //承运商类型值集 CARRIER_TYPE *********  社会运力 角色id = 2 个人司机 角色id = 9 承运商司机
        //如果是社会运力的承运商
        if( memberDTO.getCarrierFlg() != null && memberDTO.getCarrierFlg().intValue() == 1 &&
                memberDTO.getCarrierType() != null && memberDTO.getCarrierType().contains("*********")){
//            3.社会承运商的主账号
            if (CsStringUtils.equals(memberDTO.getMainAccountId(), loginInfo.getAccountId())) {
                result = 3;
            }else {
                //如果是子账号，又有司机角色
                //            2.社会承运商下的司机
                List<RoleDTO>  roleDTOList = roleService.getRoleByAccountId(loginInfo.getAccountId());
                if( roleDTOList != null && roleDTOList.stream().filter(item ->item.getRoleId()!= null && (item.getRoleId().intValue() == 9 || item.getRoleId().intValue() == 2)).count() > 0 ){
                    result = 2;
                }
            }
            //如果是挂靠在平台的平台门店承运商下的门店司机
        }else if(memberDTO.getCarrierFlg() != null && memberDTO.getCarrierFlg().intValue() == 1 &&
                memberDTO.getCarrierType() != null && memberDTO.getCarrierType().contains("*********")){
            //            4.平台门店承运商主账号
            if (CsStringUtils.equals(memberDTO.getMainAccountId(), loginInfo.getAccountId())) {
                result = 4;
            }else {
                //如果是子账号，又有司机角色
                //            5.平台门店承运商子账号
                List<RoleDTO>  roleDTOList = roleService.getRoleByAccountId(loginInfo.getAccountId());
                if( roleDTOList != null && roleDTOList.stream().filter(item ->item.getRoleId()!= null && (item.getRoleId().intValue() == 9 || item.getRoleId().intValue() == 2)).count() > 0 ){
                    result = 5;
                }
            }
        }else if(memberDTO.getCarrierFlg() != null && memberDTO.getCarrierFlg().intValue() == 1 &&
                memberDTO.getCarrierType() != null && memberDTO.getCarrierType().contains("*********")){
//            6.平台自有运力承运商主账号
            if (CsStringUtils.equals(memberDTO.getMainAccountId(), loginInfo.getAccountId())) {
                result = 6;
            }else {
                //如果是子账号，又有司机角色
//            7.平台自有运力承运商下的司机
                List<RoleDTO>  roleDTOList = roleService.getRoleByAccountId(loginInfo.getAccountId());
                if( roleDTOList != null && roleDTOList.stream().filter(item ->item.getRoleId()!= null && (item.getRoleId().intValue() == 9 || item.getRoleId().intValue() == 2)).count() > 0 ){
                    result = 7;
                }
            }
        }else if(memberDTO.getCarrierFlg() != null && memberDTO.getCarrierFlg().intValue() == 1 &&
                memberDTO.getCarrierType() != null && memberDTO.getCarrierType().contains("*********")){
//            8.平台签约运力承运商主账号
            if (CsStringUtils.equals(memberDTO.getMainAccountId(), loginInfo.getAccountId())) {
                result = 8;
            }else {
                //如果是子账号，又有司机角色
//            9.平台签约运力承运商下的司机
                List<RoleDTO>  roleDTOList = roleService.getRoleByAccountId(loginInfo.getAccountId());
                if( roleDTOList != null && roleDTOList.stream().filter(item ->item.getRoleId()!= null && (item.getRoleId().intValue() == 9 || item.getRoleId().intValue() == 2)).count() > 0 ){
                    result = 9;
                }
            }
        }else if (accountDTO.getPersonDriver() != null && accountDTO.getPersonDriver().booleanValue()){
//            1.个人司机
            result = 1;
        }
        return new ItemResult<Integer>(result);
    }

    @Operation(summary = "修改自己的手机号key1:mobile,key2:smsCode")
    @PostMapping("/updateMobilePhone")
    public ItemResult<Boolean> updateMobilePhone(@Parameter(hidden = true)LoginInfo loginInfo, @RequestBody Map<String,String> map, @Parameter(hidden = true)HttpServletRequest request)  {
        checkLogin(loginInfo);
        String mobile = map.get("mobile");
        String smsCode = map.get("smsCode");
        checkMobile(mobile);
        checkSMSCode(MODIFY_PHONE,mobile,smsCode,request);
        //如果手机号相同，则不修改
        if (!CsStringUtils.equals(loginInfo.getMobile(), mobile)) {
            accountService.updateMobilePhone(loginInfo.getAccountId(), mobile, loginInfo.getAccountId(),IPUtils.getIpAddr(request));
            loginInfo.setMobile(mobile);
            request.getSession().setAttribute(LoginInfo.SESSION_NAME,loginInfo);
        }else {
            return new ItemResult<>(BasicCode.CUSTOM_ERROR.getCode(),"手机号与原手机号一致");
        }
        return new ItemResult<>(true);
    }

    @Operation(summary = "强制修改密码")
    @PostMapping("/updatePasswordForce")
    public ItemResult<Object> updatePasswordForce(@Parameter(hidden = true)LoginInfo loginInfo, @RequestBody Map<String,String> map, @Parameter(hidden = true)HttpServletRequest request) {
        checkLogin(loginInfo);
        String password = map.get("password");
        isBlank("新密码",password);
        accountService.updatePassword(loginInfo.getAccountId(),password,loginInfo.getAccountId(),IPUtils.getIpAddr(request));
        return new ItemResult<Object>(new Object());
    }

    @Operation(summary = "修改自己的密码key1:password,key2:smsCode")
    @PostMapping("/updatePassword")
    public ItemResult<Object> updatePassword(@Parameter(hidden = true)LoginInfo loginInfo, @RequestBody Map<String,String> map, @Parameter(hidden = true)HttpServletRequest request) {
        checkLogin(loginInfo);
        String password = map.get("password");
        String smsCode = map.get("smsCode");
        isBlank("新密码",password);
        checkSMSCode(MODIFY_PASS_WORD,null,smsCode,request);
        accountService.updatePassword(loginInfo.getAccountId(),password,loginInfo.getAccountId(),IPUtils.getIpAddr(request));
        return new ItemResult<Object>(new Object());
    }

    @Operation(summary = "校验密码是否和现在的密码一样，提交修改前使用")
    @GetMapping("/checkPassword")
    public ItemResult<Boolean> checkPassword(@Parameter(hidden = true)LoginInfo loginInfo,@Parameter(name = "password", description = "密码") String password) {
        checkLogin(loginInfo);
        return new ItemResult<Boolean>(accountService.checkPassword(loginInfo.getAccountId(),password));
    }

    @Operation(summary = "更新DeviceToken")
    @PostMapping(value="/updateDeviceToken")
    public ItemResult<Boolean> updateDeviceToken(@Parameter(hidden = true)LoginInfo loginInfo, @Valid @RequestBody DeviceTokenDTO deviceTokenDTO) {
        deviceTokenDTO.setAccountId(loginInfo.getAccountId());
        accountService.updateDeviceToken(deviceTokenDTO);
        return new ItemResult<Boolean>(true);
    }

    @Operation(summary = "修改头像URL")
    @PostMapping("/updateHeadUrl")
    public ItemResult<Object> updateHeadUrl(@Parameter(hidden = true)LoginInfo loginInfo,@Parameter(name = "headUrl", description = "头像URL") String headUrl, HttpServletRequest request) {
        checkLogin(loginInfo);
        isBlank("头像地址",headUrl);

        AccountBaseInfoUpdateDTO accountBaseInfoUpdateDTO = new AccountBaseInfoUpdateDTO();
        accountBaseInfoUpdateDTO.setHeadPic(headUrl);
        accountBaseInfoUpdateDTO.setAccountId(loginInfo.getAccountId());
        accountService.updateBaseInfo(accountBaseInfoUpdateDTO);
        loginInfo.setHeadPic(headUrl);
        request.getSession().setAttribute(LoginInfo.SESSION_NAME,loginInfo);
        return new ItemResult<Object>(new Object());
    }

    @Operation(summary = "修改性别")
    @GetMapping(value="/updateSex")
    public ItemResult<Boolean> updateSex(@Parameter(hidden = true)LoginInfo loginInfo,@Parameter(name = "sex", description = "性别") String sex) {
        checkLogin(loginInfo);
        isBlank("性别",sex);

        int sexInt = 2;
        try{
            sexInt = Integer.parseInt(sex);
        }catch (Exception e){}

        AccountBaseInfoUpdateDTO accountBaseInfoUpdateDTO = new AccountBaseInfoUpdateDTO();
        accountBaseInfoUpdateDTO.setSex(sexInt);
        accountBaseInfoUpdateDTO.setAccountId(loginInfo.getAccountId());
        accountService.updateBaseInfo(accountBaseInfoUpdateDTO);
        return new ItemResult<Boolean>(true);
    }

    @Operation(summary = "判断账户名是否存在，账户名唯一")
    @PostMapping("/checkAccountNameExists")
    public ItemResult<Boolean> checkAccountNameExists(@Parameter(hidden = true)LoginInfo loginInfo,@Parameter(name = "newAccountName", description = "新用户名") String newAccountName) {
        checkLogin(loginInfo);
        return new ItemResult<Boolean>(accountService.checkAccountNameExists(newAccountName));
    }

    @Operation(summary = "修改自己的账户名")
    @PostMapping("/updateAccountName")
    public ItemResult<Object> updateAccountName(@Parameter(hidden = true)LoginInfo loginInfo,@Parameter(name = "newAccountName", description = "新用户名") String newAccountName,@Parameter(hidden = true)HttpServletRequest request) {
        checkLogin(loginInfo);
        accountService.updateAccountName(loginInfo.getAccountId(),newAccountName,loginInfo.getAccountId(),IPUtils.getIpAddr(request));
        return new ItemResult<Object>(new Object());
    }

    @Operation(summary = "判断手机号是否存在，手机号不唯一")
    @PostMapping("/checkMobilePhoneExists")
    public ItemResult<Boolean> checkMobilePhoneExists(@Parameter(hidden = true)LoginInfo loginInfo,@Parameter(name = "newMobilePhone", description = "新手机号") String newMobilePhone) {
        checkLogin(loginInfo);
        return new ItemResult<Boolean>(accountService.checkMobilePhoneExists(newMobilePhone));
    }
    
    @Operation(summary = "判断手机号是否注册过【检查主账号和个人账号，不检查子账号】账户，如果存在或则不合法则返回true")
    @PostMapping("/checkMobileByMasterAndPersonal")
    public ItemResult<Boolean> checkMobileByMasterAndPersonal(@Parameter(hidden = true)LoginInfo loginInfo,@Parameter(name = "newMobilePhone", description = "新手机号") String newMobilePhone) {
        ItemResult<Boolean> result = new ItemResult<Boolean>(null);
        result.setSuccess(true);
        result.setData(true);
        if (CsStringUtils.isBlank(newMobilePhone) || !Pattern.matches(PHONE_NUMBER_REG, newMobilePhone)) {
            result.setDescription("手机号不正确");
            return result;
        }
    	boolean personlAccount = accountService.checkMobilePhoneExistsByPersonl(newMobilePhone);
    	if(personlAccount) {
    		result.setDescription("个人账号中已经存在");
    		return result;
    	} else {
    		boolean masterAccount = accountService.checkMobilePhoneExistsByMasterAccount(newMobilePhone);
    		if(masterAccount) {
        		result.setDescription("主账号中已经存在");
        		return result;
        	} 
    	}
    	//不存在
        result.setData(false);
        result.setDescription("可以注册");
        return result;
    }

    @Operation(summary = "获取修改密码时的手机验证码")
    @GetMapping("/getSMSCode")
    public ItemResult<Boolean> getSMSCodeOld(@Parameter(hidden = true)LoginInfo loginInfo,@Parameter(name = "mobile", description = "手机号") String mobile, HttpServletRequest request) {
        checkLogin(loginInfo);
        AccountDTO accountDTO = accountService.findById(loginInfo.getAccountId());
        sendSMSCode("",mobile,accountDTO.getRegisterSign(),request);
        return new ItemResult<Boolean>(true);
    }

    @Operation(summary = "获取修改密码时的手机验证码")
    @GetMapping("/getSMSCodeByModifyPassword")
    public ItemResult<Boolean> getSMSCodeByModifyPassword(@Parameter(hidden = true)LoginInfo loginInfo,@Parameter(name = "mobile", description = "手机号") String mobile, HttpServletRequest request) {
        checkLogin(loginInfo);
        AccountDTO accountDTO = accountService.findById(loginInfo.getAccountId());
        sendSMSCode(MODIFY_PASS_WORD,mobile,accountDTO.getRegisterSign(),request);
        return new ItemResult<Boolean>(true);
    }

    @Operation(summary = "获取修改手机时的手机验证码")
    @GetMapping("/getSMSCodeByModifyMobile")
    public ItemResult<Boolean> getSMSCodeByModifyMobile(@Parameter(hidden = true) LoginInfo loginInfo,@Parameter(name = "mobile", description = "手机号") String mobile, HttpServletRequest request) {
        checkLogin(loginInfo);
        AccountDTO accountDTO = accountService.findById(loginInfo.getAccountId());
        sendSMSCode(MODIFY_PHONE,mobile,accountDTO.getRegisterSign(),request);
        return new ItemResult<Boolean>(true);
    }

    @Operation(summary = "更新账户基本信息")
    @PostMapping(value="/updateBaseInfo")
    public ItemResult<Boolean> updateBaseInfo(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody AccountBaseInfoUpdateDTO accountBaseInfoUpdateDTO){
        accountBaseInfoUpdateDTO.setAccountId(loginInfo.getAccountId());
        accountBaseInfoUpdateDTO.setOperator(loginInfo.getAccountId());
        accountService.updateBaseInfo(accountBaseInfoUpdateDTO);
        return new ItemResult<Boolean>(true);
    }

    @Operation(summary = "一个人有多个用户的情况，设置默认登陆账户")
    @GetMapping("/setDefaultMember/{accountId}")
    public ItemResult<Boolean> setDefaultMember(@Parameter(hidden = true) LoginInfo loginInfo,@Parameter(name = "accountId", description = "用户名") @PathVariable String accountId, @Parameter(hidden = true) HttpServletRequest request) {
        checkLogin(loginInfo);
        Object object = request.getSession().getAttribute(BINDING_ACCOUNT_ID_SET);
        if(object instanceof Set) {
            Set<String> bindingAccountIdSet = (Set<String>) object;
            if (bindingAccountIdSet.contains(accountId)) {
                accountService.setDefaultMember(accountId, loginInfo.getAccountId());
                return new ItemResult<>(true);
            }
        }
        throw new BizException(BasicCode.INVALID_PARAM,"accountId不正确");
    }

    @Operation(summary = "账号绑定")
    @PostMapping("/bindingAccount")
    public ItemResult<Boolean> bindingAccount(@Parameter(hidden = true) LoginInfo loginInfo, @Valid @RequestBody AccountBindingDTO accountBindingDTO) {
        checkLogin(loginInfo);
        accountBindingDTO.setCurrentAccountId(loginInfo.getAccountId());
        accountService.bindingAccount(accountBindingDTO);
        return new ItemResult<Boolean>(true);
    }

    @Operation(summary = "查询绑定的账号")
    @GetMapping("/findBindingAccount")
    public ItemResult<List<AccountDTO>> findBindingAccount(@Parameter(hidden = true) LoginInfo loginInfo) {
        checkLogin(loginInfo);
        return new ItemResult<List<AccountDTO>>(accountService.findBindingAccountById(loginInfo.getAccountId()));
    }

    @Operation(summary = "账号解绑")
    @PostMapping("/unBindingAccount")
    public ItemResult<Boolean> unBindingAccount(@Parameter(hidden = true) LoginInfo loginInfo,@Parameter(name = "unBindingAccountId", description = "解绑账户Id") String unBindingAccountId) {
        checkLogin(loginInfo);
        isBlank("要解绑的账户id",unBindingAccountId);
        accountService.unBindingAccount(loginInfo.getAccountId(),unBindingAccountId);
        return new ItemResult<Boolean>(true);
    }

    @Operation(summary = "当前账号绑定微信账号")
    @PostMapping(value="/bindingWXAccount")
    public ItemResult<Boolean> bindingWXAccount(@Parameter(hidden = true) LoginInfo loginInfo, @Valid @RequestBody AccountWXBindingDTO accountWXBindingDTO){
        checkLogin(loginInfo);
        accountWXBindingDTO.setBindingAccountName(loginInfo.getAccountName());
    	accountService.weChatBinding(accountWXBindingDTO);
    	return new ItemResult<Boolean>(true);
    }

    @Operation(summary = "当前用户解绑微信账号")
    @PostMapping(value="/unBindingWXAccount")
    public ItemResult<Boolean> unBindingWXAccount(@Parameter(hidden = true) LoginInfo loginInfo,@Parameter(hidden = true) HttpServletRequest request){
        checkLogin(loginInfo);
    	accountService.weChatUnBinding(loginInfo.getAccountId(),IPUtils.getIpAddr(request));
 	  return new ItemResult<Boolean>(true);
    }

    @Operation(summary = "根据会员id查询其下的子账号中的业务员")
    @PostMapping("/getSalesmanByMemberId")
    public ItemResult<List<AccountDTO>> getSalesmanByMemberId(@Parameter(name = "memberId", description = "会员Id") String memberId,@Parameter(name = "contractId", description = "业务员Id")String contractId){
        List<AccountDTO> result = Lists.newArrayList();
        if (CsStringUtils.isBlank(memberId)) {
            return new ItemResult<>(result);
        }
        if (CsStringUtils.isNotBlank(contractId)) {
            TrContractDTO contract = contractService.getContract(contractId);
            if (contract != null && CsStringUtils.isNotBlank(contract.getSalemanAccountId())) {
                AccountDTO accountDTO = new AccountDTO();
                accountDTO.setAccountId(contract.getSalemanAccountId());
                accountDTO.setAccountName(contract.getSalemanAccountName());
                result.add(accountDTO);
                return new ItemResult<>(result);
            }
        }
        result = accountService.getSalesmanByMemberId(memberId);
        return new ItemResult<>(result);
    }

    @Operation(summary = "根据会员id查询其下的子账号中的销售经理")
    @GetMapping(value="/findSalesManagerByMemberId")
    public ItemResult<List<AccountDTO>> findSalesManagerByMemberId(@Parameter(name = "memberId", description = "会员Id") String memberId){
        List<AccountDTO> result = accountService.findSalesManagerByMemberId(memberId);
        return new ItemResult<List<AccountDTO>>(result);
    }

    @Operation(summary = "根据会员id查询其下的子账号中的销售助理")
    @GetMapping(value="/findSalesAssistantByMemberId")
    public ItemResult<List<AccountDTO>> findSalesAssistantByMemberId(@Parameter(name = "memberId", description = "会员Id") String memberId){
        List<AccountDTO> result = accountService.findSalesAssistantByMemberId(memberId);
        return new ItemResult<List<AccountDTO>>(result);
    }

    @Operation(summary = "根据会员id查询其下的仓库管理员")
    @GetMapping(value="/findStorageAdminByMemberId")
    public ItemResult<List<AccountDTO>> findStorageAdminByMemberId(@Parameter(name = "memberId", description = "会员Id") String memberId){
        List<AccountDTO> result = accountService.findByMemberIdAndRoles(memberId,Lists.newArrayList(BaseRoleTypeEnum.STORAGE_ADMIN_SELLER.getId(),BaseRoleTypeEnum.STORAGE_ADMIN_PLATFORM.getId()));
        return new ItemResult<List<AccountDTO>>(result);
    }

    @Operation(summary = "根据会员id查询其下的某角色的子账号{memberId:'',roleIds:[1,2,3]}")
    @PostMapping(value="/findByMemberIdAndRoles")
    public ItemResult<List<AccountDTO>> findByMemberIdAndRoles(@RequestBody Map<String,Object> map){
        if( map == null ){
            throw  new BizException(BasicCode.INVALID_PARAM,"参数不可为空");
        }
        Object memberId = map.getOrDefault("memberId",null);
        if (memberId == null || CsStringUtils.isBlank(memberId.toString())) {
            throw  new BizException(BasicCode.INVALID_PARAM,"参数memberId不可为空");
        }
        Object roleIds = map.getOrDefault("roleIds",null);
        if( roleIds == null || !(roleIds instanceof List) || ((List<Integer>)roleIds).isEmpty() ){
            throw  new BizException(BasicCode.INVALID_PARAM,"参数roleIds不可为空,且必须是数组");
        }
        List<AccountDTO> result = accountService.findByMemberIdAndRoles(memberId.toString(),((List<Integer>)roleIds));
        return new ItemResult<List<AccountDTO>>(result);
    }

    @Operation(summary = "根据条件查询员工")
    @PostMapping("/list")
    public ItemResult<PageInfo<AccountDTO>> findAll(@Parameter(hidden = true) LoginInfo loginInfo, @Valid @RequestBody AccountSearchDTO accountSearchDTO) {
        accountSearchDTO.setMemberId(loginInfo.getMemberId());
        PageInfo<AccountDTO> page = accountService.findAll(accountSearchDTO);
        if( page != null && page.getList() != null && !page.getList().isEmpty() ){
            for(AccountDTO item : page.getList()){
                item.setOrgInfo(orgInfoService.findOrgByAccountId(item.getAccountId()));
            }
        }
        return new ItemResult<PageInfo<AccountDTO>>(page);
    }

    @Operation(summary = "根据id查询单个账户详细信息)")
    @PostMapping("/findDetailById")
    public ItemResult<AccountDTO> findDetailById(@Parameter(hidden = true) LoginInfo loginInfo,@Parameter(name = "accountId", description = "账号Id") @RequestParam String accountId) {
        checkLogin(loginInfo);
        return new ItemResult<AccountDTO>(accountService.findDetailById(accountId));
    }

    @Operation(summary = "获取公钥")
    @PostMapping("/getPublicKey")
    public ItemResult<String> getPublicKey(@Parameter(hidden = true) LoginInfo loginInfo) {
        checkLogin(loginInfo);
        String publicKey = accountService.getPublicKey();
        return new ItemResult<String>(publicKey);
    }
    /**
     *手机验证码过期时间 ms
     */
    private static final long PHONE_SMSCODE_TIME = 600000L;
    private static final String MODIFY_PHONE = "1";
    private static final String MODIFY_PASS_WORD = "2";
    private static final String PHONE_NUMBER_REQUEST_SESSION_SMSCODE = "smscodeKey";
    private static final String PHONE_NUMBER_REQUEST_SESSION_SMSCODE_TIME = "smscodeKeyTime";
    private static final String PHONE_NUMBER_REQUEST_SESSION_PHONENUM = "phoneNum";

    private boolean checkSMSCode(String key,String mobile,String smsCode,HttpServletRequest request){
        Object smsCode_ = request.getSession().getAttribute(PHONE_NUMBER_REQUEST_SESSION_SMSCODE+key);
        if( smsCode_ == null ){
            throw new BizException(MemberCode.VALIDATION_ERROR,"验证码不存在");
        }

        long smsCodeTime = (Long) request.getSession().getAttribute(PHONE_NUMBER_REQUEST_SESSION_SMSCODE_TIME+key);
        if(System.currentTimeMillis() - smsCodeTime > PHONE_SMSCODE_TIME  ){
            request.getSession().removeAttribute(PHONE_NUMBER_REQUEST_SESSION_SMSCODE+key);
            request.getSession().removeAttribute(PHONE_NUMBER_REQUEST_SESSION_SMSCODE_TIME+key);
            request.getSession().removeAttribute(PHONE_NUMBER_REQUEST_SESSION_PHONENUM+key);
            throw new BizException(MemberCode.VALIDATION_ERROR,"验证码已失效,请重新获取");
        }

        if (!CsStringUtils.equalsIgnoreCase(smsCode_.toString(), smsCode)) {
            throw new BizException(MemberCode.VALIDATION_ERROR,"验证码输入不正确");
        }
        String phoneNum = (String)request.getSession().getAttribute(PHONE_NUMBER_REQUEST_SESSION_PHONENUM+key);
        if (CsStringUtils.isNotBlank(mobile) && !CsStringUtils.equals(mobile, phoneNum)) {
            throw new BizException(MemberCode.VALIDATION_ERROR,"手机号输入不正确");
        }
        return true;
    }

    private void sendSMSCode(String key,String mobile,String sign,HttpServletRequest request){
        checkMobile(mobile);
        String smsCode = RandomStringUtils.random(6,false,true);
        log.info("发送短信验证码 ：{}：{}",mobile,smsCode);

        request.getSession().setAttribute(PHONE_NUMBER_REQUEST_SESSION_SMSCODE+key,smsCode);
        request.getSession().setAttribute(PHONE_NUMBER_REQUEST_SESSION_SMSCODE_TIME+key,System.currentTimeMillis());
        request.getSession().setAttribute(PHONE_NUMBER_REQUEST_SESSION_PHONENUM+key,mobile);
    }

    private void checkLogin(LoginInfo loginInfo){
        if (loginInfo == null || CsStringUtils.isBlank(loginInfo.getAccountId())) {
            throw new BizException(MemberCode.LOGIN_ERROR,"你还没有登录，请先登录");
        }
    }
    /**
     * 宽松校验，可能以后会新增号段
     */
    private static final String PHONE_NUMBER_REG = "^(1[0-9][0-9])\\d{8}$";

    private void checkMobile(String mobile){
        if (CsStringUtils.isBlank(mobile) || !Pattern.matches(PHONE_NUMBER_REG, mobile)) {
            throw new BizException(MemberCode.VALIDATION_ERROR,"手机号不正确");
        }
    }
    private void isBlank(String paramName,String paramValue){
        if (CsStringUtils.isBlank(paramValue)) {
            throw  new BizException(BasicCode.INVALID_PARAM,paramName+"不可为空");
        }
    }
    private void isMaster(LoginInfo loginInfo ){
        checkLogin(loginInfo);
        if ( (loginInfo.getAccountType() == null) || (loginInfo.getAccountType().intValue() != AccountDTO.ACCOUNT_TYPE_MEMBER_MASTER) ) {
            throw new BizException(BasicCode.CUSTOM_ERROR,"只有主账号才有此操作权限");
        }
    }

    /**
     * 判断是否当前会员的子账号
     */
    private void isCurrMemberSubAccount(LoginInfo loginInfo, String accountId,String accountName){
        if (CsStringUtils.isBlank(accountId) && CsStringUtils.isBlank(accountName)) {
            throw  new BizException(BasicCode.CUSTOM_ERROR,"账号id或账号名不可为空");
        }
        AccountDTO accountDTO = CsStringUtils.isBlank(accountName) ? accountService.findById(accountId) : accountService.findByAccountName(accountName);

        if (!CsStringUtils.equals(accountDTO.getMemberId(), loginInfo.getMemberId())) {
            throw  new BizException(BasicCode.INVALID_PARAM,"该账户不是你的子账户");
        }
    }

    private LoginInfo getLoginInfo(AccountDTO accountDTO, HttpServletRequest request){

        LoginInfo loginInfo = new LoginInfo(accountDTO);
        loginInfo.setSessionId(request.getSession().getId());

        //默认buyerFlg为0
        loginInfo.setBuyerFlg(0);

        //默认为个人买家
        loginInfo.setRoleList(Lists.newArrayList(1));
        //一下两次查询可优化为并行处理
        MemberSimpleDTO memberSimpleDTO =  memberService.findMemberSimpleById(accountDTO.getMemberId());
        if( memberSimpleDTO != null ){
            loginInfo.setSellerFlg(memberSimpleDTO.getSellerFlg());
            loginInfo.setCarrierFlg(memberSimpleDTO.getCarrierFlg());
            loginInfo.setSupplierFlg(memberSimpleDTO.getSupplierFlg());

            loginInfo.setSellerType(memberSimpleDTO.getSellerType());
            loginInfo.setCarrierType(memberSimpleDTO.getCarrierType());
            loginInfo.setSupplierType(memberSimpleDTO.getSupplierType());
            loginInfo.setBuyerType(memberSimpleDTO.getBuyerType());
            loginInfo.setMemberShortName(memberSimpleDTO.getMemberShortName());
        }

        try {
            List<RoleDTO>  accountRole = roleService.getRoleByAccountId(accountDTO.getAccountId());
            if (CollectionKit.isNotEmpty(accountRole)) {
                loginInfo.setRoleList(accountRole.stream().map(RoleDTO::getRoleId).toList());
                if (accountRole != null) {
                    for (RoleDTO roleDTO : accountRole) {
                        if ("buyer3".equals(roleDTO.getRoleType())) {
                            loginInfo.setBuyerFlg(1);
                            break;
                        }
                    }

                }
                List<SaleRegionDTO> saleRegionDTOList = saleRegionService.findByAccountId(accountDTO.getAccountId());
                if (CollectionKit.isNotEmpty(saleRegionDTOList)) {
                    loginInfo.setSaleRegionIdList(saleRegionDTOList.stream().map(SaleRegionSampleDTO::getSaleRegionId).toList());
                }
                loginInfo.setAccountRegionAdCodeList(accountRegionService.findAdCodeByAccountId(accountDTO.getAccountId()));
            }
        }catch(Exception e){
            log.error("获取权限、数据权限、销售区域信息出错：{},",e.getMessage(),e);
        }
        //保存session  Spring HttpSession 自动保存session到redis
        request.getSession().setAttribute(LoginInfo.SESSION_NAME,loginInfo);

        log.info("sessionId : {}",loginInfo.getSessionId());

        return loginInfo;
    }
}
