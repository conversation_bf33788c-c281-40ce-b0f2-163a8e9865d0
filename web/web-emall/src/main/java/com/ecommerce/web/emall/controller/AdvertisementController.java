package com.ecommerce.web.emall.controller;

import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.exception.BusinessOperationException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.goods.api.dto.ResourceDTO;
import com.ecommerce.goods.api.service.IResourceService;
import com.ecommerce.information.api.dto.advertisement.AdvertisementAndBusinessInfoDTO;
import com.ecommerce.information.api.dto.advertisement.AdvertisementAndBusinessInfoQueryDTO;
import com.ecommerce.information.api.dto.news.NewsDTO;
import com.ecommerce.information.api.dto.shop.ShopInfoDTO;
import com.ecommerce.information.api.service.IAdvertisementService;
import com.ecommerce.information.api.service.IAnnouncementService;
import com.ecommerce.information.api.service.INewsService;
import com.ecommerce.information.api.service.INewsTechnologyEquipmentService;
import com.ecommerce.information.api.service.IPurchaseNoticeService;
import com.ecommerce.information.api.service.IShopService;
import com.ecommerce.member.api.dto.member.MemberDetailDTO;
import com.ecommerce.member.api.service.IMemberService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 广告服务
 *
 * <AUTHOR>
*/
@Slf4j
@Tag(name = "Advertisement", description = "广告服务")
@RestController
@RequestMapping("/ad/anon")
public class AdvertisementController {

   @Autowired
   private IAdvertisementService iAdvertisementService;
    @Autowired
    private INewsService newsService;
    @Autowired
    private IResourceService resourceService;
    @Autowired
    private IAnnouncementService announcementService;
    @Autowired
    private INewsTechnologyEquipmentService newsTechnologyEquipmentService;
    @Autowired
    private IPurchaseNoticeService purchaseNoticeService;
    @Autowired
    private IMemberService memberService;
    @Autowired
    private IShopService iShopService;

    private static final String IMG_URL = "imgUrl";
    private static final String REDEACT_URL = "redeactUrl";
    private static final String AD_RESOURCE_NOT_EXIST = "该广告位关联资源不存在";
    private static final String TYPE_NOT_SUPPORT = "该类型暂不支持";

    @Operation(summary = "通过广告位置code查询广告")
    @PostMapping(value="/findByCode")
    public ItemResult<List<AdvertisementAndBusinessInfoDTO>> findBySpaceCode(String code){
        List<AdvertisementAndBusinessInfoDTO> list = Lists.newArrayList();
        AdvertisementAndBusinessInfoDTO dto = new AdvertisementAndBusinessInfoDTO();
        list.add(dto);
        return new ItemResult<>(list);
    }

    @Operation(summary = "通过广告位编码集合查询广告")
    @PostMapping(value="/findAdvertisementAndBusinessInfoBySpaceCode")
    public ItemResult<List<AdvertisementAndBusinessInfoDTO>> findAdvertisementAndBusinessInfoBySpaceCode(@RequestBody AdvertisementAndBusinessInfoQueryDTO dto){
        List<AdvertisementAndBusinessInfoDTO> list = iAdvertisementService.findAdvertisementAndBusinessInfoBySpaceCode(dto);
        if(CollectionUtils.isNotEmpty(list)){
            list.forEach(item->{
                item.setBusinessDTO(findBusinessInfo(item,item.getAdBusinessId()));
            });
            return new ItemResult<>(list);
        }
        return new ItemResult<>(Lists.newArrayList());
    }

    private Object findBusinessInfo(AdvertisementAndBusinessInfoDTO dto, String businessId){
        /*if(CsStringUtils.isBlank(businessId) || dto == null || CsStringUtils.isBlank(dto.getSpaceDTO().getAdRange())){
            return null;
        }*/
        if (dto == null) {
            return Maps.newHashMap();
        }
        //根据具体业务类型返回对应业务类型的dto,集值：AD_TYPE
        //AD_TYPE_01　　商品
        //AD_TYPE_02　　资讯信息
        //AD_TYPE_03　　公告
        //AD_TYPE_04　　技术装备
        //AD_TYPE_05　　卖家
        //AD_TYPE_06　　供应商
        //AD_TYPE_07　　承运商
        //AD_TYPE_08　　供应信息
        //AD_TYPE_09　　招标信息
        //AD_TYPE_10　　采购信息
        //AD_TYPE_11    店铺
        //AD_TYPE_12    单图片
        switch (dto.getAdType()) {
            case "AD_TYPE_01" :{
                Map<String , Object> map= new HashMap<>();
                ResourceDTO resourceDTO = resourceService.getResourceDetail(businessId);
                if(null != resourceDTO){
                    BigDecimal price = resourceDTO.getPrice();
                    if(price == null){
                        log.error("该广告位关联资源价格为空,businessId:{}",businessId);
                        throw new BizException(BasicCode.UNDEFINED_ERROR,"该广告位关联资源价格为空");
                    }
                    map.put("title",resourceDTO.getGoodsName());
                    map.put("des",resourceDTO.getGoodsDescribe());
                    map.put("money",price.setScale(2, RoundingMode.HALF_UP));
                    map.put("unit",resourceDTO.getSaleUnit());
                    map.put("brand",resourceDTO.getGoodsDTO().getBrand());
                    map.put("star","5");
                    map.put(IMG_URL,dto.getAdImageUrl());
                    map.put(REDEACT_URL,dto.getAdResourceUrl());
                    return  map;
                }
                throw new BizException(BasicCode.UNDEFINED_ERROR,AD_RESOURCE_NOT_EXIST);
            }
            case "AD_TYPE_02" :{
                Map<String , Object> map= new HashMap<>();
                NewsDTO newsDTO = newsService.findById(businessId);
                if(null != newsDTO){
                    map.put("title",newsDTO.getTitle());
                    map.put(IMG_URL,newsDTO.getPictureUrl());
                    map.put("updateTime",newsDTO.getUpdateTime());
                    map.put("newsText",newsDTO.getContent());
                    return map;
                }
                log.error("该广告位关联资源不存在,businessId:{}",businessId);
                throw new BizException(BasicCode.UNDEFINED_ERROR,AD_RESOURCE_NOT_EXIST);
            }
            case "AD_TYPE_03" :{
                return announcementService.findById(businessId);
            }
            case "AD_TYPE_04" :{
                return newsTechnologyEquipmentService.findById(businessId);
            }
            case "AD_TYPE_05" :{
                throw new BizException(BasicCode.UNDEFINED_ERROR,TYPE_NOT_SUPPORT);
            }
            case "AD_TYPE_06" :{
                MemberDetailDTO memberDetailDTO = memberService.findMemberById(businessId);
                Map<String , String> map= new HashMap<>();
                if(null != memberDetailDTO){
                    map.put("majorBusiness",memberDetailDTO.getMainProducts());
                    map.put("memberName",memberDetailDTO.getMemberName());
                    map.put("contacts",memberDetailDTO.getContactName());
                    map.put("contactPhone",memberDetailDTO.getContactPhone());
                    map.put("address",memberDetailDTO.getAddressDetail());
                    map.put(IMG_URL,dto.getAdImageUrl());
                    map.put(REDEACT_URL,dto.getAdResourceUrl());
                    return map;
                }
                log.error("该广告位关联资源不存在,businessId:{}",businessId);
                throw new BizException(BasicCode.UNDEFINED_ERROR,AD_RESOURCE_NOT_EXIST);
            }
            case "AD_TYPE_07" :{
                MemberDetailDTO memberDetailDTO = memberService.findMemberById(businessId);
                Map<String , String> map= new HashMap<>();
                if(null != memberDetailDTO){
                    map.put("companyName",memberDetailDTO.getMemberName());
                    map.put("phone",memberDetailDTO.getContactPhone());
                    map.put(IMG_URL,dto.getAdImageUrl());
                    map.put(REDEACT_URL,dto.getAdResourceUrl());
                    return map;
                }
                log.error("该广告位关联资源不存在,businessId:{}",businessId);
                throw new BizException(BasicCode.UNDEFINED_ERROR,AD_RESOURCE_NOT_EXIST);
            }
            case "AD_TYPE_08" :{
                throw new BizException(BasicCode.UNDEFINED_ERROR,TYPE_NOT_SUPPORT);
            }
            case "AD_TYPE_09" :{
                return purchaseNoticeService.queryDetailById(businessId);
            }
            case "AD_TYPE_10" :{
                throw new BizException(BasicCode.UNDEFINED_ERROR,TYPE_NOT_SUPPORT);
            }
            case "AD_TYPE_11" :{
                Map<String , String> map= new HashMap<>();
                ShopInfoDTO shopInfoDTO = iShopService.getShopInfo(businessId);
                if(shopInfoDTO != null){
                    map.put(IMG_URL,dto.getAdImageUrl());
                    map.put("name",shopInfoDTO.getShopName());
                    map.put("point","5");
                    map.put(REDEACT_URL,dto.getAdResourceUrl());
                    return map;
                }
                log.error("该广告位关联资源不存在,businessId:{}",businessId);
                throw new BizException(BasicCode.UNDEFINED_ERROR,AD_RESOURCE_NOT_EXIST);
            }
            case "AD_TYPE_12" :{
                Map<String , String> map= new HashMap<>();
                map.put(IMG_URL,dto.getAdImageUrl());
                map.put(REDEACT_URL,dto.getAdResourceUrl());
                return map;
            }
            default:{
                throw new BizException(BasicCode.UNDEFINED_ERROR,TYPE_NOT_SUPPORT);
            }
        }
    }


}
