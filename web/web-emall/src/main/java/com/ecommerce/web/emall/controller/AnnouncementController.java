package com.ecommerce.web.emall.controller;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.information.api.dto.announcement.AnnouncementDTO;
import com.ecommerce.information.api.dto.announcement.AnnouncementIndexQueryDTO;
import com.ecommerce.information.api.dto.announcement.AnnouncementQueryDTO;
import com.ecommerce.information.api.service.IAnnouncementService;
import com.ecommerce.member.api.dto.account.AccountSimpleDTO;
import com.ecommerce.member.api.service.IAccountService;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 公告服务接口
 *
 * <AUTHOR>
 */
@Slf4j
@Tag(name = "Announcement", description = "公告服务")
@RestController
@RequestMapping("/announcement/anon")
public class AnnouncementController {

    @Autowired
    private IAnnouncementService iAnnouncementService;
    @Autowired
    private IAccountService accountService;

    @Operation(summary = "查询最近几条公告，不分类型")
    @PostMapping(value="/listAnno")
    public ItemResult<List<AnnouncementDTO>> listAnno(@Parameter(name = "size", description = "公告条数") Integer size){
        return list(size,null);
    }

    @Operation(summary = "查询默认公告")
    @PostMapping(value="/listAnno1")
    public ItemResult<List<AnnouncementDTO>> listAnno1(@Parameter(name = "size", description = "公告条数") Integer size){
        return list(size,0);
    }
    @Operation(summary = "查询弹出公告")
    @PostMapping(value="/listAnno2")
    public ItemResult<List<AnnouncementDTO>> listAnno2(@Parameter(name = "size", description = "公告条数") Integer size){
        return list(size,1);
    }
    @Operation(summary = "查询轮播公告")
    @PostMapping(value="/listAnno3")
    public ItemResult<List<AnnouncementDTO>> listAnno3(@Parameter(name = "size", description = "公告条数") Integer size){
        return list(size,2);
    }

    private ItemResult<List<AnnouncementDTO>> list(Integer size,Integer type){
        //公告类型 0 默认  1 弹框公告 2轮播公告
        AnnouncementIndexQueryDTO query = new AnnouncementIndexQueryDTO();
        query.setAnnouncementType(type);
        query.setSize(size == null || size < 3 ? 3 : size);
        return new ItemResult<>(iAnnouncementService.findForIndex(query));
    }

    @Operation(summary = "按条件翻页查询公告")
    @PostMapping(value="/list")
    public ItemResult<PageInfo<AnnouncementDTO>> findAll(@RequestBody AnnouncementQueryDTO query){
        //审批通过
        query.setApprovalStatus(2);
        //可用
        query.setAvailableStatus(0);
        return new ItemResult<>(iAnnouncementService.findAll(query));
    }

    @Operation(summary = "查询单条公告详情(不含审批记录)")
    @PostMapping(value="/findById")
    public ItemResult<AnnouncementDTO> findById(@Parameter(name = "id", description = "公告Id") @RequestParam String id){
        AnnouncementDTO anno = iAnnouncementService.findById(id);
        if(anno.getApprovalStatus() == null || anno.getApprovalStatus().intValue() != 2 ||
                anno.getAvailableStatus() == null || anno.getAvailableStatus().intValue() != 0 ||
                anno.getDelFlg() == null || anno.getDelFlg().intValue() == 1 ){
            log.info("状态不正确，返回null");
            return new ItemResult<>(null);
        }
        if (CsStringUtils.isNotBlank(anno.getCreateUser())) {
            AccountSimpleDTO accountSimpleDTO = accountService.findSimpleById(anno.getCreateUser());
            if( accountSimpleDTO != null ){
                anno.setCreateUser(CsStringUtils.isNotBlank(accountSimpleDTO.getRealName()) ? accountSimpleDTO.getRealName() : accountSimpleDTO.getAccountName());
            }
        }
        return new ItemResult<>(anno);
    }
}
