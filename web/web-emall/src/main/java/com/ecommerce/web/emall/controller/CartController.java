package com.ecommerce.web.emall.controller;

import com.alibaba.fastjson.JSON;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.exception.BusinessOperationException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.goods.api.dto.GoodsDTO;
import com.ecommerce.goods.api.dto.ResourceDTO;
import com.ecommerce.goods.api.enums.ResourceStatusEnum;
import com.ecommerce.goods.api.service.IGoodsService;
import com.ecommerce.goods.api.service.IResourceService;
import com.ecommerce.member.api.session.LoginInfo;
import com.ecommerce.order.api.dto.CartResourceDTO;
import com.ecommerce.order.api.dto.OrderCartResCheckDTO;
import com.ecommerce.order.api.service.ICartResourceService;
import com.ecommerce.order.api.service.IOrderService;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;


/**
 * 购物车管理
 *
 * <AUTHOR>
*/

@Slf4j
@Tag(name = "CartController", description = "购物车管理")
@RestController
@RequestMapping("/cart")
public class CartController {

    @Autowired
    private ICartResourceService iCartResourceService;
    @Autowired
    private IResourceService resourceService;
    @Autowired
    private IGoodsService goodsService;
    @Autowired
    private IOrderService orderService;

    private static final String SELLER = "seller";
    private static final String STORE = "store";

   @Operation(summary = "查询购物车")
   @PostMapping(value="/queryBuyerCart")
   public ItemResult<List<CartResourceDTO>> queryBuyerCart(@Parameter(hidden = true)LoginInfo loginInfo,
                                                           @Parameter(name = "manageModel", description = "管理方式 1共享,2不共享,null不区分") String manageModel){
	  log.info("/queryBuyerCart-accountId:{} manageModel:{}", loginInfo.getAccountId(), manageModel);
      return iCartResourceService.queryCart(loginInfo.getAccountId(), manageModel);
   }


   @Operation(summary = "移除购物车,部分移除")
   @PostMapping(value="/moveCart")
   public ItemResult<String> moveCart(@Parameter(hidden = true)LoginInfo loginInfo, @RequestBody List<String> cartResIds){
	  log.info("/moveCart-cartResIds:{} operatorId:{}", cartResIds, loginInfo.getAccountId());
      return iCartResourceService.moveCart(cartResIds, loginInfo.getAccountId());
   }


   @Operation(summary = "修改购物车")
   @PostMapping(value="/updateCart")
   public ItemResult<String> updateCart(@Parameter(hidden = true)LoginInfo loginInfo, @RequestBody CartResourceDTO cartDTO){
	  log.info("/updateCart-cartDTO:{} operatorId:{}", JSON.toJSONString(cartDTO), loginInfo.getAccountId());
      return iCartResourceService.updateCart(cartDTO, loginInfo.getAccountId());
   }

   @Operation(summary = "查询购物车详情")
   @PostMapping(value="/queryCartDetail")
   public ItemResult<CartResourceDTO> queryCartDetail(@Parameter(name = "cartResId", description = "购物车ID") String cartResId){
      return iCartResourceService.queryCartDetail(cartResId);
   }


   @Operation(summary = "添加购物车")
   @PostMapping(value="/addCart")
   public ItemResult<CartResourceDTO> addCart(@Parameter(hidden = true)LoginInfo loginInfo, @RequestBody CartResourceDTO cartDTO){
	  log.info("/addCart-cartDTO:{} operatorId:{}", JSON.toJSONString(cartDTO), loginInfo.getAccountId());
	  cartDTO.setBuyerId(loginInfo.getMemberId());
	  return iCartResourceService.addCart(cartDTO, loginInfo.getAccountId());
   }


   @Operation(summary = "清空购物车,全部清空")
   @PostMapping(value="/emptyBuyerCart")
   public ItemResult<String> emptyBuyerCart(@Parameter(hidden = true)LoginInfo loginInfo){
	  log.info("/emptyBuyerCart-accountId:{}", loginInfo.getAccountId());
      return iCartResourceService.emptyCart(loginInfo.getAccountId());
   }


   @Operation(summary = "购物车数量变更/验证/增加/减少")
   @PostMapping(value="/updateQuantity")
   public ItemResult<String> updateQuantity(@Parameter(hidden = true)LoginInfo loginInfo,
                                            @Parameter(name = "cartResId", description = "购物车id") String cartResId,
                                            @Parameter(name = "quantity", description = "数量") @RequestParam BigDecimal quantity){
	  log.info("/updateQuantity-cartResId:{} quantity:{} operatorId:{}", cartResId, quantity, loginInfo.getAccountId());
	  return iCartResourceService.updateQuantity(cartResId, quantity, loginInfo.getAccountId());
   }


   @Operation(summary = "购物车结算检查")
   @PostMapping(value="/checkCartResource")
   public ItemResult<String> checkCartResource(@RequestBody List<OrderCartResCheckDTO> cartResIds){
       log.info("/checkCartResource-cartIds:{}", cartResIds);
       Map<String,String> checker = new HashMap<>();
       Set<String> interPayWaySet = null;//支持的支付方式
       Set<String> interDeliveryWaySet = null;//支持的支付方式
       Set<String> interCarrySet = null;//支持的搬运
       if (CollectionUtils.isNotEmpty(cartResIds)) {
           List<String> resourceIds = Lists.newArrayList();
           for (OrderCartResCheckDTO cartResCheck : cartResIds) {
               BigDecimal quantity = cartResCheck.getQuantity();
               CartResourceDTO cartResource = iCartResourceService.queryCartDetail(cartResCheck.getCartResourceId()).getData();
               //商品失效无法购买
               if (cartResource == null || Boolean.TRUE.equals(cartResource.getDelFlg())) {
                   throw new BizException(BasicCode.UNDEFINED_ERROR, "商品已失效");
               }
               String resourceId = cartResource.getResourceId();
               ResourceDTO resource = resourceService.getResourceDetail(resourceId);
               if (resource == null || (resource.getDelFlg() != null && resource.getDelFlg()) ||
                       !ResourceStatusEnum.RES_STATUS100.code().equals(resource.getStatus())) {
                   throw new BizException(BasicCode.UNDEFINED_ERROR, "商品已失效");
               }

               //支持的支付方式
               Set<String> payWaySet = Sets.newHashSet(resource.getPayWay());
               if (interPayWaySet == null) {
                   interPayWaySet = payWaySet;
               } else {
                   interPayWaySet = Sets.intersection(interPayWaySet, payWaySet);//交集;
               }
               if (CollectionUtils.isEmpty(interPayWaySet)) {
                   throw new BizException(BasicCode.UNDEFINED_ERROR, "所选商品支付方式不一致，不能一起购买");
               }
               //支持的配送方式
               Set<String> deliveryWaySet = Sets.newHashSet();
               if (resource.getIfTakeSelf()) {
                   deliveryWaySet.add("1");
               }
               if (resource.getIfPlatformDelivery()) {
                   deliveryWaySet.add("2");
               }
               if (resource.getIfSellerDelivery()) {
                   deliveryWaySet.add("3");
               }
               if (interDeliveryWaySet == null) {
                   interDeliveryWaySet = deliveryWaySet;
               } else {
                   interDeliveryWaySet = Sets.intersection(interDeliveryWaySet, deliveryWaySet);//交集;
               }
               if (CollectionUtils.isEmpty(interDeliveryWaySet)) {
                   throw new BizException(BasicCode.UNDEFINED_ERROR, "所选商品配送方式不一致，不能一起购买");
               }
               //搬运费计算方式
               Set<String> carrySet = Sets.newHashSet();
               GoodsDTO goodsInfo = goodsService.getGoodsInfo(resource.getGoodsId()).getData();
               carrySet.add(goodsInfo.getCartageRule());
               if (interCarrySet == null) {
                   interCarrySet = carrySet;
               } else {
                   interCarrySet = Sets.intersection(interCarrySet, carrySet);//交集;
               }
               if(CollectionUtils.isEmpty(interCarrySet)){
                   throw new BizException(BasicCode.UNDEFINED_ERROR, "所选商品搬运费计算方式不一致，不能一起购买");
               }
               //与任何其他商品都必须拆分
               if (goodsService.ifSingleBuy(resource.getGoodsId()).getData()) {
                   if (cartResIds.size() > 1) {
                       throw new BizException(BasicCode.UNDEFINED_ERROR, "[" + resource.getResourceName() + "]不能与其他商品一起购买，请分开结算");
                   }
               }
               //购买单位不为计量单位时转换购买量
               if (!CsStringUtils.equals(cartResource.getCountUnit(), resource.getPriceUnit())) {
                   quantity = quantity.multiply(resource.getConvertRate());
               }
               if (cartResCheck.isCheckResNum()) {
                   if (resource.getCansaleNum() == null || resource.getCansaleNum().compareTo(quantity) < 0) {
                       throw new BizException(BasicCode.UNDEFINED_ERROR, "商品["+resource.getResourceName()+"]可售数量不足");
                   }

                   //单笔最大购买量
                   if (resource.getOrdermaxNum() != null && resource.getOrdermaxNum().compareTo(quantity) < 0) {
                       throw new BizException(BasicCode.UNDEFINED_ERROR,
                               "商品["+resource.getResourceName()+"]单笔最大购买量为"
                                       + resource.getOrdermaxNum().setScale(2, RoundingMode.HALF_UP) + resource.getPriceUnit());
                   }

                   //单笔最小购买量
                   if (resource.getOrderminNum() != null && resource.getOrderminNum().compareTo(quantity)>0) {
                       throw new BizException(BasicCode.UNDEFINED_ERROR,
                               "商品["+resource.getResourceName()+"]单笔最小购买量为"
                                       + resource.getOrderminNum().setScale(2, RoundingMode.HALF_UP) + resource.getPriceUnit());
                   }

                   //单日最大购买量
                   if (resource.getDaymaxNum() != null) {
                       BigDecimal ymQuantity = orderService.getBuyerResDayQuantity(
                               cartResource.getBuyerId(), resourceId).getData();
                       if(resource.getDaymaxNum().compareTo(ymQuantity.add(quantity)) < 0){
                           throw new BizException(BasicCode.UNDEFINED_ERROR,
                                   "商品["+resource.getResourceName()+"]单日最大购买量为"
                                           + resource.getDaymaxNum().setScale(2, RoundingMode.HALF_UP)
                                           + resource.getPriceUnit() + ", 本日已购买数量为" + ymQuantity + resource.getPriceUnit());
                       }
                   }

                   resourceIds.add(resourceId);
               }
               String sellerId = cartResource.getSellerId();
               String storeId = cartResource.getStoreId();

               if (checker.get(SELLER) != null && !sellerId.equals(checker.get(SELLER)) && checker.get(STORE) != null && !storeId.equals(checker.get(STORE))){
                   throw new BizException(BasicCode.UNDEFINED_ERROR, "所选商品仓库及卖家不一致，不能一起购买");
               }

               if (checker.get(SELLER) != null && !sellerId.equals(checker.get(SELLER))){
                   throw new BizException(BasicCode.UNDEFINED_ERROR, "所选商品卖家不一致，不能一起购买");
               } else {
                   checker.put(SELLER, sellerId);
               }

               if (checker.get(STORE) != null && !storeId.equals(checker.get(STORE))) {
                   throw new BizException(BasicCode.UNDEFINED_ERROR, "所选商品仓库不一致，不能一起购买");
               } else {
                   checker.put(STORE, storeId);
               }
           }
       }
       return new ItemResult<>("OK");
   }

}
