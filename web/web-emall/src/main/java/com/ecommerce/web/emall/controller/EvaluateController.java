package com.ecommerce.web.emall.controller;


import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.common.result.ItemResult;
import com.ecommerce.information.api.dto.memberIntegral.MemberIntegralDTO;
import com.ecommerce.information.api.service.IMemberIntegralService;
import com.ecommerce.order.api.constant.ConstantEvaluate;
import com.ecommerce.order.api.dto.EvaluateSellerScoreResDTO;
import com.ecommerce.order.api.dto.GoodsEvaluateGmbResDTO;
import com.ecommerce.order.api.dto.GoodsEvaluateListReqDTO;
import com.ecommerce.order.api.dto.GoodsEvaluateListResDTO;
import com.ecommerce.order.api.service.IEvaluateService;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 * @Date: 2019/4/30 2:15 PM
 * @Description 买家调用评价相关接口
 */
@RestController
@RequestMapping("/evaluate/anon")
@Slf4j
@Tag(name = "Evaluate", description = "评价相关接口类")
public class EvaluateController {

    @Autowired
    IEvaluateService iEvaluateService;

    @Autowired
    protected IMemberIntegralService iMemberIntegralService;

    @Operation(summary = "根据店铺ID获取店铺综合评分")
    @PostMapping(value="/getSellerCompreScoreInfo")
    public ItemResult<EvaluateSellerScoreResDTO> getSellerCompreScoreInfo(@Parameter(name = "sellerId", description = "卖家Id") @RequestParam String sellerId){
        return iEvaluateService.getSellerCompreScoreInfo(sellerId);
    }

    @Operation(summary = "根据商品ID获取好中差评数量")
    @PostMapping(value="/getGoodsEvaluateCompreScoreInfo")
    public ItemResult<GoodsEvaluateGmbResDTO> getGoodsEvaluateCompreScoreInfo(@Parameter(name = "goodsId", description = "商品Id") @RequestParam String goodsId){
        return iEvaluateService.getGoodsEvaluateCompreScoreInfo(goodsId);
    }

    @Operation(summary = "根据商品ID分页获取评价列表")
    @PostMapping(value="/getGoodsEvaluateList")
    public ItemResult<PageInfo<GoodsEvaluateListResDTO>> getGoodsEvaluateList(@RequestBody GoodsEvaluateListReqDTO req){
        ItemResult<PageInfo<GoodsEvaluateListResDTO>> pageInfoItemResult=iEvaluateService.getGoodsEvaluateList(req);
        PageInfo<GoodsEvaluateListResDTO> goodsEvaluateListResDTOPageInfo=pageInfoItemResult.getData();
        if(CollectionUtils.isNotEmpty(goodsEvaluateListResDTOPageInfo.getList())){
            for (GoodsEvaluateListResDTO dto:goodsEvaluateListResDTOPageInfo.getList()) {
                //拼接买家等级
                MemberIntegralDTO memberIntegralDTO = this.findByMemberId(dto.getBuyerId());
                if(memberIntegralDTO == null ||
                        memberIntegralDTO.getMemberLevel() == null ||
                        memberIntegralDTO.getMemberLevel().equals(0)){
                    dto.setMemberLevel(ConstantEvaluate.NORMALMEMBER);
                }else{
                    dto.setMemberLevel(memberIntegralDTO.getMemberLevel());
                }
            }
        }
        return pageInfoItemResult;
    }
    private MemberIntegralDTO findByMemberId(String memberId){
        try{
            MemberIntegralDTO memberIntegralDTO = iMemberIntegralService.findByMemberId(memberId);
            if(memberIntegralDTO == null){
                log.error("订单中心-评价-没有用户等级信息 memberId = {}",memberId);
                return null;
            }
            return memberIntegralDTO;
        }catch (Exception e){
            log.info("订单中心-评价-获取用户等级信息异常，异常信息：{}",e.getMessage());
            return null;
        }
    }

}
