package com.ecommerce.web.emall.controller;


import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.common.result.ItemResult;
import com.ecommerce.goods.api.dto.GoodsAddItemDTO;
import com.ecommerce.goods.api.service.IGoodsAddItemService;
import com.ecommerce.member.api.session.LoginInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * @Auther: chenjun
 * @Description:
 * @Date: 05/09/2018 10:37
 */
@Slf4j
@Tag(name = "GoodsAddItemController", description = "商家加价项")
@RestController
@RequestMapping("/goodsAdditem")
public class GoodsAddItemController {
    @Autowired
    private IGoodsAddItemService iGoodsAddItemService;

    @Operation(summary = "根据商品分类查询商家模板")
    @GetMapping(value="/anon/listBusinessTemplate")
    public ItemResult<Object> listBusinessTemplate(@Parameter(name = "memberId", description = "会员ID") @RequestParam String memberId,
                                           @Parameter(name = "goodsTypeCode", description = "商品分类Code") @RequestParam Integer goodsTypeCode){
        return new  ItemResult<>(iGoodsAddItemService.listBusinessTemplate(goodsTypeCode,memberId));
    }

    @Operation(summary = "根据商品分类查询商家模板")
    @GetMapping(value="/anon/listBusinessTemplateByResource")
    public ItemResult<Object> listBusinessTemplateByResource(@Parameter(name = "resourceId", description = "资源Id") @RequestParam String resourceId,
                                                     @Parameter(name = "memberId", description = "会员Id") @RequestParam String memberId)
            throws BusinessOperationException{
        return new  ItemResult<>(iGoodsAddItemService.listBusinessTemplateByResource(resourceId,memberId));
    }


    @Operation(summary = "商家修改模板，list中可以放1条或者多条数据，根据主键修改")
    @PostMapping(value="/businessUpdateTemplate")
    public ItemResult<Object> businessUpdateTemplate(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody List<GoodsAddItemDTO> itemValueData){
        String memberId = loginInfo.getMemberId();
        return new  ItemResult<>(iGoodsAddItemService.businessUpdateTemplate(itemValueData,memberId));
    }


    @Operation(summary = "商家创建模板")
    @PostMapping(value="/businessCreateTemplate")
    public ItemResult<Object> businessCreateTemplate(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody List<GoodsAddItemDTO> itemValueData){
        String memberId = loginInfo.getMemberId();
        return new  ItemResult<>(iGoodsAddItemService.businessCreateTemplate(itemValueData,memberId,memberId));
    }


    @Operation(summary = "通过addItemId加价项，多项查询以逗号分开")
    @GetMapping(value="/getGoodsAddItems")
    public ItemResult<Object> getGoodsAddItems(@Parameter(name = "addItemIds", description = "加价项Id") @RequestParam String addItemIds){
        return new  ItemResult<>(iGoodsAddItemService.getGoodsAddItems(addItemIds));

    }


    @Operation(summary = "根据商品分类查询平台模板")
    @GetMapping(value="/anon/listBaseTemplate")
    public ItemResult<Object> listBaseTemplate(@Parameter(name = "goodsTypeCode", description = "商品类型code") @RequestParam Integer goodsTypeCode){
        return new  ItemResult<>(iGoodsAddItemService.listBaseTemplate(goodsTypeCode));
    }
}
