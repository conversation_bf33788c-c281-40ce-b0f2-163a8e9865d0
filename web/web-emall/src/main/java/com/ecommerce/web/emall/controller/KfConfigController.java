package com.ecommerce.web.emall.controller;


import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.common.result.ItemResult;
import com.ecommerce.information.api.dto.kf.KfConfigCondDTO;
import com.ecommerce.information.api.service.IKfConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 客服配置信息服务
 *
 * <AUTHOR>
*/

@RestController
@Tag(name = "KfConfig", description = "客服配置信息服务")
@RequestMapping("/kfConfig")
public class KfConfigController {

   @Autowired 
   private IKfConfigService iKfConfigService;

   @Operation(summary = "获取客服url")
   @PostMapping(value="/anon/getKfUrl")
   public ItemResult<String> getKfUrl(@RequestBody KfConfigCondDTO kfConfigCondDTO){
      return iKfConfigService.getKfUrl(kfConfigCondDTO);
   }



}
