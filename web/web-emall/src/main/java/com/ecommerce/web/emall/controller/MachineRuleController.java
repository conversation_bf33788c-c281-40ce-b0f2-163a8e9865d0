package com.ecommerce.web.emall.controller;



import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.common.result.ItemResult;
import com.ecommerce.member.api.session.LoginInfo;
import com.ecommerce.price.api.dto.CalculateResultDTO;
import com.ecommerce.price.api.dto.machine.MachineCalculatePriceDTO;
import com.ecommerce.price.api.dto.machine.MachineRuleDTO;
import com.ecommerce.price.api.dto.machine.MachineRuleQueryDTO;
import com.ecommerce.price.api.service.IMachineFeeService;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.List;


/**
 * @Created锛�Fri May 31 11:39:41 CST 2019
 * <AUTHOR>
 * @Version:2
 * @Description:台班费业务规则service
 */

@RestController
@Tag(name = "MachineRule", description = "台班费业务规则service")
@RequestMapping("/machineRule")
public class MachineRuleController {

    @Autowired
    private IMachineFeeService iMachineFeeService;

    @Operation(summary = "新增台班费业务规则")
    @PostMapping(value="/addMachineRule")
    public ItemResult<Object> addMachineRule(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody MachineRuleDTO addMachineRuleDTO){
        addMachineRuleDTO.setSellerId(loginInfo.getMemberId());
        addMachineRuleDTO.setSellerName(loginInfo.getMemberName());
        addMachineRuleDTO.setOperatorId(loginInfo.getAccountId());
        iMachineFeeService.addPriceRule(addMachineRuleDTO);
        return new ItemResult<>(true);
    }


    @Operation(summary = "分页查询所有台班费业务规则详情")
    @PostMapping(value="/getMachineRuleList")
    public ItemResult<PageInfo<MachineRuleDTO>> getMachineRuleList(LoginInfo loginInfo, @RequestBody MachineRuleQueryDTO machineRuleDTO){
        return iMachineFeeService.pagePriceRule(machineRuleDTO);
    }


    @Operation(summary = "查询台班费业务规则详情")
    @PostMapping(value="/getMachineRuleDetail")
    public ItemResult<MachineRuleDTO> getMachineRuleDetail(@Parameter(name = "machineRuleId", description = "台班费规则Id") @RequestParam String machineRuleId){
        return iMachineFeeService.findPriceRuleById(machineRuleId);
    }


    @Operation(summary = "查询所有台班费业务规则详情")
    @PostMapping(value="/getMachineRuleBySellerID")
    public ItemResult<List<MachineRuleDTO>> getMachineRuleBySellerID(@Parameter(hidden = true)LoginInfo loginInfo){
        MachineRuleQueryDTO dto = new MachineRuleQueryDTO();
        dto.setSellerId(loginInfo.getMemberId());
        return iMachineFeeService.findPriceRule(dto);
    }


    @Operation(summary = "编辑台班费业务规则")
    @PostMapping(value="/updateMachineRule")
    public ItemResult<Object> updateMachineRule(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody MachineRuleDTO machineRuleDTO){
        machineRuleDTO.setOperatorId(loginInfo.getAccountId());
        iMachineFeeService.updatePriceRule(machineRuleDTO);
        return new ItemResult<>(new Object());
    }


    @Operation(summary = "删除台班费业务规则")
    @PostMapping(value="/deleteMachineRule")
    public ItemResult<Object> deleteMachineRule(@Parameter(hidden = true) LoginInfo loginInfo, @Parameter(name = "machineRuleId", description = "台班费规则Id") @RequestParam String machineRuleId){
        iMachineFeeService.deletePriceRule(machineRuleId,loginInfo.getAccountId());
        return new ItemResult<>(new Object());
    }

    @Operation(summary = "计算台班费业务规则")
    @PostMapping(value="/calculationMachineRule")
    public ItemResult<BigDecimal> calculationMachineRule(@Parameter(name = "machineRuleId", description = "台班费规则Id") @RequestParam String machineRuleId,@Parameter(name = "amount", description = "数量") @RequestParam BigDecimal amount){
        MachineCalculatePriceDTO dto = new MachineCalculatePriceDTO();
        dto.setQuantity(amount);
        dto.setMachineRuleId(machineRuleId);
        ItemResult<CalculateResultDTO> result = iMachineFeeService.calculateAmount(dto);
        return new ItemResult<>(result.getData().getAmount());
    }

    @Operation(summary = "台班费ID查询台班费集合")
    @PostMapping(value="/getMachineRuleListByID")
    public ItemResult<List<MachineRuleDTO>> getMachineRuleListByID(@RequestBody List<String> machineRuleId){
        return iMachineFeeService.getMachineRuleListByIds(machineRuleId);
    }
}
