package com.ecommerce.web.emall.controller;


import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.common.result.ItemResult;
import com.ecommerce.information.api.dto.news.NewsDTO;
import com.ecommerce.information.api.service.INewsAssemblyBuildingService;
import com.google.common.collect.Lists;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 *
 * <AUTHOR>
 */

@RestController
@Tag(name = "NewsAssemblyBuilding", description = "装备式建筑")
@RequestMapping("/newsAssemblyBuilding/anon")
public class NewsAssemblyBuildingController {

    //装配式建筑
    @Autowired
    private INewsAssemblyBuildingService newsAssemblyBuildingService;


    @Operation(summary = "投资开发")
    @GetMapping(value = "/investProjectNews")
    public ItemResult<List<NewsDTO>> findByInvest(@Parameter(name = "size", description = "显示数量") @RequestParam(defaultValue = "10") Integer size) {
        return new ItemResult<>(newsAssemblyBuildingService.investProjectNews(size));
    }

    @Operation(summary = "工业化生产")
    @GetMapping(value = "/prodProjectNews")
    public ItemResult<List<NewsDTO>> findByProd(@Parameter(name = "size", description = "显示数量") @RequestParam(defaultValue = "10") Integer size) {
        return new ItemResult<>(newsAssemblyBuildingService.prodProjectNews(size));
    }

    @Operation(summary = "标准化设计")
    @GetMapping(value = "/designProjectNews")
    public ItemResult<List<NewsDTO>> findByDesign(@Parameter(name = "size", description = "显示数量") @RequestParam(defaultValue = "10") Integer size) {
        return new ItemResult<>(newsAssemblyBuildingService.designProjectNews(size));
    }

    @Operation(summary = "装配化施工")
    @GetMapping(value = "/buildProjectNews")
    public ItemResult<List<NewsDTO>> findByBuild(@Parameter(name = "size", description = "显示数量") @RequestParam(defaultValue = "10") Integer size) {
        return new ItemResult<>(newsAssemblyBuildingService.buildProjectNews(size));
    }

    @Operation(summary = "一体化装修")
    @GetMapping(value = "/decorateProjectNews")
    public ItemResult<List<NewsDTO>> findByDecorate(@Parameter(name = "size", description = "显示数量") @RequestParam(defaultValue = "10") Integer size) {
        return new ItemResult<>(newsAssemblyBuildingService.decorateProjectNews(size));
    }

    @Operation(summary = "信息化管理")
    @GetMapping(value = "/manageProjectNews")
    public ItemResult<List<NewsDTO>> findByManage(@Parameter(name = "size", description = "显示数量") @RequestParam(defaultValue = "10") Integer size) {
        return new ItemResult<>(newsAssemblyBuildingService.manageProjectNews(size));
    }

    @Operation(summary = "智能化应用")
    @GetMapping(value = "/applicationProjectNews")
    public ItemResult<List<NewsDTO>> findByApplication(@Parameter(name = "size", description = "显示数量") @RequestParam(defaultValue = "10") Integer size) {
        return new ItemResult<>(newsAssemblyBuildingService.applicationProjectNews(size));
    }

    @Operation(summary = "项目展示")
    @GetMapping(value = "/showProjectNews")
    public ItemResult<List<NewsDTO>> findByProject(@Parameter(name = "size", description = "显示数量") @RequestParam(defaultValue = "10") Integer size) {
        return new ItemResult<>(newsAssemblyBuildingService.showProjectNews(size));
    }

    @Operation(summary = "战略合作伙伴")
    @GetMapping(value = "/partnerProjectNews")
    public ItemResult<List<NewsDTO>> findByPartner(@Parameter(name = "size", description = "显示数量") @RequestParam(defaultValue = "10") Integer size) {
        return new ItemResult<>(newsAssemblyBuildingService.partnerProjectNews(size));
    }

    @Operation(summary = "根据id查询")
    @GetMapping(value = "/findById")
    public ItemResult<NewsDTO> findById(@Parameter(name = "newsId", description = "新闻Id") @RequestParam String newsId) {
        NewsDTO news = newsAssemblyBuildingService.findById(newsId);
        //如果为空
        if (news == null ||
                //如果已删除
                (news.getDelFlg() != null && news.getDelFlg().booleanValue()) ||
                //如果未审批通过
                (news.getCheckStatus() != null && (news.getCheckStatus() != 2 && news.getCheckStatus() != 3))) {
            return new ItemResult<>(null);
        }
        return new ItemResult<>(news);
    }

    @Operation(summary = "装配式建筑分类查询")
    @GetMapping(value="/assemblyBuilding")
    public ItemResult<List<NewsDTO>> assemblyBuilding(@Parameter(name = "value", description = "类型")String value,
                                                      @Parameter(name = "size", description = "显示数量")Integer size){
        switch (value) {
            //投资开发
            case "INFO_TYPE_INVEST":return new ItemResult<>(newsAssemblyBuildingService.investProjectNews(size));
            //工业化生产
            case "INFO_TYPE_PROD":return new ItemResult<>(newsAssemblyBuildingService.prodProjectNews(size));
            //标准化设计
            case "INFO_TYPE_DESIGN":return new ItemResult<>(newsAssemblyBuildingService.designProjectNews(size));
            //装配化施工
            case "INFO_TYPE_BUILD":return new ItemResult<>(newsAssemblyBuildingService.buildProjectNews(size));
            //一体化装修
            case "INFO_TYPE_DECORATE":return new ItemResult<>(newsAssemblyBuildingService.decorateProjectNews(size));
            //信息化管理
            case "INFO_TYPE_MANAGE":return new ItemResult<>(newsAssemblyBuildingService.manageProjectNews(size));
            //智能化应用
            case "INFO_TYPE_APPLICATION":return new ItemResult<>(newsAssemblyBuildingService.applicationProjectNews(size));

            default:return new ItemResult<>(Lists.newArrayList());
        }
    }
}
