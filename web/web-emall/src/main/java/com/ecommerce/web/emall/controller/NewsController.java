package com.ecommerce.web.emall.controller;


import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.common.result.ItemResult;
import com.ecommerce.information.api.dto.news.NewsDTO;
import com.ecommerce.information.api.dto.news.NewsQueryDTO;
import com.ecommerce.information.api.service.INewsAssemblyBuildingService;
import com.ecommerce.information.api.service.INewsService;
import com.ecommerce.information.api.service.INewsTechnologyEquipmentService;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;


/**
 * @Created锛�Sat Nov 17 14:02:09 CST 2018
 * <AUTHOR>
 * @Version:2
 * @Description:null
*/

@RestController
@Tag(name = "News", description = "新闻相关接口")
@RequestMapping("/news/anon")
public class NewsController {

   @Autowired 
   private INewsService newsService;
   //技术装备
   @Autowired
   private INewsTechnologyEquipmentService newsTechnologyEquipmentService;
   //装配式建筑
   @Autowired
   private INewsAssemblyBuildingService newsAssemblyBuildingService;


   @Operation(summary = "最新资讯")
   @GetMapping(value="/latestNews")
   public ItemResult<List<NewsDTO>> latestNews(@Parameter(name = "size", description = "显示数量") Integer size){
      return new ItemResult<>(newsService.latestNews(size));
   }

   @Operation(summary = "全国资讯")
   @GetMapping(value="/nationalNews")
   public ItemResult<List<NewsDTO>> nationalNews(@Parameter(name = "size", description = "显示数量") Integer size){
      return new ItemResult<>(newsService.nationalNews(size));
   }

   @Operation(summary = "企业资讯")
   @GetMapping(value="/corporateNews")
   public ItemResult<List<NewsDTO>> corporateNews(@Parameter(name = "size", description = "显示数量") Integer size){
      return new ItemResult<>(newsService.corporateNews(size));
   }

   @Operation(summary = "政策法规")
   @GetMapping(value="/policiesAndRegulations")
   public ItemResult<List<NewsDTO>> policiesAndRegulations(@Parameter(name = "size", description = "显示数量") Integer size){
      return new ItemResult<>(newsService.policiesAndRegulations(size));
   }

   //切分到 NewsTechnologyEquipmentController
   @Operation(summary = "热门产品")
   @GetMapping(value="/hotProduct")
   public ItemResult<List<NewsDTO>> hotProduct(@Parameter(name = "size", description = "显示数量") Integer size){
      return new ItemResult<>(newsTechnologyEquipmentService.hotProductNews(size));
   }

   @Operation(summary = "技术专题")
   @GetMapping(value="/technologyProject")
   public ItemResult<List<NewsDTO>> technologyProject(@Parameter(name = "size", description = "显示数量") Integer size){
      return new ItemResult<>(newsTechnologyEquipmentService.technologyProjectNews(size));
   }

   // 切分到 NewsAssemblyBuildingController
   @Operation(summary = "投资开发")
   @GetMapping(value="/investProjectNews")
   public ItemResult<List<NewsDTO>> investProjectNews(@Parameter(name = "size", description = "显示数量") Integer size){
      return new ItemResult<>(newsAssemblyBuildingService.investProjectNews(size));
   }
   @Operation(summary = "工业化生产")
   @GetMapping(value="/prodProjectNews")
   public ItemResult<List<NewsDTO>> prodProjectNews(@Parameter(name = "size", description = "显示数量") Integer size){
      return new ItemResult<>(newsAssemblyBuildingService.prodProjectNews(size));
   }
   @Operation(summary = "标准化设计")
   @GetMapping(value="/designProjectNews")
   public ItemResult<List<NewsDTO>> designProjectNews(@Parameter(name = "size", description = "显示数量") Integer size){
      return new ItemResult<>(newsAssemblyBuildingService.designProjectNews(size));
   }
   @Operation(summary = "装配化施工")
   @GetMapping(value="/buildProjectNews")
   public ItemResult<List<NewsDTO>> buildProjectNews(@Parameter(name = "size", description = "显示数量") Integer size){
      return new ItemResult<>(newsAssemblyBuildingService.buildProjectNews(size));
   }
   @Operation(summary = "一体化装修")
   @GetMapping(value="/decorateProjectNews")
   public ItemResult<List<NewsDTO>> decorateProjectNews(@Parameter(name = "size", description = "显示数量") Integer size){
      return new ItemResult<>(newsAssemblyBuildingService.decorateProjectNews(size));
   }
   @Operation(summary = "信息化管理")
   @GetMapping(value="/manageProjectNews")
   public ItemResult<List<NewsDTO>> manageProjectNews(@Parameter(name = "size", description = "显示数量") Integer size){
      return new ItemResult<>(newsAssemblyBuildingService.manageProjectNews(size));
   }
   @Operation(summary = "智能化应用")
   @GetMapping(value="/applicationProjectNews")
   public ItemResult<List<NewsDTO>> applicationProjectNews(@Parameter(name = "size", description = "显示数量") Integer size){
      return new ItemResult<>(newsAssemblyBuildingService.applicationProjectNews(size));
   }
   @Operation(summary = "项目展示")
   @GetMapping(value="/showProjectNews")
   public ItemResult<List<NewsDTO>> showProjectNews(@Parameter(name = "size", description = "显示数量") Integer size){
      return new ItemResult<>(newsAssemblyBuildingService.showProjectNews(size));
   }
   @Operation(summary = "战略合作伙伴")
   @GetMapping(value="/partnerProjectNews")
   public ItemResult<List<NewsDTO>> partnerProjectNews(@Parameter(name = "size", description = "显示数量") Integer size){
      return new ItemResult<>(newsAssemblyBuildingService.partnerProjectNews(size));
   }

   @Operation(summary = "行情动态")
   @GetMapping(value="/marketDynamics")
   public ItemResult<List<NewsDTO>> marketDynamics(@Parameter(name = "size", description = "显示数量") Integer size){
      return new ItemResult<>(newsService.marketDynamics(size));
   }

   @Operation(summary = "装配式建筑分类查询")
   @GetMapping(value="/assemblyBuilding")
   public ItemResult<List<NewsDTO>> assemblyBuilding(@Parameter(name = "value", description = "类型") String value,
                                                     @Parameter(name = "size", description = "显示数量") Integer size){
      switch (value) {
         //投资开发
         case "INFO_TYPE_INVEST":return new ItemResult<>(newsAssemblyBuildingService.investProjectNews(size));
         //工业化生产
         case "INFO_TYPE_PROD":return new ItemResult<>(newsAssemblyBuildingService.prodProjectNews(size));
         //标准化设计
         case "INFO_TYPE_DESIGN":return new ItemResult<>(newsAssemblyBuildingService.designProjectNews(size));
         //装配化施工
         case "INFO_TYPE_BUILD":return new ItemResult<>(newsAssemblyBuildingService.buildProjectNews(size));
         //一体化装修
         case "INFO_TYPE_DECORATE":return new ItemResult<>(newsAssemblyBuildingService.decorateProjectNews(size));
         //信息化管理
         case "INFO_TYPE_MANAGE":return new ItemResult<>(newsAssemblyBuildingService.manageProjectNews(size));
         //智能化应用
         case "INFO_TYPE_APPLICATION":return new ItemResult<>(newsAssemblyBuildingService.applicationProjectNews(size));

         default:return new ItemResult<>(Lists.newArrayList());
      }
   }

   @Operation(summary = "根据id查询")
   @GetMapping(value="/findById")
   public ItemResult<NewsDTO> findById(@Parameter(name = "newsId", description = "新闻Id") @RequestParam String newsId){
      NewsDTO news = newsService.findById(newsId);
      //如果为空
      if( news == null ||
               //如果已删除
              (news.getDelFlg() != null && news.getDelFlg().booleanValue()) ||
              //如果未审批通过
              (news.getCheckStatus() !=null && (news.getCheckStatus() !=2 && news.getCheckStatus() != 3)) ){
         return new ItemResult<>(null);
      }
      return new ItemResult<>(news);
   }

   @Operation(summary = "根据id查询单条装配式建筑")
   @GetMapping(value="/findAssemblyBuildingById")
   public ItemResult<NewsDTO> findAssemblyBuildingById(@Parameter(name = "newsId", description = "新闻Id") @RequestParam String newsId){
      NewsDTO news = newsAssemblyBuildingService.findById(newsId);
      //如果为空
      if( news == null ||
              //如果已删除
              (news.getDelFlg() != null && news.getDelFlg().booleanValue()) ||
              //如果未审批通过
              (news.getCheckStatus() !=null && (news.getCheckStatus() !=2 && news.getCheckStatus() != 3)) ){
         return new ItemResult<>(null);
      }
      return new ItemResult<>(news);
   }

   @Operation(summary = "根据id查询技术装备")
   @GetMapping(value="/findTechnologyEquipmentById")
   public ItemResult<NewsDTO> findTechnologyEquipmentById(@Parameter(name = "newsId", description = "新闻Id") @RequestParam String newsId){
      NewsDTO news = newsTechnologyEquipmentService.findById(newsId);
      //如果为空
      if( news == null ||
              //如果已删除
              (news.getDelFlg() != null && news.getDelFlg().booleanValue()) ||
              //如果未审批通过
              (news.getCheckStatus() !=null && (news.getCheckStatus() !=2 && news.getCheckStatus() != 3)) ){
         return new ItemResult<>(null);
      }
      return new ItemResult<>(news);
   }

   @Operation(summary = "翻页查询")
   @PostMapping(value="/findAll")
   public ItemResult<PageInfo<NewsDTO>> findAll(@Valid @RequestBody NewsQueryDTO query){
      query.setCheckStatus(2);
      return new ItemResult<>(newsService.findAll(query));
   }


}
