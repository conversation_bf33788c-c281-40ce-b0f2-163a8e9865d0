package com.ecommerce.web.emall.controller;


import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.common.result.ItemResult;
import com.ecommerce.information.api.dto.news.NewsDTO;
import com.ecommerce.information.api.service.INewsTechnologyEquipmentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 *
 * <AUTHOR>
 */

@RestController
@Tag(name = "NewsTechnologyEquipment", description = "技术装备服务接口")
@RequestMapping("/newsTechnologyEquipment/anon")
public class NewsTechnologyEquipmentController {

    //技术装备
    @Autowired
    private INewsTechnologyEquipmentService newsTechnologyEquipmentService;

    @Operation(summary = "热门产品")
    @GetMapping(value="/hotProduct")
    public ItemResult<List<NewsDTO>> hotProduct(@Parameter(name = "size", description = "显示数量") Integer size){
        return new ItemResult<>(newsTechnologyEquipmentService.hotProductNews(size));
    }

    @Operation(summary = "技术专题")
    @GetMapping(value="/technologyProject")
    public ItemResult<List<NewsDTO>> technologyProject(@Parameter(name = "size", description = "显示数量") Integer size){
        return new ItemResult<>(newsTechnologyEquipmentService.technologyProjectNews(size));
    }



    @Operation(summary = "根据id查询")
    @GetMapping(value="/findById")
    public ItemResult<NewsDTO> findById(@Parameter(name = "technologyEquipmentId", description = "主键Id") @RequestParam("technologyEquipmentId") String newsId){
        NewsDTO news = newsTechnologyEquipmentService.findById(newsId);

        //如果为空
        if( news == null ||
                //如果已删除
                (news.getDelFlg() != null && news.getDelFlg().booleanValue()) ||
                //如果未审批通过
                (news.getCheckStatus() !=null && (news.getCheckStatus() !=2 && news.getCheckStatus() != 3)) ){
            return new ItemResult<>(null);
        }
        return new ItemResult<>(news);
    }






}
