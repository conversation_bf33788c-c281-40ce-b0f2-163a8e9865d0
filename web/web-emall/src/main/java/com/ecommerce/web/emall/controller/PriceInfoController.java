package com.ecommerce.web.emall.controller;


import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.base.api.dto.ValueSetTreeDTO;
import com.ecommerce.base.api.dto.region.RegionSampleDTO;
import com.ecommerce.base.api.service.IRegionService;
import com.ecommerce.base.api.service.IValueSetService;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.information.api.dto.priceInfo.*;
import com.ecommerce.information.api.service.IPriceInfoService;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import com.ecommerce.common.utils.CsStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@RestController
@Tag(name = "PriceInfoController", description = "价格信息查询接口")
@RequestMapping("/anon/priceInfo")
public class PriceInfoController {

   @Autowired 
   private IPriceInfoService priceInfoService;
   @Autowired
   private IRegionService iRegionService;
   @Autowired
   private IValueSetService iValueSetService;

   @Operation(summary = "价格走势图数据查询")
   @PostMapping(value="/findPriceChartData")
   public ItemResult<List<PriceInfoDTO>> findPriceChartData(@RequestBody PriceChartDataQueryDTO dto){
      return new ItemResult<>(priceInfoService.findPriceChartData(dto));
   }

   @Operation(summary = "资讯首页查显示的")
   @GetMapping(value="/findCement")
   public ItemResult<List<PriceInfoDTO>> findCement(@Parameter(name = "size", description = "显示数量") Integer size){
      return new ItemResult<>(priceInfoService.findCement(size));
   }

   @Operation(summary = "资讯首页查显示的")
   @GetMapping(value="/findConcrete")
   public ItemResult<List<PriceInfoDTO>> findConcrete(@Parameter(name = "size", description = "显示数量") Integer size){
      return new ItemResult<>(priceInfoService.findConcrete(size));
   }

   @Operation(summary = "翻页查询")
   @PostMapping(value="/findAll")
   public ItemResult<PageInfo<PriceInfoDTO>> findAll(@RequestBody PriceInfoQueryDTO dto){
      return new ItemResult<>(priceInfoService.findAll(dto));
   }

   @Operation(summary = "查询品牌")
   @GetMapping(value="/findCementBrand")
   public ItemResult<List<String>> findCementBrand(@Parameter(name = "keyWord", description = "关键字") String keyWord){
      return new ItemResult<>(priceInfoService.findCementBrand(keyWord));
   }
   @Operation(summary = "查询品种")
   @GetMapping(value="/findCementVarieties")
   public ItemResult<List<String>> findCementVarieties(@Parameter(name = "keyWord", description = "关键字") String keyWord){
      return new ItemResult<>(priceInfoService.findCementVarieties(keyWord));
   }
   @Operation(summary = "查询品牌")
   @GetMapping(value="/findConcreteBrand")
   public ItemResult<List<String>> findConcreteBrand(@Parameter(name = "keyWord", description = "关键字") String keyWord){
      return new ItemResult<>(priceInfoService.findConcreteBrand(keyWord));
   }
   @Operation(summary = "查询品种")
   @GetMapping(value="/findConcreteVarieties")
   public ItemResult<List<String>> findConcreteVarieties(@Parameter(name = "keyWord", description = "关键字") String keyWord){
      return new ItemResult<>(priceInfoService.findConcreteVarieties(keyWord));
   }

   @Operation(summary = "根据id查询单条记录")
   @GetMapping(value="/findById")
   public ItemResult<PriceInfoDTO> findById(@Parameter(name = "id", description = "记录Id") String id){
       if (CsStringUtils.isBlank(id)) {
         return new ItemResult<>(null);
      }
      return new ItemResult<>(priceInfoService.findById(id));
   }

   @Operation(summary = "查询省份")
   @GetMapping(value="/findProvince")
   public ItemResult<List<RegionSampleDTO>> findProvince() {
      return new ItemResult<>(iRegionService.findByParentAdCode("100000"));
   }

   @Operation(summary = "查询城市")
   @GetMapping(value="/findCity")
   public ItemResult<List<RegionSampleDTO>> anonFindByParentAdCode(@Parameter(name = "parentAdcode", description = "城市code") String parentAdcode) {
      parentAdcode = parentAdcode == null ? "100000" : parentAdcode;
      return new ItemResult<>(iRegionService.findByParentAdCode(parentAdcode));
   }

   @Operation(summary = "查询品种")
   @GetMapping(value="/findCategory")
   public ItemResult<List<ValueSetTreeDTO>> findCategory() {
      ValueSetTreeDTO treeDTO = iValueSetService.getValueSetByReferenceCode("PRICE_INFO_TYPE");
      return new ItemResult<>(treeDTO == null ? Lists.newArrayList() : treeDTO.getTreeList());
   }

   @Operation(summary = "查询有数据的省份(emall首页专用)")
   @GetMapping(value="/findDataProvince")
   public ItemResult<List<String>> findDataProvince() {
      return new ItemResult<>(priceInfoService.findProvince());
   }

   @Operation(summary = "查询省份价格数据(emall首页专用)")
   @PostMapping(value="/findProvincePrice")
   public ItemResult<ProvincePriceInfoDTO> findProvincePrice(@Parameter(name = "province", description = "省份") @RequestParam String province) {
      ProvincePriceInfoQueryDTO provincePriceInfoQueryDTO = new ProvincePriceInfoQueryDTO();
      provincePriceInfoQueryDTO.setProvince(province);
      return new ItemResult<>(priceInfoService.findProvincePriceInfo(provincePriceInfoQueryDTO));
   }

   @Operation(summary = "查询省份价格数据(emall首页专用)2")
   @PostMapping(value="/findProvincePrice2")
   public ItemResult<ProvincePriceInfoIndexDTO> findProvincePrice2(@Parameter(name = "province", description = "省份") @RequestParam String province,
                                                              @Parameter(name = "size", description = "返回记录数") @RequestParam(defaultValue = "5") Integer size) {
      ProvincePriceInfoQueryDTO provincePriceInfoQueryDTO = new ProvincePriceInfoQueryDTO();
      provincePriceInfoQueryDTO.setProvince(province);
      provincePriceInfoQueryDTO.setLimitSize(size);
      provincePriceInfoQueryDTO.setOrder(1);
      return new ItemResult<>(priceInfoService.findProvincePriceInfo2(provincePriceInfoQueryDTO));
   }
}
