package com.ecommerce.web.emall.controller;

import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.exception.BusinessOperationException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.goods.api.dto.GoodsResourceListDTO;
import com.ecommerce.goods.api.dto.PagePurchaseDTO;
import com.ecommerce.goods.api.dto.PurchaseDTO;
import com.ecommerce.goods.api.dto.ReqGoodsResourceDTO;
import com.ecommerce.goods.api.dto.ResourceDTO;
import com.ecommerce.goods.api.service.IPurchaseService;
import com.ecommerce.goods.api.service.IResourceQueryService;
import com.ecommerce.goods.api.service.IResourceService;
import com.ecommerce.information.api.dto.shop.GetShopCarouselListDTO;
import com.ecommerce.information.api.dto.shop.GetShopPictureListDTO;
import com.ecommerce.information.api.dto.shop.PageShopPictureListDTO;
import com.ecommerce.information.api.dto.shop.ShopAdvertisementDTO;
import com.ecommerce.information.api.dto.shop.ShopAdvertisementEmallDTO;
import com.ecommerce.information.api.dto.shop.ShopCarouselDTO;
import com.ecommerce.information.api.dto.shop.ShopCarouselListDTO;
import com.ecommerce.information.api.dto.shop.ShopInfoDTO;
import com.ecommerce.information.api.dto.shop.ShopPictureDTO;
import com.ecommerce.information.api.dto.shop.ShopPictureListDTO;
import com.ecommerce.information.api.service.IShopService;
import com.ecommerce.member.api.session.LoginInfo;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import com.ecommerce.common.utils.CsStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;


@Slf4j
@Tag(name = "Shop", description = ":店铺服务")
@RestController
@RequestMapping("/shop/anon")
public class ShopController {
    @Autowired
    private IShopService iShopService;
    @Autowired
    private IResourceService resourceService;
    @Autowired
    private IResourceQueryService resourceQueryService;
    @Autowired
    private IPurchaseService iPurchaseService;

    @Operation(summary = "查看店铺详情")
    @PostMapping(value="/getShopInfo")
    public ItemResult<ShopInfoDTO> getShopInfo(@Parameter(name = "shopId", description = "店铺Id") @RequestParam String shopId){
        return new ItemResult<>(iShopService.getShopInfo(shopId));
    }

    @Operation(summary = "通过memberId查看店铺详情")
    @PostMapping(value="/getShopInfoByMemberId")
    public ItemResult<ShopInfoDTO> getShopInfoByMemberId(@Parameter(name = "memberId", description = "会员Id") @RequestParam String memberId){
        return new ItemResult<>(iShopService.getShopInfoByMemberId(memberId));
    }

    @Operation(summary = "通过域名前缀查询店铺")
    @PostMapping(value="/getShopInfoByUrl")
    public ItemResult<ShopInfoDTO> getShopInfoByUrl(@Parameter(name = "shopUrlPrefix", description = "域名前缀") @RequestParam String shopUrlPrefix){
        return new ItemResult<>(iShopService.getShopInfoByUrl(shopUrlPrefix));
    }

    @Operation(summary = "店铺图集列表")
    @PostMapping(value="/getShopPictureList")
    public ItemResult<List<ShopPictureListDTO>> getShopPictureList(@RequestBody GetShopPictureListDTO getShopPictureListDTO){
        return new ItemResult<>(iShopService.getShopPictureList(getShopPictureListDTO));
    }

    @Operation(summary = "店铺图集详情")
    @PostMapping(value="/getShopPictureDetail")
    public ItemResult<ShopPictureDTO> getShopPictureDetail(@Parameter(name = "pictureId", description = "图集Id") @RequestParam String pictureId){
        return new ItemResult<>(iShopService.getShopPictureDetail(pictureId));
    }

    @Operation(summary = "分页店铺图集列表")
    @PostMapping(value="/pageShopPictureList")
    public ItemResult<PageInfo<ShopPictureListDTO>> pageShopPictureList(@RequestBody PageShopPictureListDTO pageShopPictureListDTO){
        return new ItemResult<>(iShopService.pageShopPictureList(pageShopPictureListDTO));
    }

    @Operation(summary = "店铺轮播图详情")
    @PostMapping(value="/getShopCarouselDetail")
    public ItemResult<ShopCarouselDTO> getShopCarouselDetail(@Parameter(name = "carouselId", description = "轮播图Id") @RequestParam String carouselId){
        return new ItemResult<>(iShopService.getShopCarouselDetail(carouselId));
    }

    @Operation(summary = "店铺轮播图列表")
    @PostMapping(value="/getShopCarouselList")
    public ItemResult<List<ShopCarouselListDTO>> getShopCarouselList(@RequestBody GetShopCarouselListDTO getShopCarouselListDTO){
        return new ItemResult<>(iShopService.getShopCarouselList(getShopCarouselListDTO));
    }

    @Operation(summary = "通过店铺ID查询店铺广告")
    @PostMapping(value="/getShopAdvertisementList")
    public ItemResult<List<ShopAdvertisementEmallDTO>> getShopAdvertisementList(@Parameter(name = "shopId", description = "店铺Id") @RequestParam String shopId,
                                                                                @Parameter(name = "advertisementType", description = "广告类型") @RequestParam String advertisementType){
        List<ShopAdvertisementEmallDTO> resultList = new ArrayList<>();
        List<ShopAdvertisementDTO> list = iShopService.getShopAdvertisementByShopId(shopId,advertisementType);
        for(ShopAdvertisementDTO shopAdvertisementDTO : list){
            ShopAdvertisementEmallDTO shopAdvertisementEmallDTO = new ShopAdvertisementEmallDTO();
            if("seller".equals(advertisementType)){

                if (CsStringUtils.isNotBlank(shopAdvertisementDTO.getAdBusinessId())) {
                    ResourceDTO resourceDTO = resourceService.getResourceDetail(shopAdvertisementDTO.getAdBusinessId());
                    if (resourceDTO == null) {
                        throw new BizException(BasicCode.UNDEFINED_ERROR, "该广告位关联商品不存在");
                    }
                    shopAdvertisementEmallDTO.setDescribe(resourceDTO.getGoodsDescribe());
                    shopAdvertisementEmallDTO.setGoodsName(resourceDTO.getGoodsName());
                    shopAdvertisementEmallDTO.setPrice(resourceDTO.getPrice().setScale(2, RoundingMode.HALF_UP));
                    shopAdvertisementEmallDTO.setUnit(resourceDTO.getSaleUnit());
                }
            }else if("supplier".equals(advertisementType)){

                if (CsStringUtils.isNotBlank(shopAdvertisementDTO.getAdBusinessId())) {
                    PurchaseDTO resourceDTO = iPurchaseService.getPurchaseDetail(shopAdvertisementDTO.getAdBusinessId());
                    if (resourceDTO == null) {
                        throw new BizException(BasicCode.UNDEFINED_ERROR, "该广告位关联商品不存在");
                    }
                    shopAdvertisementEmallDTO.setDescribe(resourceDTO.getGoodsDescribe());
                    shopAdvertisementEmallDTO.setGoodsName(resourceDTO.getGoodsName());
                    shopAdvertisementEmallDTO.setPrice(resourceDTO.getPrice().setScale(2, RoundingMode.HALF_UP));
                    shopAdvertisementEmallDTO.setUnit(resourceDTO.getSaleUnit());
                }
            }

            shopAdvertisementEmallDTO.setAdImageUrl(shopAdvertisementDTO.getAdImageUrl());
            shopAdvertisementEmallDTO.setRedeactUrl(shopAdvertisementDTO.getAdResourceUrl());
            shopAdvertisementEmallDTO.setHint(shopAdvertisementDTO.getHint());
            shopAdvertisementEmallDTO.setAdName(shopAdvertisementDTO.getAdName());
            resultList.add(shopAdvertisementEmallDTO);
        }
        return new ItemResult<>(resultList);
    }

    @Operation(summary = "通过关键字查询店铺商品广告")
    @PostMapping(value="/searchGoodsResourceEmall")
    public ItemResult<PageInfo<GoodsResourceListDTO>> searchGoodsResourceEmall(@RequestBody ReqGoodsResourceDTO query, @Parameter(hidden = true)HttpServletRequest request){
        Object obj = request.getSession().getAttribute(LoginInfo.SESSION_NAME);
        if( obj != null && obj instanceof LoginInfo info ){
            String buyerId = info.getMemberId();
            query.setSearchMemberId(buyerId);
        }
        return new ItemResult<>(resourceQueryService.searchGoodsResourceEmall(query));
    }

    @Operation(summary = "分页采购列表查询")
    @PostMapping(value="/searchPagePurchase")
    public ItemResult<PageInfo<PurchaseDTO>> searchPagePurchase(@RequestBody PagePurchaseDTO arg0){
        return new ItemResult<>(iPurchaseService.searchPagePurchase(arg0));
    }
}
