package com.ecommerce.web.emall.controller;


import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.common.result.ItemResult;
import com.ecommerce.member.api.dto.TaxInfoDetailDTO;
import com.ecommerce.member.api.service.ITaxInfoService;
import com.ecommerce.member.api.session.LoginInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;


/**
 * 增票资质相关service
 *
 * <AUTHOR>
*/

@RestController
@Tag(name = "TaxInfo", description = "增票资质相关service")
@RequestMapping("/taxInfo")
public class TaxInfoController {

   @Autowired
   private ITaxInfoService iTaxInfoService;




   @Operation(summary = "根据会员id获取默认增票资质（企业员工则根据企业id获取默认收货地址）")
   @PostMapping(value="/getDefaultById")
   public ItemResult<TaxInfoDetailDTO> getDefaultById(@Parameter(hidden = true)LoginInfo loginInfo){
      return new ItemResult<>(iTaxInfoService.getDefaultById(loginInfo.getMemberId()));
   }


   @Operation(summary = "根据id查找增票资质")
   @PostMapping(value="/findById")
   public ItemResult<TaxInfoDetailDTO> findById(@Parameter(hidden = true)LoginInfo loginInfo,@Parameter(name = "id", description = "增派资质Id") @RequestParam String id){
      if(!iTaxInfoService.checkTaxInfo(loginInfo.getAccountId(),id)){
         return ItemResult.fail("此用户没有权限");
      }
      return new ItemResult<>(iTaxInfoService.findById(id));
   }


   @Operation(summary = "查询某会员的所有增票资质")
   @PostMapping(value="/findByMemberId")
   public ItemResult<List<TaxInfoDetailDTO>> findByMemberId(@Parameter(hidden = true)LoginInfo loginInfo){
      return new ItemResult<>(iTaxInfoService.findByMemberId(loginInfo.getMemberId()));
   }

   @Operation(summary = "根据公司名称或纳税人识别码查找公司名称和纳税人识别码")
   @GetMapping(value="/findByCompanyNameOrCode")
   public ItemResult<List<Map<String,String>>> findByCompanyNameOrCode(@Parameter(hidden = true)LoginInfo loginInfo,@Parameter(name = "keyWord", description = "关键词") String keyWord){
      return new ItemResult<>(iTaxInfoService.findByCompanyNameOrCode(keyWord));
   }

}
