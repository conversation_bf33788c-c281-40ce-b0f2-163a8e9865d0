package com.ecommerce.web.emall.controller;


import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.vehicletype.VehicleTypeListDTO;
import com.ecommerce.logistics.api.dto.vehicletype.VehicleTypeListQueryDTO;
import com.ecommerce.logistics.api.dto.vehicletype.VehicleTypeOptionDTO;
import com.ecommerce.logistics.api.service.IVehicleTypeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * <AUTHOR>
 * @Date: 27/08/2018 10:16
 * @Description:
 */
@RestController
@RequestMapping(value = "/vehicleType")
@CrossOrigin
@Slf4j
@Tag(name = "VehicleType", description = "车辆类型管理")
public class VehicleTypeController {
    @Autowired
    private IVehicleTypeService iVehicleTypeService;

    @Operation(summary = "获取车辆类型列表")
    @PostMapping(value="/queryVehicleTypeList")
    public ItemResult<PageData<VehicleTypeListDTO>> queryVehicleTypeList(@RequestBody PageQuery<VehicleTypeListQueryDTO> arg0){
        return iVehicleTypeService.queryVehicleTypeList(arg0);
    }

    @Operation(summary = "获取车辆类型Options")
    @PostMapping(value="/queryOptions")
    public ItemResult<List<VehicleTypeOptionDTO>> queryOptions(){
        return iVehicleTypeService.queryOptions();
    }
}
