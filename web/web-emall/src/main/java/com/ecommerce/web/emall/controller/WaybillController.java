package com.ecommerce.web.emall.controller;


import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.waybill.PublishWaybillListDTO;
import com.ecommerce.logistics.api.dto.waybill.PublishWaybillListQueryDTO;
import com.ecommerce.logistics.api.enums.WaybillStatusEnum;
import com.ecommerce.logistics.api.enums.WaybillTypeEnum;
import com.ecommerce.logistics.api.service.IWaybillService;
import com.ecommerce.web.emall.vo.logistics.DeliveryWaybillVO;
import com.ecommerce.web.emall.vo.logistics.PublishWaybillVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 * @Description:
 * @Date: Create in 下午8:32 18/9/10
 */
@RestController
@Tag(name = "Waybill", description = "运单服务")
@RequestMapping("/waybill")
public class WaybillController {
    @Autowired
    private IWaybillService waybillService;

    @Operation(summary = "发布运单列表")
    @PostMapping("/anon/queryPublishWaybillList")
    public ItemResult<List<PublishWaybillVO>> queryPublishWaybillList(@RequestBody PageQuery<PublishWaybillListQueryDTO> pageQuery){
        PublishWaybillListQueryDTO publishWaybillListQueryDTO =  pageQuery.getQueryDTO();
        if (publishWaybillListQueryDTO == null) {
            publishWaybillListQueryDTO = new PublishWaybillListQueryDTO();
        }
        //已发布待接单
        publishWaybillListQueryDTO.setStatus(WaybillStatusEnum.WAIT_RECEIVE.getCode());
        publishWaybillListQueryDTO.setType(WaybillTypeEnum.SOCIETY_SNATCH.getCode());
        pageQuery.setQueryDTO(publishWaybillListQueryDTO);
        pageQuery.setPageNum(1);
        pageQuery.setPageSize(1);
        ItemResult<PageData<PublishWaybillListDTO>> pageDataItemResultOne = waybillService.queryPublishWaybillList(pageQuery);
        List<PublishWaybillVO> publishWaybillList = new ArrayList<>();
        if (pageDataItemResultOne.isSuccess() && CollectionUtils.isNotEmpty(pageDataItemResultOne.getData().getList())) {
            for (PublishWaybillListDTO publishWaybillListDTO : pageDataItemResultOne.getData().getList()) {
                PublishWaybillVO publishWaybillVO = new PublishWaybillVO();
                BeanUtils.copyProperties(publishWaybillListDTO, publishWaybillVO);
                publishWaybillList.add(publishWaybillVO);
            }
        }
        //已抢运单
        publishWaybillListQueryDTO.setStatus(WaybillStatusEnum.WAIT_DELIVERY.getCode());
        pageQuery.setPageSize(2);
        ItemResult<PageData<PublishWaybillListDTO>> pageDataItemResultTwo = waybillService.queryPublishWaybillList(pageQuery);
        if (pageDataItemResultTwo.isSuccess() && CollectionUtils.isNotEmpty(pageDataItemResultTwo.getData().getList())) {
            for (PublishWaybillListDTO publishWaybillListDTO : pageDataItemResultTwo.getData().getList()) {
                PublishWaybillVO publishWaybillVO = new PublishWaybillVO();
                BeanUtils.copyProperties(publishWaybillListDTO, publishWaybillVO);
                publishWaybillList.add(publishWaybillVO);
            }
        }

        return new ItemResult<>(publishWaybillList);
    }

    @Operation(summary = "查询送货单列表")
    @PostMapping("/anon/queryDeliveringWaybillList")
    public ItemResult<List<DeliveryWaybillVO>> queryDeliveringWaybillList(@RequestBody PageQuery<PublishWaybillListQueryDTO> pageQuery){
        PublishWaybillListQueryDTO publishWaybillListQueryDTO =  pageQuery.getQueryDTO();
        if (publishWaybillListQueryDTO == null) {
            publishWaybillListQueryDTO = new PublishWaybillListQueryDTO();
        }
        //配送中
        publishWaybillListQueryDTO.setStatus(WaybillStatusEnum.DELIVERING.getCode());
        publishWaybillListQueryDTO.setType(WaybillTypeEnum.SOCIETY_SNATCH.getCode());
        pageQuery.setQueryDTO(publishWaybillListQueryDTO);
        pageQuery.setPageNum(1);
        pageQuery.setPageSize(1);
        ItemResult<PageData<PublishWaybillListDTO>> pageDataItemResultOne = waybillService.queryPublishWaybillList(pageQuery);
        List<DeliveryWaybillVO> deliveryWaybillList = new ArrayList<>();
        if (pageDataItemResultOne.isSuccess() && CollectionUtils.isNotEmpty(pageDataItemResultOne.getData().getList())) {
            for (PublishWaybillListDTO publishWaybillListDTO : pageDataItemResultOne.getData().getList()) {
                DeliveryWaybillVO deliveryWaybillVO = new DeliveryWaybillVO();
                BeanUtils.copyProperties(publishWaybillListDTO, deliveryWaybillVO);
                //屏蔽车牌号
                if (deliveryWaybillVO.getVehicleNum() != null && deliveryWaybillVO.getVehicleNum().length() >=7)
                    deliveryWaybillVO.setVehicleNum(deliveryWaybillVO.getVehicleNum().replaceAll("(.{2})\\w{3}(.*)", "$1***$2"));
                deliveryWaybillList.add(deliveryWaybillVO);
            }
        }
        //已完成
        publishWaybillListQueryDTO.setStatus(WaybillStatusEnum.COMPLETED.getCode());
        pageQuery.setPageSize(2);
        ItemResult<PageData<PublishWaybillListDTO>> pageDataItemResultTwo = waybillService.queryPublishWaybillList(pageQuery);
        if (pageDataItemResultTwo.isSuccess() && CollectionUtils.isNotEmpty(pageDataItemResultTwo.getData().getList())) {
            for (PublishWaybillListDTO publishWaybillListDTO : pageDataItemResultTwo.getData().getList()) {
                DeliveryWaybillVO deliveryWaybillVO = new DeliveryWaybillVO();
                BeanUtils.copyProperties(publishWaybillListDTO, deliveryWaybillVO);
                if (deliveryWaybillVO.getVehicleNum() != null && deliveryWaybillVO.getVehicleNum().length() >=7)
                    deliveryWaybillVO.setVehicleNum(deliveryWaybillVO.getVehicleNum().replaceAll("(.{2})\\w{3}(.*)", "$1***$2"));
                deliveryWaybillList.add(deliveryWaybillVO);
            }
        }

        return new ItemResult<>(deliveryWaybillList);
    }
}
