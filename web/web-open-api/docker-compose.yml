
services:
  open-api-web:
    image: ecommerce-web-openapi:1.0
    volumes:
      - /remote/logs/open-api-web:/var/log
      - /remote/skywalking:/home/<USER>
    deploy:
      resources:
        limits:
          memory: 800m
    container_name: open-api-web
    restart: always
    ports:
      - "8087:8087"
    environment:
      - SPRING_CLOUD_CONFIG_URI=http://10.201.188.5:8888
      - SPRING_CLOUD_CONFIG_LABEL=asset-2025
      - SPRING_CLOUD_CONFIG_PROFILE=dev
      - TZ=Asia/Shanghai
      - JAVA_TOOL_OPTIONS=-Xms256m -Xmx512m -XX:+UseSerialGC