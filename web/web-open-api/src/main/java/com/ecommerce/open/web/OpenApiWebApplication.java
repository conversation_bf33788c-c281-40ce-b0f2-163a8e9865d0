package com.ecommerce.open.web;

import com.ecommerce.common.annotation.ExcludeFromComponetScan;
import com.ecommerce.common.config.mybatis.DatasourceConfig;
import com.ecommerce.common.config.mybatis.MybatisConfiguration;
import com.ecommerce.open.web.config.FeignSpringFormEncoder;
import feign.codec.Encoder;
import org.springframework.beans.factory.ObjectFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.cloud.openfeign.support.SpringEncoder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.session.config.annotation.web.http.EnableSpringHttpSession;

@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class, HibernateJpaAutoConfiguration.class})
@EnableFeignClients({
		"com.ecommerce.open.api",
		"com.ecommerce.base.api"
})
@EnableSpringHttpSession
@ComponentScan(value = {
		"com.ecommerce.open.web",
		"com.ecommerce.common.config",
		"com.ecommerce.common.service",
		"com.ecommerce.web.common"
}, excludeFilters = {
		@ComponentScan.Filter(type = FilterType.ANNOTATION, value = ExcludeFromComponetScan.class),
		@ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, value = MybatisConfiguration.class),
		@ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, value = DatasourceConfig.class)
})
@EnableDiscoveryClient
public class OpenApiWebApplication {

	private final ObjectFactory<HttpMessageConverters> messageConverters;

	public OpenApiWebApplication(ObjectFactory<HttpMessageConverters> messageConverters) {
		this.messageConverters = messageConverters;
	}

	@Bean
	@ConditionalOnMissingBean
	public Encoder feignEncoder() {
		return new FeignSpringFormEncoder(new SpringEncoder(this.messageConverters));
	}

	public static void main(String[] args) {
		SpringApplication.run(OpenApiWebApplication.class, args);
	}
}
