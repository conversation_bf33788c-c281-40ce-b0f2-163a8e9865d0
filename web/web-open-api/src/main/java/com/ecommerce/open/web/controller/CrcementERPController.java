package com.ecommerce.open.web.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.open.api.dto.apicenter.ApiResult;
import com.ecommerce.open.api.dto.apicenter.BaseClientResponseDTO;
import com.ecommerce.open.api.dto.apicenter.IdentityVerifyDTO;
import com.ecommerce.open.api.service.apicenter.IOpenAPIInvokeService;
import com.ecommerce.open.web.util.Base64Util;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import com.ecommerce.common.utils.CsStringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.alibaba.fastjson.JSON.parseObject;

/**
 *  CrcementERPController
 *
 * <AUTHOR>
 */
@RestController
@Slf4j
@RequestMapping("/crcementERP/anon")
public class CrcementERPController {

	public static final String EVENT_CODE_SELLER_ID_JSON = "eventCode===>{}, sellerId===>{},  json ===> {} ";
	public static final String CODE_E0B02D15 = "E0B02D15";
	public static final String ERROR_ERP_RESULT_LOG = " 接收到ERP请求 电商处理异常  {}";
	private final IOpenAPIInvokeService openAPIInvokeService;

	@Autowired
	public CrcementERPController(IOpenAPIInvokeService openAPIInvokeService) {
		this.openAPIInvokeService = openAPIInvokeService;
	}

	@Operation(summary = "接收通知接口的JSON并返回")
	@PostMapping(value = "/srmNotify")
	public String srmNotify(HttpServletRequest request, @RequestHeader HttpHeaders headers,
			HttpServletResponse response) throws Exception {
		BufferedReader reader = new BufferedReader(new InputStreamReader(request.getInputStream(), StandardCharsets.UTF_8)); // 实例化输入流，并获取网页代码
		String data; // 依次循环，至到读的值为空
		StringBuilder sb = new StringBuilder();
		while ((data = reader.readLine()) != null) {
			sb.append(data);
		}
		reader.close();
		String eventCode = "EC-PUR-A0";
		String sellerId = "xxxCRCEMENT01";
		String json = sb.toString();

		log.info(" 接收到SRM请求 before decode @RequestHeader===>{} ，@RequestBody===>{}", headers, json);
		json = Base64Util.decodeToString(json);
		log.info(" 接收到SRM请求 after decode @RequestHeader===>{} ，@RequestBody===>{}", headers, json);
		json = unicodeToString(json);

        if (CsStringUtils.isNotBlank(json)) {
            JSONObject jsonObject = parseObject(json);
            String sourceOrgid = jsonObject.getString("sourceOrgid");
            if (CsStringUtils.isNotBlank(sourceOrgid)) {
                sellerId = sourceOrgid;
            }
        }

        log.info(EVENT_CODE_SELLER_ID_JSON, eventCode, sellerId, json);
		ApiResult result = null;
		try {
		 result = openAPIInvokeService.invoke(eventCode, sellerId, json);
		} catch(Exception e) {
			log.error("接收到SRM请求，处理异常", e);
			return "{\"success\":false}";
		}
		log.error(" 接口中心处理结果 error result===> {}  ", result);
		if(result ==null || !result.isSuccess()) {
			return "{\"success\":false}";
		}
		return "{\"success\":true}";
	}

	@Operation(summary = "接收通知接口的JSON并返回")
	@PostMapping(value = "/notify")
	public String notify(HttpServletRequest request, @RequestHeader HttpHeaders headers, HttpServletResponse response)
			throws Exception {

		BufferedReader reader = new BufferedReader(new InputStreamReader(request.getInputStream(), StandardCharsets.UTF_8)); // 实例化输入流，并获取网页代码
		String data; // 依次循环，至到读的值为空
		StringBuilder sb = new StringBuilder();
		while ((data = reader.readLine()) != null) {
			sb.append(data);
		}
		reader.close();

		String json = sb.toString();
		log.info(" 接收到ERP请求 before @RequestHeader===>{} ，@RequestBody===>{}", headers, json);
		json = Base64Util.decodeToString(json);
		log.info(" 接收到ERP请求 after @RequestHeader===>{} ，@RequestBody===>{}", headers, json);

		String sellerId = headers.getFirst("custno");
		String eventCode = headers.getFirst("eventcode");
		String batchNum = headers.getFirst("requestno");
		String errorCode = "";
		String errorMessage = "";
		//请求来源验证
		IdentityVerifyDTO identityVerifyDTO = new IdentityVerifyDTO();
		identityVerifyDTO.setAuthorization(headers.getFirst("authorization"));
		ItemResult<Boolean> verifyResult = openAPIInvokeService.identityVerify(identityVerifyDTO);
		if (!verifyResult.getData()) {
			errorMessage = "鉴权失败，非法请求！";
			errorCode = "E204000";
		}
        if (CsStringUtils.isEmpty(sellerId)) {
			errorMessage = "请求头部信息缺失，custNo 未设置";
			errorCode = "E204001";
		}
        if (CsStringUtils.isEmpty(eventCode)) {
			errorMessage = "请求头部信息缺失，eventCode 未设置";
			errorCode = "E204002";
		}
        if (CsStringUtils.isEmpty(batchNum)) {
			errorMessage = "请求头部信息缺失，requestno 未设置";
			errorCode = "E204003";
		}
        if (CsStringUtils.isEmpty(json)) {
			errorMessage = "请求体缺失";
			errorCode = "E204004";
		}
        if (!CsStringUtils.isEmpty(errorCode)) {
			log.error(" 接收到ERP请求 异常errorCode ===> {} ，@errorMessage===>{}", errorCode, errorMessage);
			BaseClientResponseDTO baseClientResponseDTO = new BaseClientResponseDTO();
			baseClientResponseDTO.setCode(CODE_E0B02D15);
			baseClientResponseDTO.setMessage(cnToUnicode(errorMessage));
			baseClientResponseDTO.setRequestNo(batchNum);
			return toERPResponse(JSON.toJSONString(baseClientResponseDTO));
		}
		try {
			return getNotifyResponseString(json, eventCode, sellerId, batchNum);
		} catch (Exception e) {
			log.error("接收到ERP请求，处理异常", e);
			log.error(" ============> 接收到ERP请求 电商处理异常  {}", e.getCause() == null ? e.getMessage() : e.getCause().getMessage());
			BaseClientResponseDTO baseClientResponseDTO = new BaseClientResponseDTO();
			baseClientResponseDTO.setCode(CODE_E0B02D15);
			baseClientResponseDTO
					.setMessage(cnToUnicode(e.getCause() == null ? e.getMessage() : e.getCause().getMessage()));
			baseClientResponseDTO.setRequestNo(batchNum);
			return toERPResponse(JSON.toJSONString(baseClientResponseDTO));
		}
	}

	@NotNull
	private String getNotifyResponseString(String json, String eventCode, String sellerId, String batchNum) {
		ApiResult result;
		json = unicodeToString(json);
		log.info(EVENT_CODE_SELLER_ID_JSON, eventCode, sellerId, json);
		JSONObject jsonObj = parseObject(json);
		JSONObject jsonRequest = jsonObj.getJSONObject("request");
		jsonRequest.put("ifFirstSaveClientRequest", "1");
		log.info(EVENT_CODE_SELLER_ID_JSON, eventCode, sellerId, jsonRequest.toJSONString());
		result = openAPIInvokeService.invoke(eventCode, sellerId, jsonRequest.toJSONString());
		log.error(" 接口中心处理结果 result===> {}  ", result);
		if (result == null) {
			log.error(ERROR_ERP_RESULT_LOG, "ec result is null");
			BaseClientResponseDTO baseClientResponseDTO = new BaseClientResponseDTO();
			baseClientResponseDTO.setCode(CODE_E0B02D15);
			baseClientResponseDTO.setMessage(cnToUnicode("接口中心反馈空值"));
			baseClientResponseDTO.setRequestNo(batchNum);
			return toERPResponse(JSON.toJSONString(baseClientResponseDTO));
		} else {
			if (result.isSuccess()) {
				String resultData = result.getData().replace("\"code\":\"01\"", "\"code\":\"S1A00000\"");
				log.info("resultData===>{}", resultData);
				return toERPResponse(resultData);
			} else {
				BaseClientResponseDTO baseClientResponseDTO = new BaseClientResponseDTO();
				baseClientResponseDTO.setCode(CODE_E0B02D15);
				baseClientResponseDTO.setMessage(cnToUnicode(result.getDescription()));
				baseClientResponseDTO.setRequestNo(batchNum);
				log.info("电商处理失败 自动构建处理结果===>{}", baseClientResponseDTO);
				return toERPResponse(JSON.toJSONString(baseClientResponseDTO));
			}
		}
	}

	// 与上面方法区别在于不用base64解密
	@Operation(summary = "接收通知接口的JSON并返回")
	@PostMapping(value = "/notifyMock")
	public String notifyMock(HttpServletRequest request, @RequestHeader HttpHeaders headers,
			HttpServletResponse response) throws Exception {

		BufferedReader reader = new BufferedReader(new InputStreamReader(request.getInputStream(), StandardCharsets.UTF_8)); // 实例化输入流，并获取网页代码
		String data; // 依次循环，至到读的值为空
		StringBuilder sb = new StringBuilder();
		while ((data = reader.readLine()) != null) {
			sb.append(data);
		}
		reader.close();

		String json = sb.toString();
		log.info(" 接收到ERP请求 @RequestHeader===>{} ，@RequestBody===>{}", headers, json);
		log.info(" 接收到ERP请求 @RequestHeader===>{} ，@RequestBody===>{}", headers, json);

		String sellerId = headers.getFirst("custno");
        if (CsStringUtils.isEmpty(sellerId)) {
			sellerId = headers.getFirst("custNo");
		}
		String eventCode = headers.getFirst("eventcode");
        if (CsStringUtils.isEmpty(eventCode)) {
			eventCode = headers.getFirst("eventCode");
		}
		String batchNum = headers.getFirst("requestno");
        if (CsStringUtils.isEmpty(batchNum)) {
			batchNum = headers.getFirst("requestNo");
		}
		String errorCode = "";
		String errorMessage = "";
        if (CsStringUtils.isEmpty(sellerId)) {
			errorMessage = "请求头部信息缺失，custNo 未设置";
			errorCode = "E204001";
		}
        if (CsStringUtils.isEmpty(eventCode)) {
			errorMessage = "请求头部信息缺失，eventCode 未设置";
			errorCode = "E204002";
		}
        if (CsStringUtils.isEmpty(batchNum)) {
			errorMessage = "请求头部信息缺失，requestno 未设置";
			errorCode = "E204003";
		}
        if (CsStringUtils.isEmpty(json)) {
			errorMessage = "请求体缺失";
			errorCode = "E204004";
		}
        if (!CsStringUtils.isEmpty(errorCode)) {
			log.error(" 接收到ERP请求 异常errorCode ===> {} ，@errorMessage===>{}", errorCode, errorMessage);
			BaseClientResponseDTO baseClientResponseDTO = new BaseClientResponseDTO();
			baseClientResponseDTO.setCode(CODE_E0B02D15);
			baseClientResponseDTO.setMessage(cnToUnicode(errorMessage));
			baseClientResponseDTO.setRequestNo(batchNum);
			return JSON.toJSONString(baseClientResponseDTO);
		}
		try {
			return getResponseString(json, eventCode, sellerId, batchNum);
		} catch (Exception e) {
			log.error("接收到ERP请求，处理异常", e);
			log.error(ERROR_ERP_RESULT_LOG, e.getCause() == null ? e.getMessage() : e.getCause().getMessage());
			BaseClientResponseDTO baseClientResponseDTO = new BaseClientResponseDTO();
			baseClientResponseDTO.setCode(CODE_E0B02D15);
			baseClientResponseDTO
					.setMessage(cnToUnicode(e.getCause() == null ? e.getMessage() : e.getCause().getMessage()));
			baseClientResponseDTO.setRequestNo(batchNum);
			return JSON.toJSONString(baseClientResponseDTO);
		}
	}

	private String getResponseString(String json, String eventCode, String sellerId, String batchNum) {
		ApiResult result;
		json = unicodeToString(json);
		log.info(EVENT_CODE_SELLER_ID_JSON, eventCode, sellerId, json);
		JSONObject jsonObj = parseObject(json);
		json = jsonObj.getString("request");
		log.info(EVENT_CODE_SELLER_ID_JSON, eventCode, sellerId, json);
		result = openAPIInvokeService.invoke(eventCode, sellerId, json);
		log.error(" 接口中心处理结果 result===> {}  ", result);
		if (result == null) {
			log.error(ERROR_ERP_RESULT_LOG, "ec result is null");
			BaseClientResponseDTO baseClientResponseDTO = new BaseClientResponseDTO();
			baseClientResponseDTO.setCode(CODE_E0B02D15);
			baseClientResponseDTO.setMessage(cnToUnicode("接口中心反馈空值"));
			baseClientResponseDTO.setRequestNo(batchNum);
			return JSON.toJSONString(baseClientResponseDTO);
		} else {
			if (result.isSuccess()) {
				String resultData = result.getData().replace("\"code\":\"01\"", "\"code\":\"S1A00000\"");
				log.info("resultData===>{}", resultData);
				return "{\"response\":" + resultData + "}";
			} else {
				BaseClientResponseDTO baseClientResponseDTO = new BaseClientResponseDTO();
				baseClientResponseDTO.setCode(CODE_E0B02D15);
				baseClientResponseDTO.setMessage(cnToUnicode(result.getDescription()));
				baseClientResponseDTO.setRequestNo(batchNum);
				return JSON.toJSONString(baseClientResponseDTO);
			}

		}
	}

	public static String unicodeToString(String str) {
		Pattern pattern = Pattern.compile("(\\\\u(\\p{XDigit}{4}))");
		Matcher matcher = pattern.matcher(str);
		char ch;
		while (matcher.find()) {
			String group = matcher.group(2);
			ch = (char) Integer.parseInt(group, 16);
			String group1 = matcher.group(1);
			str = str.replace(group1, ch + "");
		}
		return str;
	}

	private static String cnToUnicode(String cn) {
		return cn;
	}

	private String toERPResponse(String ecResponse) {
		ecResponse = createResponse(ecResponse);
		log.info("send to ERP response before encode ===>{}", ecResponse);
		String result = Base64Util.encodeToString(ecResponse.getBytes()).replaceAll("[\\s*\t\n\r]", "");
		log.info("send to ERP response after encode ===>{}", result);
		return result;
	}

	private String createResponse(String data) {
		return "{\"response\":" + data + "}";
	}

	public static void main(String[] args) {
		String request = """
                {
                  "request" : {
                    "custNo" : "xxxCRCEMENT01",
                    "eventCode" : "EC-LOG-U0",
                    "requestNo" : "CRCERP20190510095813569526",
                    "billStatus" : "01",
                    "ecClearStatus" : "",
                    "ecBillCode" : "",
                    "externalSellerId" : 101,
                    "ecPlanBillId" : "D11190423000028",
                    "erpDistributeCode" : "2019042800007",
                    "externalBillId" : "2019042800007-0001",
                    "ecTakeInfoCode" : "1411190509000002",
                    "logisticAmount" : 1,
                    "logisticUnitPrice" : 10,
                    "bookMark" : "",
                    "bookingStartTime" : "",
                    "bookingEndTime" : "",
                    "greenChannelMark" : "",
                    "deliveryEndDate" : "20190509112812",
                    "unloadingPlaceCode" : "XH-SGHG-114079",
                    "warehouseCode" : "249",
                    "warehouseName" : "",
                    "receiverName" : "大哥哥",
                    "receiverContact" : "xxx",
                    "receiveAddress" : "果乐乡",
                    "receiveLocation" : "",
                    "lastUpdateTime" : "20190509182352",
                    "extend1" : "",
                    "extend2" : "",
                    "extend3" : "",
                    "extend4" : "",
                    "extend5" : "",
                    "vehicleList" : [{
                      "transportCategory" : "",
                      "ecCarrierNo" : "",
                      "erpCarrierNo" : "100607",
                      "carrierName" : "xxx运输有限公司",
                      "vehicleNo" : "粤L67830",
                      "drivingLicense" : "",
                      "selfCapacity" : 0,
                      "loadCapacity" : null,
                      "realCapacity" : null,
                      "vehicle_type_id" : "罐车",
                      "InOutMode" : "出厂",
                      "transportationMode" : "配送",
                      "driverName" : "",
                      "driverPhone" : "",
                      "driverIdentity" : "",
                      "comments" : "",
                      "enableTime" : "20181219154012",
                      "disableTime" : "",
                      "length" : "",
                      "width" : "",
                      "height" : "",
                      "axles" : "",
                      "isGpsDevice" : "01",
                      "color" : "",
                      "gpsManufacturerId" : "",
                      "gpsDeviceNumber" : "14131120708",
                      "simNumber" : "14131120708",
                      "extend1" : "",
                      "extend2" : "",
                      "extend3" : "",
                      "extend4" : "",
                      "extend5" : ""
                    }],
                    "goodsList" : [{
                      "rowNo" : 1,
                      "billWeight" : 0.1,
                      "commodityCode" : "10000006",
                      "qrCode" : "SO020681222@2019-05-09@xx物资有限公司@P·O 42.5R_散装@粤L67830",
                      "realBillWeight" : "",
                      "realCostAmount" : "",
                      "realUnitPrice" : "  2000.00",
                      "extend1" : "",
                      "extend2" : "",
                      "extend3" : "",
                      "extend4" : "",
                      "extend5" : ""
                    }],
                    "statusList" : [{
                      "rowNo" : 1,
                      "executeStatus" : "01",
                      "commandTime" : "20190428170324",
                      "mome" : "",
                      "extend1" : "",
                      "extend2" : "",
                      "extend3" : "",
                      "extend4" : "",
                      "extend5" : ""
                    }]
                  }
                }\
                """;
		String result = Base64Util.encodeToString(request.getBytes()).replaceAll("[\\s*\t\n\r]", "");
		log.info("send to ERP response ===>{}", result);
	}
}
