package com.ecommerce.open.web.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ecommerce.common.exception.BasicRuntimeException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.open.web.enums.BizCodeMockEnum;
import com.ecommerce.open.web.util.Base64Util;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.stringtemplate.v4.ST;

/**
 * ERPMockController
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/erpMock")
public class ERPMockController {

    @Resource(name = "bizRedisTemplate")
    private RedisTemplate<String, String> bizRedisTemplate;

    @PostMapping("/doRequest")
    public String doRequest(@RequestBody String json) {
        log.info("@json报文 === >" + json);
        JSONObject jsonObject = JSON.parseObject(json);
        JSONObject headers = jsonObject.getJSONObject("PROCESS_SN_REQUEST").getJSONObject("RESTHeader");
        log.info("@RequestHeader === >" + headers);
        JSONObject inputParameters = jsonObject.getJSONObject("PROCESS_SN_REQUEST").getJSONObject("InputParameters");
        log.info("doPostRequest start");
        String bizCode = inputParameters.getString("P_IFACE_CODE");
        String batchNum = inputParameters.getString("P_BATCH_NUMBER");
        log.info("@bizCode === >" + bizCode);

        BizCodeMockEnum bizCodeMockEnum = BizCodeMockEnum.getByCode(bizCode);
        if (bizCodeMockEnum == null) {
            throw new BasicRuntimeException("not found bizCode:" + bizCode);
        }
        String flag = bizRedisTemplate.opsForValue().get("OPEN_API_WEB:ERP_MOC:" + bizCode);

        String data = "{\"response\": " + bizCodeMockEnum.message() + "}";

        log.info("data befor rander===>{}", data);
        ST datast = new ST(data);
        if (CsStringUtils.equals(flag, "FAIL")) {
            datast.add("code", "00");
            datast.add("message", "mock的失败");
        } else {
            datast.add("code", "01");
            datast.add("message", "mock的成功");
        }
        datast.add("requestNo", batchNum);
        datast.add("externalSellerId", "xxxCRCEMENT01");
        data = datast.render();

        log.info("data befor encoder===>" + data);
        data = Base64Util.encodeToString(data.getBytes());
        log.info("data after encoder===>" + data);

        String template = "{\"OutputParameters\":{\n" +
                "\"X_RESPONSE_DATA\":\"<data>\",\n" +
                "        \"@xmlns:xsi\":\"http://www.w3.org/2001/XMLSchema-instance\",\n" +
                "        \"@xmlns\":\"http://xmlns.oracle.com/apps/cux/rest/EC_SN_processws/process_sn_request/\",\n" +
                "        \"X_RETURN_MESG\" : '测试业务逻辑完整', \n" +
                "        \"X_RETURN_CODE\":\"<code>\"\n" +
                "    }\n" +
                "}";
        ST messagest = new ST(template);
        messagest.add("data", data);
        if (CsStringUtils.equals(flag, "FAIL")) {
            String failCode = "E0B02D15";
            messagest.add("code", failCode);
        } else {
            String successCode = "S1A00000";
            messagest.add("code", successCode);
        }
        String message = messagest.render();
        log.info("返回的结果===>{}", message);
        log.info("doPostRequest end");

        return message;
    }

    @GetMapping("/changeMockResult")
    public ItemResult<Void> changeMockResult(@RequestParam String bizCode, @RequestParam String result) {
        bizRedisTemplate.opsForValue().set("OPEN_API_WEB:ERP_MOC:" + bizCode, result);
        return new ItemResult<>(null);
    }

}
