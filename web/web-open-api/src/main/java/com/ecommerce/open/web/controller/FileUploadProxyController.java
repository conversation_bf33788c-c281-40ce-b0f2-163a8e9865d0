package com.ecommerce.open.web.controller;

import com.alibaba.fastjson.JSON;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.member.api.dto.exception.MemberCode;
import com.ecommerce.member.api.session.LoginInfo;
import com.ecommerce.open.api.service.IFilePutService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @Date: 07/12/2018 11:04
 * @DESCRIPTION:
 */
@RestController
@RequestMapping("/file")
@Slf4j
@Tag(name = "FileUploadProxyController", description = "附件上传中转服务")
public class FileUploadProxyController {


    public static final String UNKNOWN = "unknown";
    private final IFilePutService filePutService;
    private final RedisTemplate<String, Object> bizRedisTemplate;

    @Autowired
    public FileUploadProxyController(IFilePutService filePutService, RedisTemplate<String, Object> bizRedisTemplate) {
        this.filePutService = filePutService;
        this.bizRedisTemplate = bizRedisTemplate;
    }

    @Operation(summary = "文件上传(表单提交)")
    @PostMapping(value = "/upload")
    public ItemResult<List<Map<String, String>>> putFiles(
            @Parameter(hidden = true) LoginInfo loginInfo,
            @Parameter(description = "应文件名") @RequestParam(value = "fileName", required = false) String[] fileNames,
            @RequestParam(required = false) String bid,
            @Parameter(description = "应用场景") @RequestParam String businessScenario,
            @Parameter(description = "是否保存文件信息到附件表(默认false)") @RequestParam(defaultValue = "0" ) Boolean saveAttachmentInfo,
            @Parameter(description = "要上传的文件") @RequestParam(value = "file") MultipartFile[] files,
            HttpServletRequest request) throws Exception {
        if (loginInfo == null || CsStringUtils.isBlank(loginInfo.getAccountId())) {
            throw new BizException(MemberCode.LOGIN_ERROR,"你还没有登录，请先登录");
        }
        if(fileNames.length < 1){
            throw new BizException(MemberCode.MEMBER_OTHER_ERROR,"上传失败,上传文件为空");
        }
        Map<String,String> fileMap = new HashMap<>();
        for(int i = 0;i < fileNames.length;i++){
            if(fileMap.containsKey(fileNames[i])){
                throw new BizException(MemberCode.MEMBER_OTHER_ERROR,"上传失败,上传文件重名");
            }
            fileMap.put(fileNames[i],JSON.toJSONString(files[i].getBytes()));
        }
        log.info("redisKey==========>{}","OPEN_API_WEB:FILE_UPLOAD:" + loginInfo.getAccountId());
        bizRedisTemplate.opsForHash().putAll("OPEN_API_WEB:FILE_UPLOAD:" + loginInfo.getAccountId(), fileMap);
        return new ItemResult<>(filePutService.putFiles(fileNames,loginInfo.getAccountId(),businessScenario,bid,saveAttachmentInfo,getIpAddr(request),loginInfo.getAccountId()));
    }

    public static String getIpAddr(HttpServletRequest request){
        if (request == null) {
            return UNKNOWN;
        }
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.isEmpty() || UNKNOWN.equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || UNKNOWN.equalsIgnoreCase(ip)) {
            ip = request.getHeader("X-Forwarded-For");
        }
        if (ip == null || ip.isEmpty() || UNKNOWN.equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || UNKNOWN.equalsIgnoreCase(ip)) {
            ip = request.getHeader("X-Real-IP");
        }

        if (ip == null || ip.isEmpty() || UNKNOWN.equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }
}
