package com.ecommerce.open.web.controller;

import com.ecommerce.open.api.dto.gnete.GuaranteedPayNotifyDTO;
import com.ecommerce.open.web.enums.WechatNotifyCodeEnum;
import com.ecommerce.open.web.service.IHttpService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */

@RestController
@Slf4j
@RequestMapping("/http/anon")
public class HttpController {

    private final IHttpService iHttpService;

    // 使用构造函数注入
    @Autowired
    public HttpController(IHttpService iHttpService) {
        this.iHttpService = iHttpService;
    }


   /**
    * 微信支付回调
    * @param request
    * @return
    * @throws Exception
    */
   @PostMapping(value = "/weChatNotify", produces = "application/xml")
   public void getWechatHttp(HttpServletRequest request,HttpServletResponse response) throws IOException {
       String a = iHttpService.getWechatHttp(request, WechatNotifyCodeEnum.WEIXIN_PAY);
       response.getOutputStream().write(a.getBytes());
       response.getOutputStream().flush();
   }
   
// @ApiOperation("接收通知接口的JSON并返回")
@PostMapping(value = "/notify")
 public String notify(HttpServletRequest request,HttpServletResponse response)throws Exception{
	 
	 
	 
 	log.info(" 获取到请求：@RequestHeader{}" ,  request );
     return "success";
 }

   /**
    * 微信退款回调
    * @param request
    * @return
    * @throws Exception
    */
   @PostMapping(value = "/weChatRefundNotify", produces = "application/xml")
   public void getWechatRefundHttp(HttpServletRequest request, HttpServletResponse response) throws IOException {
      String a = iHttpService.getWechatHttp(request, WechatNotifyCodeEnum.WEIXIN_REFUND);
      response.getOutputStream().write(a.getBytes());
      response.getOutputStream().flush();
   }

   /**
    * 支付宝异步回调
    * 
    * @param request
    * @return
    * @throws Exception
    */
   @PostMapping(value = "/alipayNotify")
   public String alipayNotify(HttpServletRequest request) {
      return iHttpService.alipayNotify(request);
   }

   /**
    * 东方付通回调
    * @param request
    * @return
    * @throws Exception
    */
   @PostMapping(value = "/easterPayNotify", produces = "application/json")
   public String easterPayNotify(HttpServletRequest request) {
      return iHttpService.easterPayNotify(request);
   }

   /**
    * 见证宝 - 充值回调
    * @return
    * @throws Exception
    */
   @PostMapping(value = "/pinganjzRechargeNotify")
   public ResponseEntity<String> pinganjzRechargeNotify(@RequestParam(required = false) String orig, @RequestParam(required = false) String sign) {
      try {
         String notify = iHttpService.pinganjzRechargeNotify(orig, sign);
         if (!"ok".equals(notify)) {
             return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(notify);
         }
      } catch (Exception e) {
         log.error(e.getMessage(),e);
      }
      return ResponseEntity.status(HttpStatus.OK).build();
   }

   /**
    * 见证宝 - 密码回调
    * @return
    * @throws Exception
    */
   @PostMapping(value = "/pinganjzPasswordNotify")
   public String pinganjzPasswordNotify(HttpServletRequest request) {
      return iHttpService.pinganjzPasswordNotify(request);
   }

   /**
    * 见证宝 - 聚合支付回调
    * @return
    * @throws Exception
    */
   @PostMapping(value = "/pinganjzAggregationNotify")
   public String pinganjzAggregationNotify(HttpServletRequest request) {
      return iHttpService.pinganjzAggregationNotify(request);
   }

    /**
     * 银联支付-全民付移动支付C扫B 支付回调
     */
    @PostMapping(value = "/gneteAggregatePayNotify")
    public String gneteAggregatePayNotify(HttpServletRequest request) {
        return iHttpService.gneteAggregatePayNotify(request);
    }

    /**
     * 银联支付-担保相关回调都在这里了
     */
    @PostMapping(value = "/guaranteedPayNotify")
    public String guaranteedPayNotify(@RequestBody GuaranteedPayNotifyDTO dto) {
        return iHttpService.guaranteedPayNotify(dto);
    }



}
