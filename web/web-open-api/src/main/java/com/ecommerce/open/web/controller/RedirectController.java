package com.ecommerce.open.web.controller;

import com.ecommerce.open.web.service.IHttpService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @created 14:55 18/04/2019
 */
@Slf4j
@RestController
@RequestMapping("/redirect")
public class RedirectController {

    private final IHttpService iHttpService;

    // 使用构造函数注入
    @Autowired
    public RedirectController(IHttpService iHttpService) {
        this.iHttpService = iHttpService;
    }
}
