package com.ecommerce.open.web.enums;

import com.ecommerce.common.utils.CsStringUtils;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 *  BizCodeMockEnum
 *
 * <AUTHOR>
 */
public enum BizCodeMockEnum {

    EC_MEM_D0("EC-MEM-D0", """
            {
                "code":"<code>",
                "message":"<message>",
                "requestNo":"12325346457",
                "memberList":[
                    {
                        "memCode":"lucd_mem001",
                        "memberStatus":"01",
                        "memberName":"第一个",
                        "memintro":"第一次测试",
                        "externalSellerId":"<externalSellerId>",
                        "memberSallType":"EC",
                        "memberGroup":"EC",
                        "taxregisterNo":"lucd_tax_001",
                        "contry":"中国",
                        "province":"广东省",
                        "city":"广州市",
                        "area":"增城区",
                        "address":"仙村厂",
                        "extend1":"预留字段1",
                        "extend2":"预留字段2",
                        "extend3":"预留字段3",
                        "extend4":"预留字段4",
                        "extend5":"预留字段5"
                    },
            		{
                        "memCode":"lucd_mem002",
                        "memberStatus":"01",
                        "memberName":"第二个",
                        "memintro":"第二次测试",
                        "externalSellerId":"<externalSellerId>",
                        "memberSallType":"EC",
                        "memberGroup":"EC",
                        "taxregisterNo":"lucd_tax_002",
                        "contry":"中国",
                        "province":"广东省",
                        "city":"广州市",
                        "area":"增城区",
                        "address":"广达厂",
                        "extend1":"预留字段1",
                        "extend2":"预留字段2",
                        "extend3":"预留字段3",
                        "extend4":"预留字段4",
                        "extend5":"预留字段5"
                    }
                ]
            }\
            """),
    EC_MEM_A3("EC-MEM-A3", """
            {
                "code":"01",
                "message":"处理成功",
                "requestNo":"34546745656",
                "memberCode":"A00200",
                "procResult":"100",
                "mdmCode":"erp_lucd_mdm001",
                "memberStatus":"1",
                "procMsg":"成功",
                "address":"A位置",
                "extend1":"预留字段",
                "extend2":"预留字段",
                "extend3":"预留字段",
                "extend4":"预留字段",
                "extend5":"预留字段"
            }\
            """),
    EC_GDS_C0("EC-GDS-C0", """
            {
                "code":"<code>",
                "message":"<message>",
                "requestNo":"<requestNo>",
                "goodsList":[
                    {
                        "externalSellerId":"<externalSellerId>",
                        "commodityCode":"erp_lucd_wl001",
                        "commodityName":"黄金",
                        "packageType":"100",
                        "unit":"吨",
                        "extend1":"预留字段1",
                        "extend2":"预留字段3",
                        "extend4":"预留字段4",
                        "extend5":"预留字段5"
                    },
            		{
                        "externalSellerId":"<externalSellerId>",
                        "commodityCode":"erp_lucd_wl002",
                        "commodityName":"钻石",
                        "packageType":"100",
                        "unit":"克拉",
                        "extend1":"预留字段1",
                        "extend2":"预留字段3",
                        "extend4":"预留字段4",
                        "extend5":"预留字段5"
                    },
            		{
                        "externalSellerId":"<externalSellerId>",
                        "commodityCode":"erp_lucd_wl003",
                        "commodityName":"铂金",
                        "packageType":"200",
                        "unit":"袋",
                        "extend1":"预留字段1",
                        "extend2":"预留字段3",
                        "extend4":"预留字段4",
                        "extend5":"预留字段5"
                    }
                ]
            }\
            """),
    EC_GDS_D0("EC-GDS-D0", """
            {
                "code":"01",
                "message":"查询成功",
                "requestNo":"12234",
                "pickupPointList":[
                    {
                        "externalSellerId":"xxxCRCEMENT01",
                        "pickupPointCode":"erp_lucd_wh001",
                        "pickupPointName":"大学城中心仓",
                        "pickupPointOrgCode":"erp_lucd_org001",
                        "countryCode":"872",
                        "countryName":"大中华区",
                        "provinceCode":"440000",
                        "provinceName":"广东省",
                        "cityCode":"440300",
                        "cityName":"深圳市",
                        "districtCode":"440303",
                        "districtName":"罗湖区",
                        "streetCode":"44030303",
                        "streetName":"黄贝街道",
                        "address":"丹枫白露酒店",
                        "location":"114.139343,22.544868",
                        "contactName":"白鹭",
                        "contactPhone":"13311365785",
                        "extend1":"预留字段1",
                        "extend2":"预留字段3",
                        "extend4":"预留字段4",
                        "extend5":"预留字段5"
                    },
            		{
                        "externalSellerId":"xxxCRCEMENT01",
                        "pickupPointCode":"erp_lucd_wh002",
                        "pickupPointName":"小学城中心仓",
                        "pickupPointOrgCode":"erp_lucd_org001",
                        "countryCode":"872",
                        "countryName":"大中华区",
                        "provinceCode":"440000",
                        "provinceName":"广东省",
                        "cityCode":"440300",
                        "cityName":"深圳市",
                        "districtCode":"440303",
                        "districtName":"罗湖区",
                        "streetCode":"44030303",
                        "streetName":"黄贝街道",
                        "address":"小白露酒店",
                        "location":"114.139396,22.544855",
                        "contactName":"丹枫",
                        "contactPhone":"13111365785",
                        "extend1":"预留字段1",
                        "extend2":"预留字段3",
                        "extend4":"预留字段4",
                        "extend5":"预留字段5"
                    }
                ]
            }\
            """),
    EC_GDS_F0("EC-GDS-F0", """
            {
                "code":"<code>",
                "message":"<message>",
                "requestNo":"<requestNo>",
                "orgList":[
                    {
                        "orgCode":"erp_lucd_org001",
                        "orgName":"erp第一大区",
                        "orgDes":"天朝第一区",
                        "orgType":"00",
                        "extend1":"预留字段1",
                        "extend2":"预留字段2",
                        "extend3":"预留字段3",
                        "extend4":"预留字段4",
                        "extend5":"预留字段5"
                    },
            		{
                        "orgCode":"erp_lucd_org002",
                        "orgName":"erp第一中区",
                        "orgDes":"销售组织说明",
                        "parentCode":"erp_lucd_org001",
                        "orgType":"01",
                        "extend1":"预留字段1",
                        "extend2":"预留字段2",
                        "extend3":"预留字段3",
                        "extend4":"预留字段4",
                        "extend5":"预留字段5"
                    },
            		{
                        "orgCode":"erp_lucd_org003",
                        "orgName":"erp第一小区",
                        "orgDes":"小区",
                        "parentCode":"erp_lucd_org002",
                        "orgType":"02",
                        "extend1":"预留字段1",
                        "extend2":"预留字段2",
                        "extend3":"预留字段3",
                        "extend4":"预留字段4",
                        "extend5":"预留字段5"
                    },
            		{
                        "orgCode":"erp_lucd_org004",
                        "orgName":"erp第二中区",
                        "orgDes":"中区",
                        "parentCode":"erp_lucd_org001",
                        "orgType":"01",
                        "extend1":"预留字段1",
                        "extend2":"预留字段2",
                        "extend3":"预留字段3",
                        "extend4":"预留字段4",
                        "extend5":"预留字段5"
                    }
                ]
            }\
            """),
    EC_CTS_C0("EC-CTS-C0", """
            {
                "code":"<code>",
                "message":"<message>",
                "requestNo":"<requestNo>",
                "externalSellerId":"<externalSellerId>",
                "contractCode":"lucd_ec_ctc001",
                "erpContractCode":"lucd_erp_ctc001",
                "memCode":"erp_lucd_mdm001",
                "contractStatus":"01",
                "startDate":"**************",
                "endDate":"**************",
                "mdmName":"第一个客户",
                "deliveryWay":"01",
                "deliveryAddress":"企业天地8号德勤大楼(渝中区瑞天路)",
                "payWay":"01",
                "payRemark":"付款备注",
                "billType":"01",
                "erpSalemanAccountId":"erp_lucd_act001",
                "erpSalemanRealName":"ERP业务员名字",
                "orgCode":"erp_lucd_org001",
                "extend1":"预留字段1",
                "extend2":"预留字段3",
                "extend4":"预留字段4",
                "extend5":"预留字段5",
                "contractList":[
                    {
                        "rowNo":"行项目号",
                        "externalSellerId":"<externalSellerId>",
                        "commodityCode":"commodityCode",
                        "commodityName":"商品名称",
                        "packageType":"100",
                        "unit":"吨",
                        "outGoodsAddress":"磁器口街道磁正街社区居民委员会",
                        "shipPrice":"1.00",
                        "flowMonitor":"01",
                        "outGoodsAddressId":"00001",
                        "extend1":"预留字段1",
                        "extend2":"预留字段3",
                        "extend4":"预留字段4",
                        "extend5":"预留字段5"
                    }
                ]
            }\
            """),
    EC_FAP_A0("EC-FAP-A0", """
            {
                "code":"<code>",
                "message":"<message>",
                "requestNo":"<requestNo>",
                "balance":"9999.99",
                "credit":"9999.99",
                "externalSellerId":"<externalSellerId>",
                "extend1":"预留字段1",
                "extend2":"预留字段2",
                "extend3":"预留字段3",
                "extend4":"预留字段4",
                "extend5":"预留字段5"
            }\
            """),
    EC_LOG_B0("EC-LOG-B0", """
            {
            		"code": "<code>",
            		"message": "<message>",
            		"requestNo": "<requestNo>",
            		"balance": "2",
            		"credit": "121",
            		"totalCredit": "123",
            		"externalSellerId": "<externalSellerId>",
            		"extend1": "预留字段1",
            		"extend2": "预留字段2",
            		"extend3": "预留字段3",
            		"extend4": "预留字段4",
            		"extend5": "预留字段5"
            	}\
            """),
EC_LOG_S0("EC-LOG-S0", """
            {
            		"code": "<code>",
            		"message": "<message>",
            		"requestNo": "<requestNo>",
            		"balance": "2",
            		"credit": "121",
            		"totalCredit": "123",
            		"externalSellerId": "<externalSellerId>",
            		"extend1": "预留字段1",
            		"extend2": "预留字段2",
            		"extend3": "预留字段3",
            		"extend4": "预留字段4",
            		"extend5": "预留字段5"
            	}\
            """),
EC_LOG_Z0("EC-LOG-Z0", """
            {
            		"code": "<code>",
            		"message": "<message>",
            		"requestNo": "<requestNo>",
            		"balance": "2",
            		"credit": "121",
            		"totalCredit": "123",
            		"externalSellerId": "<externalSellerId>",
            		"extend1": "预留字段1",
            		"extend2": "预留字段2",
            		"extend3": "预留字段3",
            		"extend4": "预留字段4",
            		"extend5": "预留字段5"
            	}\
            """),
//    EC_LOG_T1("EC-LOG-T1", "运单信息查询-电商发起"),
    EC_LOG_Q0("EC-LOG-Q0", """
        {
            "code":"<code>",
            "message":"<message>",
            "requestNo":"<requestNo>"
            "clearStatus":"02"
        }\
        """),
//    EC_LOG_L0("EC-LOG-L0", "排队情况查询"),
//    EC_MON_T0("EC-MON-T0", "卖家提现通知"),
//    EC_LOG_K0("EC-LOG-K0", "车辆轨迹信息传输"),
//    EC_PUR_A0("EC-PUR-A0", "采购需求推送"),
    DEFAULT("DEFAULT", """
            {
                "code":"<code>",
                "message":"<message>",
                "requestNo":"<requestNo>"
            }\
            """)
    ;

    private String code;

    private String message;

    BizCodeMockEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }
    public static BizCodeMockEnum getByCode(String code) {
        for (BizCodeMockEnum _enum : values()) {
            if (CsStringUtils.equals(code, _enum.code)) {
                return _enum;
            }
        }
        return DEFAULT;
    }


    public static List<String> getAllCode() {
        return Stream.of(values()).map(BizCodeMockEnum::code).toList();
    }


    public String code() {
        return code;
    }
    public String message() {
        return message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
    
}
