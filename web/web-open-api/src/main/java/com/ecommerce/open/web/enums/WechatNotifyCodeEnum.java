package com.ecommerce.open.web.enums;

import lombok.Getter;

@Getter
public enum WechatNotifyCodeEnum {

    /**
     * 微信支付回调
     */
    WEIXIN_PAY("weixin_pay", "微信支付回调"),

    /**
     * 微信退款回调
     */
    WEIXIN_REFUND("weixin_refund", "微信退款回调")
    ;

    private String code;

    private String message;

    WechatNotifyCodeEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }


}
