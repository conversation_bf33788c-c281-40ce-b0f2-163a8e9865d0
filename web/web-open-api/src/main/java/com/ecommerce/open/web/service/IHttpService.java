package com.ecommerce.open.web.service;

import com.ecommerce.open.api.dto.gnete.GuaranteedPayNotifyDTO;
import com.ecommerce.open.web.enums.WechatNotifyCodeEnum;

import jakarta.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
public interface IHttpService {

     /**
      * 获取微信发送的HTTP请求
      */
     String getWechatHttp(HttpServletRequest request, WechatNotifyCodeEnum wechatNotifyCodeEnum);

     /**
      * 支付宝异步回调
      * 
      * @param request
      * @return
      */
     String alipayNotify(HttpServletRequest request);

     /**
      * 东方付通异步回调
      * @param request
      * @return
      */
     String easterPayNotify(HttpServletRequest request);

     /**
      * 见证宝 - 充值回调
      * @return
      */
     String pinganjzRechargeNotify(String orig, String sign);


     /**
      * 见证宝 - 密码回调
      * @param request
      * @return
      */
     String pinganjzPasswordNotify(HttpServletRequest request);

     /**
      * 见证宝 - 聚合支付回调
      * @param request
      * @return
      */
     String pinganjzAggregationNotify(HttpServletRequest request);

     String gneteAggregatePayNotify(HttpServletRequest request);

     String guaranteedPayNotify(GuaranteedPayNotifyDTO dto);
}
