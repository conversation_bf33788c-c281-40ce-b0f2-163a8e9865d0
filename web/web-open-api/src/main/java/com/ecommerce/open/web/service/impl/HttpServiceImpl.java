package com.ecommerce.open.web.service.impl;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.open.api.dto.EasterPayNotifyResponseDTO;
import com.ecommerce.open.api.dto.WXNotifyResponseDTO;
import com.ecommerce.open.api.dto.gnete.GuaranteedPayNotifyDTO;
import com.ecommerce.open.api.service.IAlipayConnectorService;
import com.ecommerce.open.api.service.IEasterPayConnectorService;
import com.ecommerce.open.api.service.IGneteConnectorService;
import com.ecommerce.open.api.service.IPingAnJZConnectorService;
import com.ecommerce.open.api.service.IWeiXinConnectorService;
import com.ecommerce.open.web.enums.WechatNotifyCodeEnum;
import com.ecommerce.open.web.service.IHttpService;
import com.ecommerce.open.web.util.XMLUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

@Service
@Slf4j
public class HttpServiceImpl implements IHttpService {

    public static final String LOG_INFO_EMPTY_REQUEST = "回调参数为空";
    private final IWeiXinConnectorService weiXinConnector;
    private final IAlipayConnectorService alipayConnectorService;
    private final IEasterPayConnectorService easterPayConnectorService;
    private final IPingAnJZConnectorService pingAnJZConnectorService;
    private final IGneteConnectorService gneteConnectorService;

    @Autowired
    public HttpServiceImpl(
            IWeiXinConnectorService weiXinConnector,
            IAlipayConnectorService alipayConnectorService,
            IEasterPayConnectorService easterPayConnectorService,
            IPingAnJZConnectorService pingAnJZConnectorService,
            IGneteConnectorService gneteConnectorService) {
        this.weiXinConnector = weiXinConnector;
        this.alipayConnectorService = alipayConnectorService;
        this.easterPayConnectorService = easterPayConnectorService;
        this.pingAnJZConnectorService = pingAnJZConnectorService;
        this.gneteConnectorService = gneteConnectorService;
    }

    @Override
    public String getWechatHttp(HttpServletRequest request, WechatNotifyCodeEnum wechatNotifyCodeEnum) {
        try (InputStream inputStream = request.getInputStream()) {
            // 获取回调xml内容
            StringBuilder sb = new StringBuilder();
            int len;
            byte[] buffer = new byte[1024];
            while ((len = inputStream.read(buffer)) != -1) {
                sb.append(new String(buffer, 0, len));
            }
            String notifyString = sb.toString();
            log.info("微信异步回调 ：{} ", notifyString);
            if (CsStringUtils.isBlank(notifyString)) {
                return "";
            }
            WXNotifyResponseDTO wxNotifyResponseDTO = null;
            if (wechatNotifyCodeEnum.getCode().equals(WechatNotifyCodeEnum.WEIXIN_PAY.getCode())) {
                wxNotifyResponseDTO = weiXinConnector.getWxPaymentNotify(notifyString).getData();
            } else if (wechatNotifyCodeEnum.getCode().equals(WechatNotifyCodeEnum.WEIXIN_REFUND.getCode())) {
                wxNotifyResponseDTO = weiXinConnector.getWxRefundNotify(notifyString).getData();
            }
            String returnStr = "";
            if (null != wxNotifyResponseDTO) {
                returnStr = XMLUtil.beanToXML(wxNotifyResponseDTO);
            }
            return returnStr;
        } catch (Exception e) {
            WXNotifyResponseDTO wxNotifyResponseDTO = new WXNotifyResponseDTO();
            wxNotifyResponseDTO.setReturn_code("![CDATA[FAIL]]");
            wxNotifyResponseDTO.setReturn_msg("![CDATA[object transform error]]");
            String returnStr = "";
            try {
                returnStr = XMLUtil.beanToXML(wxNotifyResponseDTO);
            } catch (Exception e1) {
                log.error("", e1);
            }
            return returnStr;
        }
    }

    @Override
    public String alipayNotify(HttpServletRequest request) {
        Map<String, String> requestMap = getRequestMap(request);
        if (requestMap.isEmpty()) {
            log.info(LOG_INFO_EMPTY_REQUEST);
            return "";
        }
        ItemResult<String> notify = alipayConnectorService.alipayNotify(requestMap);
        log.info("支付宝异步回调：{}", notify.getData());
        return notify.getData();
    }

    @Override
    public String easterPayNotify(HttpServletRequest request) {
        // 读取请求内容
        String postdata = request.getParameter("postdata");
        if (postdata == null) {
            postdata = this.getInputStreamParam(request);
        }

        log.info("东方付通回调接口收到指令：" + postdata);
        ObjectMapper objectMapper = new ObjectMapper();
        String param = null;
        try {
            param = objectMapper.readValue(postdata, Map.class).toString();
        } catch (IOException e) {
            log.error("东方付通回调接口异常", e);
        }
        ItemResult<EasterPayNotifyResponseDTO> result = easterPayConnectorService.getEasterPayNotify(param);
        log.info("返回东方付通信息：" + result.getData().toString());
        return result.getData().getPayStatus();
    }

    private String getInputStreamParam(HttpServletRequest request) {
        final StringBuilder stringBuffer = new StringBuilder(255);
        try {
            final BufferedReader in = new BufferedReader(new InputStreamReader(request.getInputStream(), StandardCharsets.UTF_8));
            String line;
            while ((line = in.readLine()) != null) {
                stringBuffer.append(line);
            }
        } catch (IOException e) {
            log.error("getInputStreamParam异常", e);
        }
        return stringBuffer.toString();
    }

    @Override
    public String pinganjzRechargeNotify(String orig, String sign) {
        log.info("pinganjzRechargeNotify:{}/{}", orig, sign);
        if (CsStringUtils.isEmpty(orig) && CsStringUtils.isEmpty(sign)) {
            log.info(LOG_INFO_EMPTY_REQUEST);
            return "";
        }
        Map<String, String> map = new HashMap<>();
        map.put("orig", orig);
        map.put("sign", sign);
        ItemResult<String> itemResult = pingAnJZConnectorService.rechargeNotify(map);
        return String.valueOf(itemResult.getData());
    }

    @Override
    public String pinganjzPasswordNotify(HttpServletRequest request) {
        Map<String, String> requestMap = getRequestMap(request);
        if (requestMap.isEmpty()) {
            log.info(LOG_INFO_EMPTY_REQUEST);
            return "";
        }
        ItemResult<String> itemResult = pingAnJZConnectorService.pinganjzPasswordNotify(requestMap);
        log.info("异步回调执行结果：{}", itemResult.getData());
        return String.valueOf(itemResult.getData());
    }

    @Override
    public String pinganjzAggregationNotify(HttpServletRequest request) {
        Map<String, String> requestMap = getRequestMap(request);
        if (requestMap.isEmpty()) {
            log.info(LOG_INFO_EMPTY_REQUEST);
            return "";
        }
        ItemResult<String> itemResult = pingAnJZConnectorService.pinganjzAggregationNotify(requestMap);
        log.info("异步回调执行结果：{}", itemResult.getData());
        return String.valueOf(itemResult.getData());
    }

    @Override
    public String gneteAggregatePayNotify(HttpServletRequest request) {
        Map<String, String> requestMap = getRequestMap(request);
        if (requestMap.isEmpty()) {
            log.info(LOG_INFO_EMPTY_REQUEST);
            return "FAILED";
        }
        return gneteConnectorService.aggregatePayNotify(requestMap);
    }

    @Override
    public String guaranteedPayNotify(GuaranteedPayNotifyDTO dto) {
        return gneteConnectorService.guaranteedPayNotify(dto);
    }

    private Map<String, String> getRequestMap(HttpServletRequest request) {
        Map<String, String[]> parameterMap = request.getParameterMap();
        Map<String, String> model = new HashMap<>();
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, String[]> entry : parameterMap.entrySet()) {
            String name = entry.getKey();
            String[] value = entry.getValue();
            String val = String.join(",", value);
            model.put(name, val);
            sb.append(name).append("=").append(val).append("&");
        }
        if (!sb.isEmpty()) {
            sb.delete(sb.length() - 1, sb.length());
        }
        log.info("异步回调参数列表: {}", sb);
        return model;
    }
}
