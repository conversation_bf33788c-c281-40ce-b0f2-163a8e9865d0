package com.ecommerce.open.web.util;

import java.util.Base64;

/**
 * Base64类
 */
public class Base64Util
{
    private Base64Util()
    {}

    /**
     * Base64 encode
     *
     * @param data
     * @return
     */
    public static String encodeToString(String data)
    {
        return encodeToString(data.getBytes());
    }

    /**
     * Base64 encode
     *
     * @param data
     * @return
     */
    public static String encodeToString(byte[] data)
    {
        return Base64.getEncoder().encodeToString(data);
    }

    /**
     * Base64 encode
     *
     * @param data
     * @return
     */
    public static byte[] encode(String data)
    {
        return encode(data.getBytes());
    }

    /**
     * Base64 encode
     *
     * @param data
     * @return
     */
    public static byte[] encode(byte[] data)
    {
        return Base64.getEncoder().encode(data);
    }

    /**
     * Base64 decode
     *
     * @param data
     * @return
     */
    public static String decodeToString(String data)
    {
        return new String(Base64.getDecoder().decode(data));
    }

    /**
     * Base64 decode
     *
     * @param data
     * @return
     */
    public static String decodeToString(byte[] data)
    {
        return new String(Base64.getDecoder().decode(data));
    }

    /**
     * Base64 decode
     *
     * @param data
     * @return
     */
    public static byte[] decode(String data)
    {
        return Base64.getDecoder().decode(data);
    }

    /**
     * Base64 decode
     *
     * @param data
     * @return
     */
    public static byte[] decode(byte[] data)
    {
        return Base64.getDecoder().decode(data);
    }

    /**
     * 安全的base64 encode
     *
     * @param str 字符串
     * @return
     */
    public static String safeBase64Encode(String str)
    {
        str = str.replace('+', '-').replace('/', '_').replace("=", "");
        return str;
    }

    /**
     * 安全的base64 decode
     *
     * @param str 字符串
     * @return
     */
    public static String safeBase64Decode(String str)
    {
        str = str.replace('-', '+').replace('_', '/');
        int mod4 = str.length() % 4;
        if(mod4 > 0)
        {
            str += "====".substring(0, 4 - mod4);
        }
        return str;
    }
}
