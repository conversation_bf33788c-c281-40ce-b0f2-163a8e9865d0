package com.ecommerce.open.web.util;




import com.ecommerce.common.exception.BizException;import com.ecommerce.common.exception.BasicCode;import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;

import jakarta.xml.bind.JAXBContext;
import jakarta.xml.bind.Marshaller;
import jakarta.xml.bind.Unmarshaller;
import java.io.StringReader;
import java.io.StringWriter;
import java.util.*;

/**
 * <AUTHOR>
 */
public class XMLUtil {

    // 添加私有构造函数，防止类被实例化
    private XMLUtil() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated.");
    }

    /**
     * bean对象转换为xml文档字符串
     *
     * @param beanObject
     * @param <T>
     * @return
     * @throws Exception
     */
    public static <T> String beanToXML(T beanObject) throws Exception {
        StringWriter writer = new StringWriter();
        JAXBContext context = JAXBContext.newInstance(beanObject.getClass());
        Marshaller marshaller = context.createMarshaller();
        marshaller.marshal(beanObject, writer);
        return writer.toString();
    }

    /**
     * xml字符串转换为bean对象
     *
     * @param xmlStr XML 字符串
     * @param tClass 目标类的 Class 对象
     * @param <T>    泛型类型
     * @return 转换后的 bean 对象
     * @throws Exception 如果转换过程中发生错误
     */
    public static <T> T xmlStringToBean(String xmlStr, Class<T> tClass) throws Exception {
        StringReader reader = new StringReader(xmlStr);
        JAXBContext context = JAXBContext.newInstance(tClass);
        Unmarshaller unmarshaller = context.createUnmarshaller();
        Object obj = unmarshaller.unmarshal(reader);
        if (!tClass.isInstance(obj)) {
            throw new ClassCastException("Unmarshalled object is not an instance of " + tClass.getName());
        }
        return tClass.cast(obj);
    }

    @SuppressWarnings("unchecked")
    public static Map<String, Object> dom2Map(String xmlDoc) throws DocumentException {
        Document doc = DocumentHelper.parseText(xmlDoc);
        Map<String, Object> map = new HashMap<>();
        if (doc == null)
            return map;
        Element root = doc.getRootElement();
        for (Iterator<Element> iterator = root.elementIterator(); iterator.hasNext(); ) {
            Element e = iterator.next();
            List<Element>  list = e.elements();
            if (!list.isEmpty()) {
                map.put(e.getName(), dom2Map(e));
            } else {
                map.put(e.getName(), e.getText());
            }
        }
        return map;
    }

    @SuppressWarnings("unchecked")
    public static Map<String, Object> dom2Map(Element e) {
        Map<String, Object>  map = new HashMap<>();
        List<Element>  list = e.elements();
        if (!list.isEmpty()) {
            for (int i = 0;i < list.size(); i++) {
                Element iter = list.get(i);
                List<Object> mapList = new ArrayList<>();
                if (!iter.elements().isEmpty()) {
                    getMapObject(iter, map, mapList);
                } else {
                    getMapObjectDefault(map, iter, mapList);
                }
            }
        } else {
            map.put(e.getName(), e.getText());
        }
        return map;
    }

    private static void getMapObjectDefault(Map<String, Object> map, Element iter, List<Object> mapList) {
        if (map.get(iter.getName()) != null) {
            Object obj = map.get(iter.getName());
            if (obj instanceof ArrayList) {
                mapList = new ArrayList<>();
                mapList.add(obj);
                mapList.add(iter.getText());
            }
            if (obj instanceof ArrayList) {
                mapList = (List) obj;
                mapList.add(iter.getText());
            }
            map.put(iter.getName(), mapList);
        } else {
            map.put(iter.getName(), iter.getText());
        }
    }

    private static void getMapObject(Element iter, Map<String, Object> map, List<Object> mapList) {
        Map<String, Object> objectMap = dom2Map(iter);
        if (map.get(iter.getName()) != null){
            Object obj = map.get(iter.getName());
            if (!(obj instanceof ArrayList)){
                mapList = new ArrayList<>();
                mapList.add(obj);
                mapList.add(objectMap);
            }
            if (obj instanceof ArrayList){
                mapList = (List) obj;
                mapList.add(objectMap);
            }
            map.put(iter.getName(), mapList);
        } else {
            map.put(iter.getName(), objectMap);
        }
    }

}
