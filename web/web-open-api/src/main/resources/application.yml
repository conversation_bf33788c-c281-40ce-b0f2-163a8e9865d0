logging:
  config: classpath:log/logback-${spring.cloud.config.profile}.xml
  level:
    com.netflix.discovery.DiscoveryClient: WARN
vip:
  server:
    ip:
      openapi: ***********
spring:
  application:
    name: web-open-api
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  kafka:
    bootstrap-servers: ${kafka_bootstrap_servers::9092}
  rabbitmq:
    host: ${rabbitmq.host:**************}
    port: ${rabbitmq.port:5672}
    username: ${rabbitmq.username:guest}
    password: ${rabbitmq.password:guest}
  # This property is deprecated and will be removed in future Spring Boot versions
        # This property is deprecated and will be removed in future Spring Boot versions
        # max-idle: 10
        # This property is deprecated and will be removed in future Spring Boot versions
        # This property is deprecated and will be removed in future Spring Boot versions
        # max-active: 100
        # max-wait: -1
        # This property is deprecated and will be removed in future Spring Boot versions
        # max-idle: 10
        # min-idle:  5
  biz-redis:
    database: ${redis.biz.database:6}
    host: ${redis.biz.host:***********}
    port: ${redis.biz.port:6379}
    password: ${redis.biz.password:123456}
    timeout: 3000
  thymeleaf:
    prefix: classpath:/templates/
    encoding: UTF-8
    servlet:
      content-type: text/html
    mode: LEGACYHTML5
  data:
    redis:
      database: 0
      host: ${redis.defult.host:***********}
      password: ${redis.defult.password:123456}
      port: ${redis.defult.port:6379}
      timeout: 3000
  config:
    import: 'optional:configserver:'
  cloud:
    config:
      uri: http://127.0.0.1:8888
      profile: dev
      label: asset-2025
      name: web-open-api,eureka,vip-server,kafka,rabbitmq,redis

server:
  port: ${app.server.port:8087}
  servlet:
    context-path: /openApi

#请求处理的超时时间
ribbon.ReadTimeout: 15000
#请求连接的超时时间
ribbon.ConnectTimeout: 15000

eureka:
  client:
    serviceUrl:
      defaultZone: ${EUREKA_SERVER_ADDRESS:http://dev-euraka.xxx.com/eureka/}
  instance:
    hostname: ${DEV_IP:${spring.cloud.client.ip-address}}
    instance-id: ${DEV_IP:${spring.cloud.client.ip-address}}:${server.port}
    prefer-ip-address: ${PREFER_IP:true}
    status-page-url-path: http://${DEV_IP:${spring.cloud.client.ip-address}}:${server.port}${server.servlet.context-path}/swagger-ui.html

feign:
  hystrix:
    enabled: true
  compression:
    request:
      enabled: true
      mime-types: text/xml,application/xml,application/json
      min-request-size: 2048
    response:
      enabled: true
hystrix:
  command:
    default:
      execution:
        isolation:
          strategy: SEMAPHORE
          thread:
            timeoutInMilliseconds: 15000 #缺省为1000
  threadpool:
    default:
      coreSize: 100 #缺省为10

cros:
  whitelist: ${cros_whitelist:}
