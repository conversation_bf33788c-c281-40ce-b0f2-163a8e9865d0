package com.ecommerce.web.seller.controller;

import com.ecommerce.base.api.dto.ValueSetTreeDTO;
import com.ecommerce.base.api.service.IValueSetService;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.information.api.dto.advertisement.AdverisementSpaceCreateDTO;
import com.ecommerce.information.api.dto.advertisement.AdverisementSpaceDTO;
import com.ecommerce.information.api.dto.advertisement.AdvertisementSpaceQueryDTO;
import com.ecommerce.information.api.service.IAdvertisementSpaceService;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@RestController
@Tag(name = "AdvertisementSpaceController", description = "广告位服务")
@RequestMapping("/advertisementSpace")
@CrossOrigin
public class AdvertisementSpaceController {

    @Autowired
    private IAdvertisementSpaceService iAdvertisementSpaceService;
    @Autowired
    private IValueSetService iValueSetService;

    @Operation(summary = "根据id查找广告位")
    @PostMapping(value = "/findById")
    public ItemResult<AdverisementSpaceCreateDTO> findById(@Parameter(name = "id", description = "广告位id") String id) {
        return new ItemResult<>(iAdvertisementSpaceService.findById(id));
    }

    @Operation(summary = "根据关键字查询")
    @PostMapping(value = "/findByKeyWord")
    public ItemResult<List<AdverisementSpaceDTO>> findByKeyWord(@Parameter(name = "keyWord", description = "关键字") String keyWord) {
        if (CsStringUtils.isBlank(keyWord) || keyWord.length() < 2) {
            return new ItemResult<>(Lists.newArrayList());
        }
        return new ItemResult<>(iAdvertisementSpaceService.findByKeyWord(keyWord));
    }

    @Operation(summary = "根据范围查找所有广告位")
    @PostMapping(value = "/findByAdRange")
    public ItemResult<PageInfo<AdverisementSpaceDTO>> findByAdRange(@Parameter(name = "adRange", description = "广告范围") String adRange) {
        if (adRange == null) {
            return new ItemResult<>(new PageInfo());
        }
        AdvertisementSpaceQueryDTO query = new AdvertisementSpaceQueryDTO();
        query.setAdRange(adRange);
        query.setPageNum(1);
        query.setPageSize(100);
        PageInfo<AdverisementSpaceDTO> page = iAdvertisementSpaceService.findAll(query);
        if (page != null && page.getList() != null && !page.getList().isEmpty()) {
            //AD_SPACE_POSITION	广告位置
            //AD_SPACE_TYPE	广告位类型
            ValueSetTreeDTO vs1 = iValueSetService.getValueSetByReferenceCode("AD_SPACE_POSITION");
            Map<String, String> map1 = vs1 == null || vs1.getTreeList() == null ? Maps.newHashMap() : vs1.getTreeList().stream().collect(Collectors.toMap(ValueSetTreeDTO::getOptionKey, ValueSetTreeDTO::getOptionValue, (key1, key2) -> key2));
            if (!map1.isEmpty()) {
                page.getList().forEach(item -> {
                    item.setTitle(map1.getOrDefault(item.getPosition(), ""));
                });
            }
        }
        return new ItemResult<>(page);
    }

    @Operation(summary = "查找所有广告位")
    @PostMapping(value = "/findAll")
    public ItemResult<PageInfo<AdverisementSpaceDTO>> findAll(@Parameter(name = "query", description = "广告位查询DTO") @RequestBody AdvertisementSpaceQueryDTO query) {
        PageInfo<AdverisementSpaceDTO> page = iAdvertisementSpaceService.findAll(query);
        if (page != null && page.getList() != null && !page.getList().isEmpty()) {
            //AD_SPACE_POSITION	广告位置
            //AD_SPACE_TYPE	广告位类型
            ValueSetTreeDTO vs1 = iValueSetService.getValueSetByReferenceCode("AD_SPACE_POSITION");
            Map<String, String> map1 = vs1 == null || vs1.getTreeList() == null ? Maps.newHashMap() : vs1.getTreeList().stream().collect(Collectors.toMap(ValueSetTreeDTO::getOptionKey, ValueSetTreeDTO::getOptionValue, (key1, key2) -> key2));
            if (!map1.isEmpty()) {
                page.getList().forEach(item -> {
                    item.setTitle(map1.getOrDefault(item.getPosition(), ""));
                });
            }
        }

        return new ItemResult<>(page);
    }
}
