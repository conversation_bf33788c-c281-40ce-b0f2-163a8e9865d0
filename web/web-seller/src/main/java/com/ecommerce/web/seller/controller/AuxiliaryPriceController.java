package com.ecommerce.web.seller.controller;

import com.alibaba.fastjson.JSON;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.shippingroute.AuxiliaryPriceDTO;
import com.ecommerce.logistics.api.dto.shippingroute.AuxiliaryPriceListDTO;
import com.ecommerce.logistics.api.enums.UserRoleEnum;
import com.ecommerce.logistics.api.service.IAuxiliaryPriceService;
import com.ecommerce.member.api.session.LoginInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * 卖家辅助价目表服务
 * Created by hexinhui on 2020/11/17 20:04
 */
@Slf4j
@Tag(name = "AuxiliaryPrice", description = "卖家辅助价目表服务")
@CrossOrigin
@RestController
@RequestMapping(value = "/auxiliaryPrice")
public class AuxiliaryPriceController {


    @Autowired
    private IAuxiliaryPriceService auxiliaryPriceService;

    @Operation(summary = "卖家添加辅助价目表")
    @PostMapping(value="/addAuxiliaryPrice")
    public ItemResult<Void> addAuxiliaryPrice(@Parameter(hidden = true)LoginInfo loginInfo,
                                              @Valid @RequestBody List<AuxiliaryPriceDTO> dtoList) {
        dtoList.forEach(dto ->{
            dto.setBelongerId(loginInfo.getMemberId());
            dto.setUserType(UserRoleEnum.SELLER.getCode());
            dto.setOperatorUserId(loginInfo.getAccountId());
        });
        return auxiliaryPriceService.addAuxiliaryPrice(dtoList);
    }


    @Operation(summary = "卖家删除一个辅助价目表的所有运价差数据")
    @PostMapping(value="/delAuxiliaryPriceByNo")
    public ItemResult<Void> delAuxiliaryPriceByNo(@Parameter(hidden = true)LoginInfo loginInfo,
                                                  @RequestParam String auxiliaryPriceNo) {
        String operatorUserId = loginInfo.getAccountId();
        String belongerId = loginInfo.getMemberId();
        log.info("delAuxiliaryPriceByNo入参：辅助价目表编号：{},操作人ID：{}", auxiliaryPriceNo, operatorUserId);
        return auxiliaryPriceService.delAuxiliaryPriceByNo(belongerId, auxiliaryPriceNo, operatorUserId);
    }


    @Operation(summary = "卖家删除辅助价目表里面的某一个运价差")
    @PostMapping(value="/delAuxiliaryPrice")
    public ItemResult<Void> delAuxiliaryPrice(@Parameter(hidden = true)LoginInfo loginInfo,
                                              @RequestParam String auxiliaryPriceNo,
                                              @RequestParam String auxiliaryPriceId) {
        String operatorUserId = loginInfo.getAccountId();
        String belongerId = loginInfo.getMemberId();
        log.info("delFarePayment入参：辅助价目表NO：{}, 辅助价目表ID：{},操作人ID：{}",auxiliaryPriceNo, auxiliaryPriceId, operatorUserId);
        return auxiliaryPriceService.delAuxiliaryPrice(auxiliaryPriceNo,belongerId, auxiliaryPriceId, operatorUserId);
    }


    @Operation(summary = "卖家编辑辅助价目表")
    @PostMapping(value="/editAuxiliaryPrice")
    public ItemResult<Void> editAuxiliaryPrice(@Parameter(hidden = true)LoginInfo loginInfo,
                                               @Valid @RequestBody List<AuxiliaryPriceDTO> dtoList) {
        dtoList.forEach(dto ->{
            dto.setBelongerId(loginInfo.getMemberId());
            dto.setUserType(UserRoleEnum.SELLER.getCode());
            dto.setOperatorUserId(loginInfo.getAccountId());
        });
        log.info("editAuxiliaryPrice入参：{}", JSON.toJSONString(dtoList));
        return auxiliaryPriceService.editAuxiliaryPrice(dtoList);
    }


    @Operation(summary = "卖家通过辅助价目表编号查看辅助价目表详情")
    @PostMapping(value="/queryAuxiliaryPriceDetails")
    public ItemResult<List<AuxiliaryPriceDTO>> queryAuxiliaryPriceDetails(@Parameter(hidden = true)LoginInfo loginInfo,
                                                                          @RequestParam String auxiliaryPriceNo,
                                                                          @RequestParam(defaultValue = "false") Boolean drySeasonPolicyAffected) {
        log.info("queryAuxiliaryPriceDetails入参：辅助价目表编号：{},drySeasonPolicyAffected: {}", auxiliaryPriceNo,drySeasonPolicyAffected);
        return auxiliaryPriceService.queryAuxiliaryPriceDetails(loginInfo.getMemberId(), auxiliaryPriceNo,drySeasonPolicyAffected);
    }


    @Operation(summary = "卖家分页查询辅助价目列表记录")
    @PostMapping(value="/queryAuxiliaryPriceList")
    public ItemResult<PageData<AuxiliaryPriceListDTO>> queryAuxiliaryPriceList(@Parameter(hidden = true)LoginInfo loginInfo,
                                                                               @RequestBody PageQuery<AuxiliaryPriceDTO> pageQuery) {
        if (pageQuery != null) {
            if (pageQuery.getQueryDTO() == null){
                AuxiliaryPriceDTO auxiliaryPriceDTO = new AuxiliaryPriceDTO();
                auxiliaryPriceDTO.setBelongerId(loginInfo.getMemberId());
                auxiliaryPriceDTO.setUserType(UserRoleEnum.SELLER.getCode());
                pageQuery.setQueryDTO(auxiliaryPriceDTO);
            } else {
                pageQuery.getQueryDTO().setBelongerId(loginInfo.getMemberId());
                pageQuery.getQueryDTO().setUserType(UserRoleEnum.SELLER.getCode());
            }
        }
        return auxiliaryPriceService.queryAuxiliaryPriceList(pageQuery);
    }
}
