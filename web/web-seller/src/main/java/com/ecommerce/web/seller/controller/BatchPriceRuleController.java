package com.ecommerce.web.seller.controller;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.goods.api.dto.BatchPriceShowDTO;
import com.ecommerce.goods.api.dto.BatchPriceTableQueryDTO;
import com.ecommerce.goods.api.service.IBatchPriceRuleService;
import com.ecommerce.member.api.dto.account.AccountDTO;
import com.ecommerce.member.api.session.LoginInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 *
 * <AUTHOR>
 */
@RestController
@Tag(name = "BatchPriceRuleController", description = "批量挂牌规则服务")
@RequestMapping("/batchPriceRule")
public class BatchPriceRuleController {

    @Autowired
    private IBatchPriceRuleService batchPriceRuleService;

    @Operation(summary = "初始化批量挂牌数据")
    @PostMapping(value = "/initBatchPriceForShow")
    public ItemResult<List<BatchPriceShowDTO>> initBatchPriceForShow(@Parameter(hidden = true)LoginInfo loginInfo,
                                                                     @Parameter(name = "goodsId", description = "商品id") @RequestParam String goodsId,
                                                                     @Parameter(name = "ifUp", description = "是否定时上架") @RequestParam(required = false) Boolean ifUp) {
        BatchPriceTableQueryDTO batchPriceTableQueryDTO = new BatchPriceTableQueryDTO();
        batchPriceTableQueryDTO.setSellerId(loginInfo.getMemberId());
        batchPriceTableQueryDTO.setAccountId(loginInfo.getAccountId());
        batchPriceTableQueryDTO.setGoodsId(goodsId);
        batchPriceTableQueryDTO.setIfUp(ifUp != null && ifUp);
        batchPriceTableQueryDTO.setMainAccountFlag(loginInfo.getAccountType() != null && loginInfo.getAccountType() == AccountDTO.ACCOUNT_TYPE_MEMBER_MASTER);
        return new ItemResult<>(batchPriceRuleService.initBatchPriceForShow(batchPriceTableQueryDTO));
    }

    @Operation(summary = "初始化批量挂牌修改数据")
    @PostMapping(value = "/initBatchPriceForUpdate")
    public ItemResult<List<BatchPriceShowDTO>> initBatchPriceForUpdate(@Parameter(hidden = true)LoginInfo loginInfo,
                                                                       @Parameter(name = "goodsId", description = "商品id") @RequestParam String goodsId,
                                                                       @Parameter(name = "ifUp", description = "是否定时上架") @RequestParam(required = false) Boolean ifUp) {
        BatchPriceTableQueryDTO batchPriceTableQueryDTO = new BatchPriceTableQueryDTO();
        batchPriceTableQueryDTO.setSellerId(loginInfo.getMemberId());
        batchPriceTableQueryDTO.setAccountId(loginInfo.getAccountId());
        batchPriceTableQueryDTO.setGoodsId(goodsId);
        batchPriceTableQueryDTO.setEditFlag(1);
        batchPriceTableQueryDTO.setIfUp(ifUp != null && ifUp);
        batchPriceTableQueryDTO.setMainAccountFlag(loginInfo.getAccountType() != null && loginInfo.getAccountType() == AccountDTO.ACCOUNT_TYPE_MEMBER_MASTER);
        return new ItemResult<>(batchPriceRuleService.initBatchPriceForShow(batchPriceTableQueryDTO));
    }
}
