package com.ecommerce.web.seller.controller;


import com.alibaba.fastjson.JSON;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.member.api.dto.base.PageQuery;
import com.ecommerce.member.api.dto.carrier.CarrierBindingConditionQueryDTO;
import com.ecommerce.member.api.dto.carrier.CarrierBindingListDTO;
import com.ecommerce.member.api.dto.carrier.CarrierBindingListQueryDTO;
import com.ecommerce.member.api.dto.carrier.CarrierBindingRemoveDTO;
import com.ecommerce.member.api.dto.carrier.CarrierBindingSaveDTO;
import com.ecommerce.member.api.dto.member.ErpCarrierSyncDTO;
import com.ecommerce.member.api.dto.member.ErpMemberInfoDTO;
import com.ecommerce.member.api.dto.member.MemberSimpleDTO;
import com.ecommerce.member.api.service.ICarrierBindingService;
import com.ecommerce.member.api.service.ICarrierErpInfoService;
import com.ecommerce.member.api.service.IMemberService;
import com.ecommerce.member.api.session.LoginInfo;
import com.ecommerce.web.seller.dto.member.CarrierBindingListExtDTO;
import com.google.common.collect.Lists;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Tag(name = "CarrierBindingController", description = "卖家和承运商关系绑定服务")
@RestController
@RequestMapping("/carrierBinding")
public class CarrierBindingController {

    @Autowired
    private ICarrierBindingService carrierBindingService;
    @Autowired
    private ICarrierErpInfoService iCarrierErpInfoService;
    @Autowired
    private IMemberService memberService;

    @Operation(summary = "新增承运商绑定关系")
    @PostMapping("/saveCarrierBinding")
    public ItemResult<Boolean> saveCarrierBinding(@Parameter(hidden = true)LoginInfo loginInfo,
                                                  @Parameter(name = "carrierBindingSaveDTO", description = "承运商绑定关系保存对象") @RequestBody CarrierBindingSaveDTO carrierBindingSaveDTO) {
        carrierBindingSaveDTO.setBindingMemberId(loginInfo.getMemberId());
        carrierBindingSaveDTO.setBindingMemberName(loginInfo.getMemberName());
        carrierBindingSaveDTO.setOperatorId(loginInfo.getAccountId());
        carrierBindingSaveDTO.setOperatorName(loginInfo.getAccountName());
        return carrierBindingService.saveCarrierBinding(carrierBindingSaveDTO);
    }

    @Operation(summary = "判断是否可以绑定（true表示可以绑定）")
    @PostMapping("/checkRelation")
    public ItemResult<Boolean> checkRelation(@Parameter(hidden = true)LoginInfo loginInfo,
                                             @Parameter(name = "carrierBindingSaveDTO", description = "承运商绑定关系保存对象") @RequestBody CarrierBindingSaveDTO carrierBindingSaveDTO) {
        carrierBindingSaveDTO.setBindingMemberId(loginInfo.getMemberId());
        carrierBindingSaveDTO.setBindingMemberName(loginInfo.getMemberName());
        carrierBindingSaveDTO.setOperatorId(loginInfo.getAccountId());
        carrierBindingSaveDTO.setOperatorName(loginInfo.getAccountName());
        return carrierBindingService.checkRelation(carrierBindingSaveDTO);
    }

    @Operation(summary = "逻辑删除承运商绑定关系")
    @PostMapping("/removeCarrierBinding")
    public ItemResult<Boolean> removeCarrierBinding(@Parameter(hidden = true)LoginInfo loginInfo,
                                                    @Parameter(name = "carrierBindingRemoveDTO", description = "承运商绑定关系删除对象") @RequestBody CarrierBindingRemoveDTO carrierBindingRemoveDTO) {
        carrierBindingRemoveDTO.setOperatorId(loginInfo.getAccountId());
        carrierBindingRemoveDTO.setOperatorName(loginInfo.getAccountName());
        return carrierBindingService.removeCarrierBinding(carrierBindingRemoveDTO);
    }

    @Operation(summary = "修改承运商绑定关系")
    @PostMapping("/modifyCarrierBinding")
    public ItemResult<Boolean> modifyCarrierBinding(@Parameter(hidden = true)LoginInfo loginInfo,
                                                    @Parameter(name = "carrierBindingSaveDTO", description = "承运商绑定关系保存对象") @RequestBody CarrierBindingSaveDTO carrierBindingSaveDTO) {
        carrierBindingSaveDTO.setBindingMemberId(loginInfo.getMemberId());
        carrierBindingSaveDTO.setBindingMemberName(loginInfo.getMemberName());
        carrierBindingSaveDTO.setOperatorId(loginInfo.getAccountId());
        carrierBindingSaveDTO.setOperatorName(loginInfo.getAccountName());
        return carrierBindingService.modifyCarrierBinding(carrierBindingSaveDTO);
    }

    @Operation(summary = "分页查询承运商绑定列表")
    @PostMapping("/pageQueryCarrierBindingList")
    public ItemResult<PageData<CarrierBindingListDTO>> pageQueryCarrierBindingList(@Parameter(hidden = true)LoginInfo loginInfo,
                                                                                   @Parameter(name = "pageQuery", description = "承运商绑定关系列表查询对象分页查询对象") @RequestBody PageQuery<CarrierBindingListQueryDTO> pageQuery) {
        CarrierBindingListQueryDTO carrierBindingListQueryDTO = pageQuery.getQueryDTO();
        carrierBindingListQueryDTO = carrierBindingListQueryDTO == null ? new CarrierBindingListQueryDTO() : carrierBindingListQueryDTO;
        carrierBindingListQueryDTO.setBindingMemberId(loginInfo.getMemberId());
        pageQuery.setQueryDTO(carrierBindingListQueryDTO);
        return carrierBindingService.pageQueryCarrierBindingList(pageQuery);
    }

    @Operation(summary = "条件查询承运商绑定列表")
    @PostMapping("/queryCarrierBindingByCondition")
    public ItemResult<List<CarrierBindingListExtDTO>> queryCarrierBindingByCondition(@Parameter(hidden = true)LoginInfo loginInfo,
                                                                                     @Parameter(name = "carrierBindingConditionQueryDTO", description = "承运商绑定关系条件查询对象") @RequestBody CarrierBindingConditionQueryDTO carrierBindingConditionQueryDTO) {
        log.info("carrierBindingConditionQueryDTO : {}", JSON.toJSONString(carrierBindingConditionQueryDTO));
        carrierBindingConditionQueryDTO.setBindingMemberId(loginInfo.getMemberId());
        List<CarrierBindingListExtDTO> extDTOList = Lists.newArrayList();
        ItemResult<List<CarrierBindingListDTO>> itemResult = carrierBindingService.queryCarrierBindingByCondition(carrierBindingConditionQueryDTO);
        log.info("queryCarrierBindingByCondition itemResult : {}", JSON.toJSONString(itemResult));
        if (itemResult != null && itemResult.isSuccess() && CollectionUtils.isNotEmpty(itemResult.getData())) {
            List<String> ids = itemResult.getData().stream().map(CarrierBindingListDTO::getCarrierId).distinct().toList();
            log.info("ids : {}", JSON.toJSONString(ids));
            List<MemberSimpleDTO> memberSimpleDTOS = memberService.findMemberSimpleByIds(ids);
            if (CollectionUtils.isNotEmpty(memberSimpleDTOS)) {
                // 承运商业务范围
                Map<String, String> id2BusinessScopeMap = memberSimpleDTOS.stream()
                        .filter(Objects::nonNull)
                        .filter(item -> CsStringUtils.isNotBlank(item.getBusinessScope()))
                        .collect(Collectors.toMap(MemberSimpleDTO::getMemberId, MemberSimpleDTO::getBusinessScope));
                log.info("id2BusinessScopeMap : {}", JSON.toJSONString(id2BusinessScopeMap));
                // 会员代码
                Map<String, String> id2BCarrierCodeMap = memberSimpleDTOS.stream()
                        .filter(Objects::nonNull)
                        .collect(Collectors.toMap(MemberSimpleDTO::getMemberId, MemberSimpleDTO::getMemberCode));
                log.info("id2BCarrierCodeMap : {}", JSON.toJSONString(id2BusinessScopeMap));
                itemResult.getData().forEach(dto -> {
                    CarrierBindingListExtDTO extDTO = new CarrierBindingListExtDTO();
                    BeanUtils.copyProperties(dto, extDTO);
                    extDTO.setBusinessScope(id2BusinessScopeMap.get(extDTO.getCarrierId()));
                    extDTO.setCarrierCode(id2BCarrierCodeMap.get(extDTO.getCarrierId()));
                    extDTOList.add(extDTO);
                });
            }
        }
        return new ItemResult<>(extDTOList);
    }

    @Operation(summary = "通过厂商会员ID查询ERP承运商信息")
    @PostMapping(value="/findErpInfo")
    public ItemResult<List<ErpMemberInfoDTO>> findErpInfo(@Parameter(hidden = true)LoginInfo loginInfo){
        return iCarrierErpInfoService.findErpCarrierInfo(loginInfo.getMemberId());
    }


    @Operation(summary = "通过承运商名称同步承运商信息")
    @PostMapping(value="/syncErpCarrierInfo")
    public ItemResult<List<ErpMemberInfoDTO>> syncErpCarrierInfo(@Parameter(hidden = true)LoginInfo loginInfo,
                                                  @RequestBody @Valid ErpCarrierSyncDTO erpCarrierSyncDTO){
        erpCarrierSyncDTO.setMemberId(loginInfo.getMemberId());
        erpCarrierSyncDTO.setOperatorId(loginInfo.getAccountId());
        return iCarrierErpInfoService.syncErpCarrierInfo(erpCarrierSyncDTO);
    }

}
