package com.ecommerce.web.seller.controller;

import com.ecommerce.base.api.enums.UserRoleEnum;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.intelligentDispatch.CarrierParkingLotDTO;
import com.ecommerce.logistics.api.service.ICarrierParkingLotService;
import com.ecommerce.member.api.session.LoginInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@Tag(name = "CarrierParkingLotController", description = "承运商停车点管理")
@RestController
@RequestMapping("/carrierParkingLot")
public class CarrierParkingLotController {

    @Autowired
    private ICarrierParkingLotService carrierParkingLotService;

    @Operation(summary = "新增承运商停车点")
    @PostMapping(value = "/add")
    public ItemResult<Boolean> add(@Parameter(hidden = true)LoginInfo loginInfo,
                                   @RequestBody CarrierParkingLotDTO dto) {
        dto.setConsignorId(loginInfo.getMemberId());
        dto.setConsignorCode(loginInfo.getMemberCode());
        dto.setConsignorName(loginInfo.getMemberName());
        dto.setConsignorUserType(UserRoleEnum.SELLER.getCode());
        // 操作人信息
        dto.setOperatorUserId(loginInfo.getAccountId());
        dto.setOperatorMemberId(loginInfo.getMemberId());
        return carrierParkingLotService.add(dto);
    }

    @Operation(summary = "编辑承运商停车点")
    @PostMapping(value = "/edit")
    public ItemResult<Boolean> edit(@Parameter(hidden = true)LoginInfo loginInfo,
                                    @RequestBody CarrierParkingLotDTO dto) {
        // 操作人信息
        dto.setOperatorUserId(loginInfo.getAccountId());
        dto.setOperatorMemberId(loginInfo.getMemberId());
        return carrierParkingLotService.edit(dto);
    }

    @Operation(summary = "删除承运商停车点")
    @PostMapping(value = "/delete")
    public ItemResult<Boolean> delete(@Parameter(hidden = true)LoginInfo loginInfo,
                                      @RequestBody CarrierParkingLotDTO dto) {
        // 操作人信息
        dto.setOperatorUserId(loginInfo.getAccountId());
        dto.setOperatorMemberId(loginInfo.getMemberId());
        return carrierParkingLotService.delete(dto);
    }

    @Operation(summary = "分页查询承运商停车点")
    @PostMapping(value = "/pageCarrierParkingLot")
    public ItemResult<PageData<CarrierParkingLotDTO>> pageCarrierParkingLot(@Parameter(hidden = true)LoginInfo loginInfo,
                                                                            @RequestBody PageQuery<CarrierParkingLotDTO> pageQuery) {
        CarrierParkingLotDTO queryDTO = pageQuery.getQueryDTO() == null ? new CarrierParkingLotDTO() : pageQuery.getQueryDTO();
        queryDTO.setConsignorId(loginInfo.getMemberId());
        queryDTO.setConsignorUserType(UserRoleEnum.SELLER.getCode());
        // 操作人信息
        queryDTO.setOperatorUserId(loginInfo.getAccountId());
        queryDTO.setOperatorMemberId(loginInfo.getMemberId());
        pageQuery.setQueryDTO(queryDTO);
        return carrierParkingLotService.pageCarrierParkingLot(pageQuery);
    }
}
