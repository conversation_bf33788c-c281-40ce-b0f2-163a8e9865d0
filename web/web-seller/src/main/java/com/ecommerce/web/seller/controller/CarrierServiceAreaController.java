package com.ecommerce.web.seller.controller;

import com.ecommerce.base.api.enums.UserRoleEnum;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.intelligentDispatch.CarrierServiceAreaDTO;
import com.ecommerce.logistics.api.service.ICarrierServiceAreaService;
import com.ecommerce.member.api.session.LoginInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@Tag(name = "CarrierServiceAreaController", description = "承运商服务范围")
@RestController
@RequestMapping("/carrierServiceArea")
public class CarrierServiceAreaController {

    @Autowired
    private ICarrierServiceAreaService carrierServiceAreaService;

    @Operation(summary = "新增承运商服务范围")
    @PostMapping(value = "/add")
    public ItemResult<Boolean> add(@Parameter(hidden = true)LoginInfo loginInfo,
                                   @RequestBody CarrierServiceAreaDTO dto) {
        dto.setConsignorId(loginInfo.getMemberId());
        dto.setConsignorCode(loginInfo.getMemberCode());
        dto.setConsignorName(loginInfo.getMemberName());
        dto.setConsignorUserType(UserRoleEnum.SELLER.getCode());
        // 操作人信息
        dto.setOperatorUserId(loginInfo.getAccountId());
        dto.setOperatorUserType(UserRoleEnum.SELLER.getCode());
        dto.setOperatorMemberId(loginInfo.getMemberId());
        return carrierServiceAreaService.add(dto);
    }

    @Operation(summary = "编辑承运商服务范围")
    @PostMapping(value = "/edit")
    public ItemResult<Boolean> edit(@Parameter(hidden = true)LoginInfo loginInfo,
                                    @RequestBody CarrierServiceAreaDTO dto) {
        // 操作人信息
        dto.setOperatorUserId(loginInfo.getAccountId());
        dto.setOperatorUserType(UserRoleEnum.SELLER.getCode());
        dto.setOperatorMemberId(loginInfo.getMemberId());
        return carrierServiceAreaService.edit(dto);
    }

    @Operation(summary = "删除承运商服务范围")
    @PostMapping(value = "/delete")
    public ItemResult<Boolean> delete(@Parameter(hidden = true)LoginInfo loginInfo,
                                      @RequestBody CarrierServiceAreaDTO dto) {
        // 操作人信息
        dto.setOperatorUserId(loginInfo.getAccountId());
        dto.setOperatorUserType(UserRoleEnum.SELLER.getCode());
        dto.setOperatorMemberId(loginInfo.getMemberId());
        return carrierServiceAreaService.delete(dto);
    }

    @Operation(summary = "分页查询承运商服务范围")
    @PostMapping(value = "/pageCarrierServiceArea")
    public ItemResult<PageData<CarrierServiceAreaDTO>> pageCarrierServiceArea(@Parameter(hidden = true)LoginInfo loginInfo,
                                                                              @RequestBody PageQuery<CarrierServiceAreaDTO> pageQuery) {
        CarrierServiceAreaDTO queryDTO = pageQuery.getQueryDTO() == null ? new CarrierServiceAreaDTO() : pageQuery.getQueryDTO();
        queryDTO.setConsignorId(loginInfo.getMemberId());
        queryDTO.setConsignorUserType(UserRoleEnum.SELLER.getCode());
        // 操作人信息
        queryDTO.setOperatorUserId(loginInfo.getAccountId());
        queryDTO.setOperatorUserType(UserRoleEnum.SELLER.getCode());
        queryDTO.setOperatorMemberId(loginInfo.getMemberId());
        pageQuery.setQueryDTO(queryDTO);
        return carrierServiceAreaService.pageCarrierServiceArea(pageQuery);
    }

    @Operation(summary = "根据承运商服务范围ID查询详情")
    @PostMapping(value = "/queryDetailById")
    public ItemResult<CarrierServiceAreaDTO> queryDetailById(@RequestParam String serviceAreaId) {
        return carrierServiceAreaService.queryDetailById(serviceAreaId);
    }
}
