
package com.ecommerce.web.seller.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.goods.api.dto.GoodsDTO;
import com.ecommerce.goods.api.service.IGoodsService;
import com.ecommerce.member.api.dto.account.AccountDTO;
import com.ecommerce.member.api.dto.member.MemberSuperSimpleDTO;
import com.ecommerce.member.api.dto.referrer.ReferrerInfoDTO;
import com.ecommerce.member.api.dto.referrer.ReferrerInfoQueryDTO;
import com.ecommerce.member.api.service.IBuyerAndReferrerService;
import com.ecommerce.member.api.service.IMemberService;
import com.ecommerce.member.api.session.LoginInfo;
import com.ecommerce.price.api.dto.BuyerPriceSaleItemDTO;
import com.ecommerce.price.api.dto.BuyerPriceSaleRuleCreateDTO;
import com.ecommerce.price.api.dto.BuyerPriceSaleRuleDTO;
import com.ecommerce.price.api.dto.BuyerSectionSaleItemDTO;
import com.ecommerce.price.api.dto.BuyerSectionSaleRuleDTO;
import com.ecommerce.price.api.dto.ReferrerInfoItemDTO;
import com.ecommerce.price.api.dto.ReferrerInfoRuleDTO;
import com.ecommerce.price.api.dto.RefferQueryDTO;
import com.ecommerce.price.api.dto.RuleInfoDTO;
import com.ecommerce.price.api.service.IBuyerPriceSaleService;
import com.ecommerce.price.api.service.IBuyerSectionSaleService;
import com.ecommerce.price.api.service.IPriceSaleService;
import com.ecommerce.price.api.service.IReferrerInfoItemService;
import com.ecommerce.price.api.service.IReferrerInfoRuleService;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.List;


@Slf4j
@Tag(name = "ConcessionCenterController", description = "优惠中心相关功能接口")
@RestController
@RequestMapping("/concessionCenter")
public class ConcessionCenterController {

    @Autowired
    private IBuyerSectionSaleService buyerSectionSaleService;

    @Autowired
    private IBuyerPriceSaleService buyerPriceSaleService;

    @Autowired
    private IMemberService iMemberService;

    @Autowired
    private IGoodsService goodsService;

    @Autowired
    private IReferrerInfoRuleService referrerInfoRuleService;

    @Autowired
    private IReferrerInfoItemService referrerInfoItemService;

    @Autowired
    private IBuyerAndReferrerService buyerAndReferrerService;

    @Autowired
    private IPriceSaleService priceSaleService;

    private static final String PARAM_NULL = "参数为空";
    private static final String SELLER_ID = ",sellerId:";
    private static final String SELLER_ID1 = ";sellerId:";
    private static final String PAGE_SIZE = ";pageSize:";
    private static final String PAGE_NUM = ";pageNum:";
    private static final String OPERATOR_ID = ",operatorId:";
    private static final String PARSE_DATE_ERROR = "转换日期格式错误：";

    @Operation(summary = "分页查询一客一价规则明细")
    @Parameters({
            @Parameter(name = "buyerName", description = "买家名称", required = false),
            @Parameter(name = "goodsName", description = "商品名称", required = false),
            @Parameter(name = "startStatus", description = "规则状态", required = false),
            @Parameter(name = "pageSize", description = "展示条数", required = true),
            @Parameter(name = "pageNum", description = "分页页数", required = true)
    })
    @GetMapping(value = "/findBuyerPriceSaleRule")
    public ItemResult<PageInfo<BuyerPriceSaleRuleDTO>> findBuyerPriceSaleRule(@Parameter(hidden = true)LoginInfo loginInfo,
                                                                              String buyerName,
                                                                              String goodsName,
                                                                              Boolean startStatus,
                                                                              Integer pageSize,
                                                                              Integer pageNum) {
        String sellerId = loginInfo.getMemberId();
        if (CsStringUtils.isBlank(sellerId) || CsStringUtils.isBlank(pageSize.toString()) || CsStringUtils.isBlank(pageNum.toString())) {
            log.error(PARAM_NULL + SELLER_ID + sellerId + PAGE_SIZE + pageSize + PAGE_NUM + pageNum);
            throw new BizException(BasicCode.PARAM_NULL);
        }

        BuyerPriceSaleRuleDTO buyerPriceSaleRuleDTO = new BuyerPriceSaleRuleDTO();
        if (!CsStringUtils.isBlank(buyerName)) {
            buyerPriceSaleRuleDTO.setBuyerName(buyerName);
        }
        if (!CsStringUtils.isBlank(goodsName)) {
            buyerPriceSaleRuleDTO.setGoodsName(goodsName);
        }
        buyerPriceSaleRuleDTO.setPageSize(pageSize);
        buyerPriceSaleRuleDTO.setPageNum(pageNum);
        buyerPriceSaleRuleDTO.setSellerId(sellerId);
        return new ItemResult<PageInfo<BuyerPriceSaleRuleDTO>>(buyerPriceSaleService.findBuyerPriceSaleRule(buyerPriceSaleRuleDTO));
    }

    @Operation(summary = "根据Id更新规则启动状态")
    @PutMapping(value = "/updateBuyerPriceSaleRuleById")
    public ItemResult<Boolean> updateBuyerPriceSaleRuleById(@Parameter(hidden = true)LoginInfo loginInfo,
                                                            @Parameter(name = "list", description = "一客一价促销规则DTO列表") @RequestBody @Valid List<BuyerPriceSaleRuleDTO> list) {
        String operatorId = loginInfo.getAccountId();
        String sellerId = loginInfo.getMemberId();
        if (CsStringUtils.isBlank(operatorId) || CsStringUtils.isBlank(sellerId) || list.size() == 0) {
            log.error(PARAM_NULL + OPERATOR_ID + operatorId + SELLER_ID1 + sellerId + ";list:" + list.toString());
            throw new BizException(BasicCode.PARAM_NULL);
        }
        for (BuyerPriceSaleRuleDTO buyerPriceSaleRuleDTO : list) {
            buyerPriceSaleRuleDTO.setSellerId(sellerId);
        }
        buyerPriceSaleService.updateById(list, operatorId);
        return new ItemResult<Boolean>(true);
    }

    @Operation(summary = "分页查询一客一价使用明细")
    @Parameters({
            @Parameter(name = "buyerPriceSaleRuleId", description = "规则ID", required = false),
            @Parameter(name = "buyerName", description = "买家名称", required = false),
            @Parameter(name = "orderNo", description = "订单号", required = false),
            @Parameter(name = "startTime", description = "开始时间", required = false),
            @Parameter(name = "endTime", description = "结束时间", required = false),
            @Parameter(name = "pageSize", description = "展示条数", required = true),
            @Parameter(name = "pageNum", description = "分页页数", required = true)
    })
    @GetMapping(value = "/findBuyerPriceSaleItem")
    public ItemResult<PageInfo<BuyerPriceSaleItemDTO>> findBuyerPriceSaleItem(@Parameter(hidden = true)LoginInfo loginInfo,
                                                                              String buyerPriceSaleRuleId,
                                                                              String buyerName,
                                                                              String orderNo,
                                                                              String startTime,
                                                                              String endTime,
                                                                              Integer pageSize,
                                                                              Integer pageNum) {

        String sellerId = loginInfo.getMemberId();
        if (CsStringUtils.isBlank(sellerId) || CsStringUtils.isBlank(pageSize.toString()) || CsStringUtils.isBlank(pageNum.toString())) {
            log.error(PARAM_NULL + SELLER_ID + sellerId + PAGE_SIZE + pageSize + PAGE_NUM + pageNum);
            throw new BizException(BasicCode.PARAM_NULL);

        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        BuyerPriceSaleItemDTO buyerPriceSaleItemDTO = new BuyerPriceSaleItemDTO();

        if (!CsStringUtils.isBlank(buyerPriceSaleRuleId)) {
            buyerPriceSaleItemDTO.setBuyerPriceSaleRuleId(buyerPriceSaleRuleId);
        }
        if (!CsStringUtils.isBlank(buyerName)) {
            buyerPriceSaleItemDTO.setBuyerName(buyerName);
        }
        if (!CsStringUtils.isBlank(orderNo)) {
            buyerPriceSaleItemDTO.setOrderNo(orderNo);
        }
        if (!CsStringUtils.isBlank(startTime)) {
            try {
                buyerPriceSaleItemDTO.setStartTime(sdf.parse(startTime));
            } catch (ParseException e) {
                log.error(PARSE_DATE_ERROR + e);
                throw new BizException(BasicCode.INVALID_PARAM);
            }
        }
        if (!CsStringUtils.isBlank(endTime)) {
            try {
                buyerPriceSaleItemDTO.setEndTime(sdf.parse(endTime));
            } catch (ParseException e) {
                log.error(PARSE_DATE_ERROR + e);
                throw new BizException(BasicCode.INVALID_PARAM);
            }
        }
        buyerPriceSaleItemDTO.setPageSize(pageSize);
        buyerPriceSaleItemDTO.setPageNum(pageNum);
        buyerPriceSaleItemDTO.setSellerId(sellerId);
        return new ItemResult<PageInfo<BuyerPriceSaleItemDTO>>(buyerPriceSaleService.findBuyerPriceSaleItem(buyerPriceSaleItemDTO));


    }

    @Operation(summary = "根据Id更新规则启动状态")
    @PutMapping(value = "/updateBuyerSectionSaleRuleById")
    public ItemResult<Boolean> updateById(@Parameter(hidden = true)LoginInfo loginInfo,
                                          @Parameter(name = "list", description = "买家达量(区间)促销规则DTO列表") @RequestBody List<BuyerSectionSaleRuleDTO> list) {
        String operatorId = loginInfo.getAccountId();
        String sellerId = loginInfo.getMemberId();
        if (CsStringUtils.isBlank(operatorId) || CsStringUtils.isBlank(sellerId) || list.size() == 0) {
            log.error(PARAM_NULL + OPERATOR_ID + operatorId + SELLER_ID1 + sellerId + ";list:" + list.toString());
            throw new BizException(BasicCode.PARAM_NULL);
        }
        for (BuyerSectionSaleRuleDTO buyerSectionSaleRuleDTO : list) {
            buyerSectionSaleRuleDTO.setSellerId(sellerId);
        }
        buyerSectionSaleService.updateById(list, operatorId);
        return new ItemResult<Boolean>(true);

    }


    @Operation(summary = "分页查询达量使用明细")
    @Parameters({
            @Parameter(name = "buyerSectionSaleRuleId", description = "规则ID", required = false),
            @Parameter(name = "buyerName", description = "买家名称", required = false),
            @Parameter(name = "orderNo", description = "订单号", required = false),
            @Parameter(name = "startTime", description = "开始时间", required = false),
            @Parameter(name = "endTime", description = "结束时间", required = false),
            @Parameter(name = "pageSize", description = "展示条数", required = true),
            @Parameter(name = "pageNum", description = "分页页数", required = true)
    })
    @GetMapping(value = "/findBuyerSectionSaleItem")
    public ItemResult<PageInfo<BuyerSectionSaleItemDTO>> findBuyerSectionSaleItem(@Parameter(hidden = true)LoginInfo loginInfo,
                                                                                  String buyerSectionSaleRuleId,
                                                                                  String buyerName,
                                                                                  String orderNo,
                                                                                  String startTime,
                                                                                  String endTime,
                                                                                  Integer pageSize,
                                                                                  Integer pageNum) {
        String sellerId = loginInfo.getMemberId();
        if (CsStringUtils.isBlank(sellerId) || CsStringUtils.isBlank(pageSize.toString()) || CsStringUtils.isBlank(pageNum.toString())) {
            log.error(PARAM_NULL + SELLER_ID + sellerId + PAGE_SIZE + pageSize + PAGE_NUM + pageNum);
            throw new BizException(BasicCode.PARAM_NULL);

        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        BuyerSectionSaleItemDTO buyerSectionSaleItemDTO = new BuyerSectionSaleItemDTO();
        if (!CsStringUtils.isBlank(startTime)) {
            try {
                buyerSectionSaleItemDTO.setStartTime(sdf.parse(startTime));
            } catch (ParseException e) {
                log.error(PARSE_DATE_ERROR + e);
                throw new BizException(BasicCode.INVALID_PARAM);
            }
        }
        if (!CsStringUtils.isBlank(endTime)) {
            try {
                buyerSectionSaleItemDTO.setEndTime(sdf.parse(endTime));
            } catch (ParseException e) {
                log.error(PARSE_DATE_ERROR + e);
                throw new BizException(BasicCode.INVALID_PARAM);
            }
        }
        if (!CsStringUtils.isBlank(buyerSectionSaleRuleId)) {
            buyerSectionSaleItemDTO.setBuyerSectionSaleRuleId(buyerSectionSaleRuleId);
        }
        if (!CsStringUtils.isBlank(buyerName)) {
            buyerSectionSaleItemDTO.setBuyerName(buyerName);
        }
        if (!CsStringUtils.isBlank(orderNo)) {
            buyerSectionSaleItemDTO.setOrderNo(orderNo);
        }
        buyerSectionSaleItemDTO.setPageSize(pageSize);
        buyerSectionSaleItemDTO.setPageNum(pageNum);
        buyerSectionSaleItemDTO.setSellerId(sellerId);
        return new ItemResult<PageInfo<BuyerSectionSaleItemDTO>>(buyerSectionSaleService.findBuyerSectionSaleItem(buyerSectionSaleItemDTO));

    }

    @PostMapping(value = "/batchSaveOrUpdateBuyerPriceSaleRule")
    public ItemResult<Object> batchSaveOrUpdateBuyerPriceSaleRule(@Parameter(hidden = true) LoginInfo loginInfo,
                                                          @Parameter(name = "createDTO", description = "一客一价促销规则创建DTO") @RequestBody BuyerPriceSaleRuleCreateDTO createDTO) {

        log.info("batchSaveOrUpdateBuyerPriceSaleRule === param: {}", createDTO);
        String operatorId = loginInfo.getAccountId();
        String sellerId = loginInfo.getMemberId();
        if (createDTO == null || CollectionUtils.isEmpty(createDTO.getBuyerPriceSaleRuleDTOList())) {
            throw new BizException(BasicCode.PARAM_NULL, "优惠列表");
        }
        if (CsStringUtils.isEmpty(createDTO.getBuyerId()) || CsStringUtils.isEmpty(createDTO.getGoodsId()) ||
                CsStringUtils.isEmpty(createDTO.getUnit())) {
            throw new BizException(BasicCode.PARAM_NULL,"用户名称或商品名称");
        }
        createDTO.setSellerId(sellerId);
        createDTO.setSellerName(loginInfo.getMemberName());
        buyerPriceSaleService.batchSaveOrUpdate(createDTO, operatorId);
        return new ItemResult<>(true);
    }

    @PostMapping(value = "/initBatchBuyerPriceSaleRuleForShow")
    public ItemResult<Object> initBatchBuyerPriceSaleRuleForShow(@Parameter(hidden = true) LoginInfo loginInfo,
                                                         @Parameter(name = "buyerId", description = "买家id") @RequestParam String buyerId,
                                                         @Parameter(name = "goodsId", description = "商品id") @RequestParam String goodsId) {

        log.info("initBatchBuyerPriceSaleRuleForShow === param: {}", goodsId);
        String sellerId = loginInfo.getMemberId();
        if (CsStringUtils.isEmpty(goodsId)) {
            return new ItemResult<>("参数不可为空");
        }
        boolean isMaster = loginInfo.getAccountType() != null && loginInfo.getAccountType() == AccountDTO.ACCOUNT_TYPE_MEMBER_MASTER;
        return new ItemResult<>(buyerPriceSaleService.initBatchBuyerPriceSaleRuleForShow(buyerId, goodsId, sellerId, loginInfo.getAccountId(),isMaster));
    }

    @Operation(summary = "创建达量规则")
    @Parameters({
            @Parameter(name = "goodsId", description = "商品ID", required = true),
            @Parameter(name = "goodsName", description = "商品名称", required = true),
            @Parameter(name = "unit", description = "单位", required = true),
            @Parameter(name = "preferentialIntervalJson", description = "优惠区间json", required = true),
            @Parameter(name = "commitType", description = "提交类型,保存草稿请传‘1’,提交‘3’", required = true)
    })
    @PostMapping(value = "/createBuyerSectionSaleRule")
    public ItemResult<Boolean> createBuyerSectionSaleRule(@Parameter(hidden = true)LoginInfo loginInfo,
                                                          String goodsId,
                                                          String goodsName,
                                                          String unit,
                                                          String preferentialIntervalJson,
                                                          Integer commitType) {
        String operatorId = loginInfo.getAccountId();
        String sellerId = loginInfo.getMemberId();
        if (CsStringUtils.isBlank(operatorId) || CsStringUtils.isBlank(sellerId)
                || CsStringUtils.isBlank(goodsName) || CsStringUtils.isBlank(goodsId) || CsStringUtils.isBlank(unit)
                || CsStringUtils.isBlank(preferentialIntervalJson) || CsStringUtils.isBlank(commitType.toString())) {
            log.error(PARAM_NULL + OPERATOR_ID + operatorId + ";goodsId:" + goodsId + ";unit:" + unit
                    + ";goodsName:" + goodsName + SELLER_ID1 + sellerId
                    + ";preferentialIntervalJson:" + preferentialIntervalJson + ";commitType:" + commitType);
            throw new BizException(BasicCode.PARAM_NULL);
        }
        JSONArray jso = JSON.parseArray(preferentialIntervalJson);
        buyerSectionSaleService.create(jso, goodsId, goodsName, unit, commitType, sellerId, operatorId);
        return new ItemResult<Boolean>(true);
    }

    @Operation(summary = "分页查询达量规则明细")
    @Parameters({
            @Parameter(name = "goodsName", description = "商品名称", required = false),
            @Parameter(name = "startStatus", description = "规则状态", required = false),
            @Parameter(name = "pageSize", description = "展示条数", required = true),
            @Parameter(name = "pageNum", description = "分页页数", required = true)
    })
    @GetMapping(value = "/findBuyerSectionSaleRule")
    public ItemResult<PageInfo<BuyerSectionSaleRuleDTO>> findBuyerSectionSaleRule(@Parameter(hidden = true)LoginInfo loginInfo,
                                                                                  String goodsName,
                                                                                  Boolean startStatus,
                                                                                  Integer pageSize,
                                                                                  Integer pageNum) {
        log.info(JSON.toJSONString(loginInfo));
        String sellerId = loginInfo.getMemberId();
        if (CsStringUtils.isBlank(sellerId) || CsStringUtils.isBlank(pageSize.toString()) || CsStringUtils.isBlank(pageNum.toString())) {
            log.error(PARAM_NULL + SELLER_ID + sellerId + PAGE_SIZE + pageSize + PAGE_NUM + pageNum);
            throw new BizException(BasicCode.PARAM_NULL);

        }
        BuyerSectionSaleRuleDTO buyerSectionSaleRuleDTO = new BuyerSectionSaleRuleDTO();
        if (!CsStringUtils.isBlank(goodsName)) {
            buyerSectionSaleRuleDTO.setGoodsName(goodsName);
        }
        if (startStatus != null) {
            buyerSectionSaleRuleDTO.setStartStatus(startStatus);
        }
        buyerSectionSaleRuleDTO.setSellerId(sellerId);
        buyerSectionSaleRuleDTO.setPageNum(pageNum);
        buyerSectionSaleRuleDTO.setPageSize(pageSize);
        return new ItemResult<PageInfo<BuyerSectionSaleRuleDTO>>(buyerSectionSaleService.findBuyerSectionSaleRule(buyerSectionSaleRuleDTO));

    }


    @Operation(summary = "查询买家名称")
    @Parameters({
            @Parameter(name = "memberName", description = "买家名称", required = true)
    })
    @PostMapping(value = "/findBuyerName")
    public ItemResult<List<MemberSuperSimpleDTO>> findByBuyerNameLike(String memberName) {
        if (CsStringUtils.isBlank(memberName)) {
            log.error("参数为空,memberName:" + memberName);
            throw new BizException(BasicCode.PARAM_NULL);

        }
        return new ItemResult<List<MemberSuperSimpleDTO>>(iMemberService.findByBuyerNameLike(memberName));
    }

    @Operation(summary = "查询商品名称")
    @Parameters({
            @Parameter(name = "goodsName", description = "商品名称", required = true)
    })
    @PostMapping(value = "/findGoodsName")
    public ItemResult<List<GoodsDTO>> findUserGoodsLikeName(@Parameter(hidden = true)LoginInfo loginInfo,
                                                            String goodsName) {
        String memberId = loginInfo.getMemberId();
        if (CsStringUtils.isEmpty(goodsName) || CsStringUtils.isBlank(memberId)) {
            log.error("参数为空,goodsName:" + goodsName + ",memberId:" + memberId);
            throw new BizException(BasicCode.PARAM_NULL);
        }
        return goodsService.findUserGoodsLikeName(goodsName, memberId, null);
    }

    //--------------------------------以下推荐人优惠方式代码------------------------------
    @Operation(summary = "分页查询推荐人优惠规则")
    @PostMapping(value = "/findReferrerInfoRule")
    public ItemResult<PageInfo<ReferrerInfoRuleDTO>> findReferrerInfoRule(@Parameter(hidden = true) LoginInfo loginInfo,
                                                                          @Parameter(name = "referrerInfoRuleDTO", description = "推荐人优惠规则DTO") @RequestBody ReferrerInfoRuleDTO referrerInfoRuleDTO) {
        String sellerId = loginInfo.getMemberId();
        if (CsStringUtils.isBlank(sellerId)) {
            log.error(PARAM_NULL + SELLER_ID + sellerId);
            throw new BizException(BasicCode.PARAM_NULL);
        }
        referrerInfoRuleDTO.setSellerId(sellerId);
        return new ItemResult<PageInfo<ReferrerInfoRuleDTO>>(referrerInfoRuleService.findReferrerInfoRuleList(referrerInfoRuleDTO));
    }

    @Operation(summary = "创建推荐人优惠规则")
    @PostMapping(value = "/createReferrerInfoRule")
    public ItemResult<Boolean> createReferrerInfoRule(@Parameter(hidden = true) LoginInfo loginInfo,
                                                      @Parameter(name = "referrerInfoRuleDTO", description = "推荐人优惠规则DTO") @RequestBody ReferrerInfoRuleDTO referrerInfoRuleDTO) {
        String operatorId = loginInfo.getAccountId();
        String sellerId = loginInfo.getMemberId();
        if (CsStringUtils.isBlank(sellerId) || CsStringUtils.isBlank(operatorId)) {
            log.error(PARAM_NULL + SELLER_ID + sellerId + OPERATOR_ID + operatorId);
            throw new BizException(BasicCode.PARAM_NULL);
        }
        referrerInfoRuleDTO.setSellerId(sellerId);
        referrerInfoRuleDTO.setOperatorId(operatorId);
        log.info("===createReferrerInfoRule,req===={}", JSON.toJSONString(referrerInfoRuleDTO));
        return new ItemResult<Boolean>(referrerInfoRuleService.createReferrerInfoRule(referrerInfoRuleDTO));
    }

    @Operation(summary = "查询会员")
    @Parameters({
            @Parameter(name = "name", description = "会员名称", required = true)
    })
    @GetMapping(value = "/findMemberName")
    public ItemResult<List<MemberSuperSimpleDTO>> findMemberName(String name) {
        if (CsStringUtils.isBlank(name)) {
            log.error("参数为空,name:" + name);
            throw new BizException(BasicCode.PARAM_NULL);

        }
        return new ItemResult<List<MemberSuperSimpleDTO>>(iMemberService.findByBuyerNameLike(name));
    }

    @Operation(summary = "查询优惠code是否可用")
    @Parameters({
            @Parameter(name = "referrerCode", description = "优惠编码", required = true)
    })
    @GetMapping(value = "/findReferrerCodeCanUse")
    public ItemResult<Boolean> findReferrerCodeCanUse(@Parameter(hidden = true) LoginInfo loginInfo,
                                                      String referrerCode) {
        String sellerId = loginInfo.getMemberId();
        if (CsStringUtils.isBlank(sellerId) || CsStringUtils.isBlank(referrerCode)) {
            log.error(PARAM_NULL + SELLER_ID + sellerId + ",referrerCode" + referrerCode);
            throw new BizException(BasicCode.PARAM_NULL);
        }
        return new ItemResult<Boolean>(referrerInfoRuleService.findReferrerCodeCanUse(referrerCode, sellerId));
    }

    @Operation(summary = "根据id查询推荐人优惠详情")
    @Parameters({
            @Parameter(name = "referrerInfoRuleId", description = "推荐人优惠id", required = true)
    })
    @GetMapping(value = "/findReferrerInfoRuleById")
    public ItemResult<ReferrerInfoRuleDTO> findReferrerInfoRuleById(String referrerInfoRuleId) {
        if (CsStringUtils.isBlank(referrerInfoRuleId)) {
            log.error("参数为空,referrerInfoRuleId:" + referrerInfoRuleId);
            throw new BizException(BasicCode.PARAM_NULL);
        }
        return new ItemResult<ReferrerInfoRuleDTO>(referrerInfoRuleService.findReferrerInfoRule(referrerInfoRuleId));
    }

    @Operation(summary = "分页查询推荐人优惠规则使用明细")
    @PostMapping(value = "/findReferrerInfoItem")
    public ItemResult<PageInfo<ReferrerInfoItemDTO>> findReferrerInfoItem(@Parameter(hidden = true) LoginInfo loginInfo,
                                                                          @Parameter(name = "referrerInfoItemDTO", description = "推荐人编码优惠使用明细DTO") @RequestBody ReferrerInfoItemDTO referrerInfoItemDTO) {
        String sellerId = loginInfo.getMemberId();
        if (CsStringUtils.isBlank(sellerId)) {
            log.error(PARAM_NULL + SELLER_ID + sellerId);
            throw new BizException(BasicCode.PARAM_NULL);
        }
        referrerInfoItemDTO.setSellerId(sellerId);
        return new ItemResult<PageInfo<ReferrerInfoItemDTO>>(referrerInfoItemService.findReferrerInfoItem(referrerInfoItemDTO));
    }

    @Operation(summary = "根据卖家ID查询门店信息")
    @GetMapping(value = "/findStoreInfo")
    @Parameters({
            @Parameter(name = "name", description = "推荐人名称", required = false)
    })
    public ItemResult<PageInfo<ReferrerInfoDTO>> findStoreInfo(@Parameter(hidden = true) LoginInfo loginInfo,
                                                               String name) {
        String sellerId = loginInfo.getMemberId();
        if (CsStringUtils.isBlank(sellerId)) {
            log.error(PARAM_NULL + SELLER_ID + sellerId);
            throw new BizException(BasicCode.PARAM_NULL);
        }
        ReferrerInfoQueryDTO referrerInfoQueryDTO = new ReferrerInfoQueryDTO();
        referrerInfoQueryDTO.setReferrerAccountId(sellerId);
        if (!CsStringUtils.isBlank(name)) {
            referrerInfoQueryDTO.setReferrerName(name);
        }
        referrerInfoQueryDTO.setPageNum(1);
        referrerInfoQueryDTO.setPageSize(20);
        return new ItemResult<PageInfo<ReferrerInfoDTO>>(buyerAndReferrerService.findAllReferrerInfoByCondition(referrerInfoQueryDTO));
    }

    //TODO
    @Operation(summary = "根据id修改优惠信息")
    @PostMapping(value = "/updateRuleInfo")
    public ItemResult<Boolean> updateRuleInfo(@Parameter(hidden = true) LoginInfo loginInfo,
                                              @Parameter(name = "ruleInfoDTO", description = "优惠信息DTO") @RequestBody RuleInfoDTO ruleInfoDTO) {
        String operatorId = loginInfo.getAccountId();
        String sellerId = loginInfo.getMemberId();
        if (CsStringUtils.isBlank(sellerId) || CsStringUtils.isBlank(operatorId)) {
            log.error(PARAM_NULL + SELLER_ID + sellerId + OPERATOR_ID + operatorId);
            throw new BizException(BasicCode.PARAM_NULL);
        }
        if (CsStringUtils.isNotBlank(sellerId)) {
            ruleInfoDTO.setSellerId(sellerId);
        }
        if (CsStringUtils.isNotBlank(operatorId)) {
            ruleInfoDTO.setOperatorId(operatorId);
        }
        priceSaleService.updateDTO(ruleInfoDTO, operatorId);
        return new ItemResult<Boolean>(true);
    }

    //TODO
    @Operation(summary = "根据id删除优惠信息")
    @PostMapping(value = "/deleteRuleInfo")
    public ItemResult<Boolean> deleteRuleInfo(@Parameter(hidden = true) LoginInfo loginInfo,
                                              @Parameter(name = "ruleInfoDTOList", description = "优惠信息DTO列表") @RequestBody List<RuleInfoDTO> ruleInfoDTOList) {
        String operatorId = loginInfo.getAccountId();
        String sellerId = loginInfo.getMemberId();
        if (CsStringUtils.isBlank(sellerId) || CsStringUtils.isBlank(operatorId)) {
            log.error(PARAM_NULL + SELLER_ID + sellerId + OPERATOR_ID + operatorId);
            throw new BizException(BasicCode.PARAM_NULL);
        }
        for (RuleInfoDTO ruleInfoDTO : ruleInfoDTOList) {
            if (CsStringUtils.isNotBlank(sellerId)) {
                ruleInfoDTO.setSellerId(sellerId);
            }
            if (CsStringUtils.isNotBlank(operatorId)) {
                ruleInfoDTO.setOperatorId(operatorId);
            }
        }
        priceSaleService.deleteDTO(ruleInfoDTOList);
        return new ItemResult<Boolean>(true);
    }

    @Operation(summary = "根据id禁用优惠信息")
    @PostMapping(value = "/disableRuleInfo")
    public ItemResult<Boolean> disableRuleInfo(@Parameter(hidden = true) LoginInfo loginInfo,
                                               @Parameter(name = "ruleInfoDTOList", description = "优惠信息DTO列表") @RequestBody List<RuleInfoDTO> ruleInfoDTOList) {
        String operatorId = loginInfo.getAccountId();
        String sellerId = loginInfo.getMemberId();
        if (CsStringUtils.isBlank(sellerId) || CsStringUtils.isBlank(operatorId)) {
            log.error(PARAM_NULL + SELLER_ID + sellerId + OPERATOR_ID + operatorId);
            throw new BizException(BasicCode.PARAM_NULL);
        }
        for (RuleInfoDTO ruleInfoDTO : ruleInfoDTOList) {
            if (CsStringUtils.isNotBlank(sellerId)) {
                ruleInfoDTO.setSellerId(sellerId);
            }
            if (CsStringUtils.isNotBlank(operatorId)) {
                ruleInfoDTO.setOperatorId(operatorId);
            }
        }
        priceSaleService.disableRuleInfo(ruleInfoDTOList);
        return new ItemResult<Boolean>(true);
    }

    @Operation(summary = "根据id启用优惠信息")
    @PostMapping(value = "/enableRuleInfo")
    public ItemResult<Boolean> enableRuleInfo(@Parameter(hidden = true) LoginInfo loginInfo,
                                              @Parameter(name = "ruleInfoDTOList", description = "优惠信息DTO列表") @RequestBody List<RuleInfoDTO> ruleInfoDTOList) {
        String operatorId = loginInfo.getAccountId();
        String sellerId = loginInfo.getMemberId();
        if (CsStringUtils.isBlank(sellerId) || CsStringUtils.isBlank(operatorId)) {
            log.error(PARAM_NULL + SELLER_ID + sellerId + OPERATOR_ID + operatorId);
            throw new BizException(BasicCode.PARAM_NULL);
        }
        for (RuleInfoDTO ruleInfoDTO : ruleInfoDTOList) {
            if (CsStringUtils.isNotBlank(sellerId)) {
                ruleInfoDTO.setSellerId(sellerId);
            }
            if (CsStringUtils.isNotBlank(operatorId)) {
                ruleInfoDTO.setOperatorId(operatorId);
            }
        }
        priceSaleService.enableRuleInfo(ruleInfoDTOList);
        return new ItemResult<Boolean>(true);
    }

    @Operation(summary = "查询达量优惠规则")
    @Parameters({
            @Parameter(name = "buyerSectionSaleRuleId", description = "达量规则id", required = true)
    })
    @GetMapping(value = "/findBuyerSectionSaleRuleById")
    public ItemResult<BuyerSectionSaleRuleDTO> findBuyerSectionSaleRuleById(String buyerSectionSaleRuleId) {
        return new ItemResult<BuyerSectionSaleRuleDTO>(buyerSectionSaleService.findById(buyerSectionSaleRuleId));
    }

    @Operation(summary = "根据优惠码查询推荐人优惠信息")
    @GetMapping(value = "/findReferrerInfoRuleByCode")
    public ItemResult<ReferrerInfoRuleDTO> findReferrerInfoRuleByCode(@Parameter(hidden = true) LoginInfo loginInfo,
                                                                      @Parameter(name = "code", description = "优惠码") String code) {
        if (CsStringUtils.isBlank(code)) {
            throw new BizException(BasicCode.PARAM_NULL);
        }
        return new ItemResult<ReferrerInfoRuleDTO>(referrerInfoRuleService.findReferrerCodeByCode(code, loginInfo.getMemberId()));
    }

    @Operation(summary = "查询是否有门店优惠")
    @GetMapping(value = "/findIsExitReferrer")
    public ItemResult<Boolean> findIsExitReferrer(@Parameter(name = "refferQueryDTOS", description = "推荐人优惠查询DTO列表") @RequestBody List<RefferQueryDTO> refferQueryDTOS) {
        return new ItemResult<Boolean>(referrerInfoRuleService.findRefferByGoodsId(refferQueryDTOS));
    }

}
