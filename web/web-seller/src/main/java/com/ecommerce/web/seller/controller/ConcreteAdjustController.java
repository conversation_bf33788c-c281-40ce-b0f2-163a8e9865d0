package com.ecommerce.web.seller.controller;

import com.alibaba.fastjson.JSON;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.exception.BusinessOperationException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.common.utils.ArithUtils;
import com.ecommerce.common.utils.DateUtil;
import com.ecommerce.logistics.api.enums.PickingBillTypeEnum;
import com.ecommerce.member.api.session.LoginInfo;
import com.ecommerce.order.api.dto.base.PageQuery;
import com.ecommerce.order.api.dto.concrete.ConcreteAdjustContractQueryDTO;
import com.ecommerce.order.api.dto.concrete.ConcreteAdjustGoodsDTO;
import com.ecommerce.order.api.dto.concrete.ConcreteAdjustGoodsQueryDTO;
import com.ecommerce.order.api.dto.concrete.ConcreteAdjustOrderDTO;
import com.ecommerce.order.api.dto.concrete.ConcreteAdjustOrderDetailDTO;
import com.ecommerce.order.api.dto.concrete.ConcreteAdjustOrderQueryDTO;
import com.ecommerce.order.api.dto.concrete.ConcreteAdjustPlanDTO;
import com.ecommerce.order.api.dto.concrete.ConcreteAdjustPlanListDTO;
import com.ecommerce.order.api.dto.concrete.ConcreteAdjustPlanQueryDTO;
import com.ecommerce.order.api.dto.concrete.ConcreteAdjustPlanSaveDTO;
import com.ecommerce.order.api.dto.concrete.ContractSelectorDTO;
import com.ecommerce.order.api.dto.proxy.GoodsSelectorDTO;
import com.ecommerce.order.api.enums.ConcreteAdjustStatusEnum;
import com.ecommerce.order.api.service.IConcreteAdjustService;
import com.ecommerce.web.seller.vo.order.ConcreteAdjustOrderExportDTO;
import com.google.common.collect.Lists;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
@Slf4j
@Tag(name = "ConcreteAdjustController", description = "调价服务")
@RestController
@RequestMapping("/concreteAdjust")
public class ConcreteAdjustController {

    @Autowired
    private IConcreteAdjustService concreteAdjustService;

    @Operation(summary = "创建调价计划")
    @PostMapping(value = "/creteConcreteAdjustPlan")
    public ItemResult<String> creteConcreteAdjustPlan(
            @Parameter(hidden = true)LoginInfo loginInfo,
            @RequestBody ConcreteAdjustPlanSaveDTO concreteAdjustPlanSaveDTO) {
        concreteAdjustPlanSaveDTO.setSellerId(loginInfo.getMemberId());
        concreteAdjustPlanSaveDTO.setOperatorUserId(loginInfo.getAccountId());
        concreteAdjustPlanSaveDTO.setOperatorUserName(loginInfo.getAccountName());
        return concreteAdjustService.creteConcreteAdjustPlan(concreteAdjustPlanSaveDTO);
    }

    @Operation(summary = "查询调价计划详情")
    @PostMapping(value = "/queryConcreteAdjustPlanDetail")
    public ItemResult<ConcreteAdjustPlanDTO> queryConcreteAdjustPlanDetail(
            @RequestParam String adjustPlanId) {
        return concreteAdjustService.queryConcreteAdjustPlanDetail(adjustPlanId);
    }

    @Operation(summary = "查询合同商品列表")
    @PostMapping(value = "/queryConcreteAdjustContractGoodsList")
    public ItemResult<List<GoodsSelectorDTO>> queryConcreteAdjustContractGoodsList(
            @RequestParam String contractId) {
        return concreteAdjustService.queryConcreteAdjustContractGoodsList(contractId);
    }

    @Operation(summary = "查询调价合同列表")
    @PostMapping(value = "/queryConcreteAdjustContractList")
    public ItemResult<List<ContractSelectorDTO>> queryConcreteAdjustContractList(
            @Parameter(hidden = true)LoginInfo loginInfo,
            @RequestBody ConcreteAdjustContractQueryDTO concreteAdjustContractQueryDTO) {
        concreteAdjustContractQueryDTO.setSellerId(loginInfo.getMemberId());
        return concreteAdjustService.queryConcreteAdjustContractList(concreteAdjustContractQueryDTO);
    }

    @Operation(summary = "查询调价计划订单详情")
    @PostMapping(value = "/queryConcreteAdjustOrderDetail")
    public ItemResult<ConcreteAdjustOrderDetailDTO> queryConcreteAdjustOrderDetail(
            @RequestBody ConcreteAdjustOrderQueryDTO concreteAdjustOrderQueryDTO) {
        return concreteAdjustService.queryConcreteAdjustOrderDetail(concreteAdjustOrderQueryDTO);
    }

    @Operation(summary = "预览订单调价列表")
    @PostMapping(value = "/previewAdjustOrderList")
    public ItemResult<PageData<ConcreteAdjustOrderDTO>> previewAdjustOrderList(
            @RequestBody PageQuery<ConcreteAdjustGoodsQueryDTO> pageQuery) {
        return concreteAdjustService.previewAdjustOrderList(pageQuery);
    }

    @Operation(summary = "查询调价订单列表")
    @PostMapping(value = "/queryConcreteAdjustOrderList")
    public ItemResult<PageData<ConcreteAdjustOrderDTO>> queryConcreteAdjustOrderList(
            @RequestBody PageQuery<ConcreteAdjustOrderQueryDTO> pageQuery) {
        return concreteAdjustService.queryConcreteAdjustOrderList(pageQuery);
    }

    @Operation(summary = "分页查询调价计划列表")
    @PostMapping(value = "/queryConcreteAdjustPlanList")
    public ItemResult<PageData<ConcreteAdjustPlanListDTO>> queryConcreteAdjustPlanList(
            @Parameter(hidden = true)LoginInfo loginInfo,
            @RequestBody PageQuery<ConcreteAdjustPlanQueryDTO> pageQuery) {
        ConcreteAdjustPlanQueryDTO concreteAdjustPlanQueryDTO =
                pageQuery.getQueryDTO() == null ? new ConcreteAdjustPlanQueryDTO() : pageQuery.getQueryDTO();
        concreteAdjustPlanQueryDTO.setSellerId(loginInfo.getMemberId());
        pageQuery.setQueryDTO(concreteAdjustPlanQueryDTO);
        return concreteAdjustService.queryConcreteAdjustPlanList(pageQuery);
    }

    @Operation(summary = "查询调价商品列表")
    @PostMapping(value = "/queryConcreteAdjustGoodsList")
    public ItemResult<List<ConcreteAdjustGoodsDTO>> queryConcreteAdjustGoodsList(
            @RequestBody ConcreteAdjustGoodsQueryDTO concreteAdjustGoodsQueryDTO) {
        return concreteAdjustService.queryConcreteAdjustGoodsList(concreteAdjustGoodsQueryDTO);
    }

    @Operation(summary = "重试通知ERP")
    @PostMapping(value = "/retryConcreteAdjustPriceNotify")
    public ItemResult<Void> retryConcreteAdjustPriceNotify(
            @RequestBody ConcreteAdjustGoodsDTO concreteAdjustGoodsDTO) {
        return concreteAdjustService.retryConcreteAdjustPriceNotify(concreteAdjustGoodsDTO);
    }

    @Operation(summary = "导出订单明细列表")
    @PostMapping(value = "/exportAdjustOrderItemList")
    public String exportAdjustOrderItemList(
            @RequestBody ConcreteAdjustOrderExportDTO concreteAdjustOrderExportDTO) {
        log.info("exportAdjustOrderItemList_concreteAdjustOrderExportDTO:" + JSON.toJSONString(concreteAdjustOrderExportDTO));
        StringBuilder htmlBuilder = new StringBuilder("<table width='100%' border='1' style='font-size:12pt'>");
        //追加表头
        htmlBuilder.append(
        "<tr>" +
            "<td bgcolor='#00a3e0'>订单号</td>" +
            "<td bgcolor='#00a3e0'>配送方式</td>" +
            "<td bgcolor='#00a3e0'>成交量（立方）</td>" +
            "<td bgcolor='#00a3e0'>原始成交单价（元/立方</td>" +
            "<td bgcolor='#00a3e0'>原始成交金额（元）</td>" +
            "<td bgcolor='#00a3e0'>最新成交单价（元/立方）</td>" +
            "<td bgcolor='#00a3e0'>最新成交金额（元）</td>" +
            "<td bgcolor='#00a3e0'>回调后成交单价（元/立方）</td>" +
            "<td bgcolor='#00a3e0'>回调后成交金额（元）</td>" +
            "<td bgcolor='#00a3e0'>订单创建时间</td>" +
            "<td bgcolor='#00a3e0'>订单完成时间</td>" +
            "<td bgcolor='#00a3e0'>回调次数</td>" +
            "<td bgcolor='#00a3e0'>回调状态</td>" +
        "</tr>");
        if (CollectionUtils.isEmpty(concreteAdjustOrderExportDTO.getAdjustGoodsList())) {
            htmlBuilder.append("</table>");
            return htmlBuilder.toString();
        }
        concreteAdjustOrderExportDTO.getAdjustGoodsList().stream().forEach(concreteAdjustGoodsDTO -> {
            //查询关联订单(分页查询防止数据量过大导致查询缓慢的问题)
            Integer pageNum = 1;
            Integer pageSize = 200;
            PageQuery<ConcreteAdjustGoodsQueryDTO> pageQuery = new PageQuery<>();
            ConcreteAdjustGoodsQueryDTO concreteAdjustGoodsQueryDTO = new ConcreteAdjustGoodsQueryDTO();
            concreteAdjustGoodsQueryDTO.setAdjustPeriod(concreteAdjustOrderExportDTO.getAdjustPeriod());
            concreteAdjustGoodsQueryDTO.setContractId(concreteAdjustOrderExportDTO.getContractId());
            concreteAdjustGoodsQueryDTO.setContractSequence(concreteAdjustOrderExportDTO.getContractSequence());
            concreteAdjustGoodsQueryDTO.setAdjustGoodsIdList(Lists.newArrayList(concreteAdjustGoodsDTO.getGoodsId()));
            pageQuery.setQueryDTO(concreteAdjustGoodsQueryDTO);
            pageQuery.setPageSize(pageSize);
            PageData<ConcreteAdjustOrderDTO> pageData;
            do {
                pageQuery.setPageNum(pageNum);
                ItemResult<PageData<ConcreteAdjustOrderDTO>> pageDataItemResult = concreteAdjustService.previewAdjustOrderList(pageQuery);
                if (!pageDataItemResult.isSuccess()) {
                    throw new BizException(BasicCode.UNDEFINED_ERROR, "获取订单数据异常");
                }
                pageData = pageDataItemResult.getData();
                if (CollectionUtils.isNotEmpty(pageData.getList())) {
                    pageData.getList().stream().forEach(concreteAdjustOrderDTO -> {
                        if (concreteAdjustOrderDTO.getAdjustTradingPrice() == null) {
                            concreteAdjustOrderDTO.setAdjustTradingPrice(ArithUtils.add(concreteAdjustOrderDTO.getLatestTradingPrice(), concreteAdjustGoodsDTO.getAdjustPrice()));
                            concreteAdjustOrderDTO.setAdjustTradingAmount(ArithUtils.multiply(concreteAdjustOrderDTO.getAdjustTradingPrice(), concreteAdjustOrderDTO.getTradingQuantity()));
                        }
                        //数据处理
                        htmlBuilder.append(
                        "<tr>" +
                            "<td style='vnd.ms-excel.numberformat:@'>" + concreteAdjustOrderDTO.getOrderCode() + "</td>" +
                            "<td style='vnd.ms-excel.numberformat:@'>" + PickingBillTypeEnum.valueOfCode(concreteAdjustOrderDTO.getDeliverWay()).getDesc() + "</td>" +
                            "<td align='left' style='vnd.ms-excel.numberformat:#0.00'>" + concreteAdjustOrderDTO.getTradingQuantity() + "</td>" +
                            "<td align='left' style='vnd.ms-excel.numberformat:#0.00'>" + concreteAdjustOrderDTO.getTradingPrice() + "</td>" +
                            "<td align='left' style='vnd.ms-excel.numberformat:#0.00'>" + concreteAdjustOrderDTO.getTradingAmount() + "</td>" +
                            "<td align='left' style='vnd.ms-excel.numberformat:#0.00'>" + concreteAdjustOrderDTO.getLatestTradingPrice() + "</td>" +
                            "<td align='left' style='vnd.ms-excel.numberformat:#0.00'>" + concreteAdjustOrderDTO.getLatestTradingAmount() + "</td>" +
                            "<td align='left' style='vnd.ms-excel.numberformat:#0.00'>" + concreteAdjustOrderDTO.getAdjustTradingPrice() + "</td>" +
                            "<td align='left' style='vnd.ms-excel.numberformat:#0.00'>" + concreteAdjustOrderDTO.getAdjustTradingAmount() + "</td>" +
                            "<td style='vnd.ms-excel.numberformat:@'>" + DateUtil.format(concreteAdjustOrderDTO.getOrderCreateTime(), "yyyy-MM-dd HH:mm:ss") + "</td>" +
                            "<td style='vnd.ms-excel.numberformat:@'>" + (concreteAdjustOrderDTO.getOrderCompleteTime() == null ?
                                "" : DateUtil.format(concreteAdjustOrderDTO.getOrderCompleteTime(), "yyyy-MM-dd HH:mm:ss")) + "</td>" +
                            "<td style='vnd.ms-excel.numberformat:@'>" + concreteAdjustOrderDTO.getAdjustCount() + "</td>" +
                            "<td style='vnd.ms-excel.numberformat:@'>" + (concreteAdjustOrderDTO.getAdjustStatus() == null ?
                                "" : ConcreteAdjustStatusEnum.valueOfCode(concreteAdjustOrderDTO.getAdjustStatus()).getMessage()) + "</td>" +
                        "</tr>");
                    });
                }
                pageNum++;
            } while (pageNum <= pageData.getPages());
        });
        htmlBuilder.append("</table>");

        return htmlBuilder.toString();
    }

}
