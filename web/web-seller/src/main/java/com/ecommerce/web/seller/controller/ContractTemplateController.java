package com.ecommerce.web.seller.controller;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.member.api.session.LoginInfo;
import com.ecommerce.goods.api.dto.contract.DownloadResponseDTO;
import com.ecommerce.goods.api.dto.contract.ReqSellerContractTemplateDTO;
import com.ecommerce.goods.api.dto.contract.TrContractTemplateDTO;
import com.ecommerce.goods.api.enums.contract.TemplateStatusEnum;
import com.ecommerce.goods.api.service.IContractTemplateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;

import static org.apache.hc.core5.http.HttpHeaders.CONTENT_TYPE;


/**
 * <AUTHOR>
 * @created 10:16 16/08/2019
 * @description
 */
@Slf4j
@Tag(name = "ContractTemplateController", description = "管理合同模板")
@RestController
@RequestMapping("/contractTemplate")
public class ContractTemplateController {

    @Autowired
    private IContractTemplateService iContractTemplateService;

    private static final String CONTENT_DISPOSITION = "Content-disposition";

    @Operation(summary = "null")
    @PostMapping(value = "/createContractTemplate")
    public ItemResult<Object> createContractTemplate(LoginInfo loginInfo,
                                             @Parameter(name = "createDTO", description = "合同模板对象") @RequestBody TrContractTemplateDTO createDTO) {
        createDTO.setMemberId(loginInfo.getMemberId());
        return new ItemResult<>(iContractTemplateService.createContractTemplate(createDTO));
    }

    @Operation(summary = "null")
    @PostMapping(value = "/getContractTemplateDetail")
    public ItemResult<Object> getContractTemplateDetail(@Parameter(name = "templateId", description = "模板id") @RequestParam String templateId) {
        return new ItemResult<>(iContractTemplateService.getContractTemplateDetail(templateId));
    }

    @Operation(summary = "null")
    @PostMapping(value = "/pageContractTemplate")
    public ItemResult<Object> pageContractTemplate(LoginInfo loginInfo,
                                           @Parameter(name = "reqDTO", description = "查询合同模板入参") @RequestBody ReqSellerContractTemplateDTO reqDTO) {
        return new ItemResult<>(iContractTemplateService.pageContractTemplate(reqDTO, loginInfo.getMemberId()));
    }

    @Operation(summary = "null")
    @PostMapping(value = "/getContractTemplateList")
    public ItemResult<Object> getContractTemplateList(LoginInfo loginInfo,
                                              @Parameter(name = "req", description = "查询合同模板入参") @RequestBody ReqSellerContractTemplateDTO req) {
        return new ItemResult<>(iContractTemplateService.getContractTemplateList(req, loginInfo.getMemberId()));
    }

    @Operation(summary = "null")
    @PostMapping(value = "/deleteContractTemplate")
    public ItemResult<Object> deleteContractTemplate(@Parameter(hidden = true) LoginInfo loginInfo,
                                             @Parameter(name = "templateId", description = "模板id") @RequestParam String templateId) {
        return new ItemResult<>(iContractTemplateService.deleteContractTemplate(templateId, loginInfo.getMemberId()));
    }

    @Operation(summary = "启用")
    @PostMapping(value = "/enableTemplateStatus")
    public ItemResult<Boolean> enableTemplateStatus(@Parameter(hidden = true)LoginInfo loginInfo,
                                                    @Parameter(name = "templateId", description = "模板id") @RequestParam String templateId) {
        Integer status = TemplateStatusEnum.ENABLE.getCode();
        return new ItemResult<>(iContractTemplateService.updateContractTemplateStatus(templateId, status, loginInfo.getMemberId()));
    }

    @Operation(summary = "禁用")
    @PostMapping(value = "/disableTemplateStatus")
    public ItemResult<Boolean> disableTemplateStatus(@Parameter(hidden = true)LoginInfo loginInfo,
                                                     @Parameter(name = "templateId", description = "模板id") @RequestParam String templateId) {
        Integer status = TemplateStatusEnum.DISABLE.getCode();
        return new ItemResult<>(iContractTemplateService.updateContractTemplateStatus(templateId, status, loginInfo.getMemberId()));
    }

    @Operation(summary = "null")
    @GetMapping(value = "/downloadFileByContract")
    public void downloadFileByContract(@Parameter(name = "contractId", description = "合同id") @RequestParam String contractId,
                                       HttpServletResponse response) throws IOException {
        log.info(">>> downloadFileByContract [contractId]: {}", contractId);
        DownloadResponseDTO responseDTO = iContractTemplateService.downloadFileByContract(contractId);
        log.info("responseDTO: {}, {}", responseDTO.getBytes().length, responseDTO.getFileName());
        response.setHeader(CONTENT_DISPOSITION, responseDTO.getFileName());
        response.setHeader(CONTENT_TYPE, "application/octet-stream");
        response.setHeader("Access-Control-Expose-Headers", CONTENT_DISPOSITION);
        response.getOutputStream().write(responseDTO.getBytes());
        response.getOutputStream().flush();
        response.getOutputStream().close();
    }

    @Operation(summary = "null")
    @GetMapping(value = "/downloadFileByAdjustPrice")
    public void downloadFileByAdjustPrice(@Parameter(name = "adjustPriceId", description = "调价函id") @RequestParam String adjustPriceId,
                                          HttpServletResponse response) throws IOException {
        log.info(">>> downloadFileByAdjustPrice [adjustPriceId]: {}", adjustPriceId);
        DownloadResponseDTO responseDTO = iContractTemplateService.downloadFileByAdjustPrice(adjustPriceId);
        log.info("responseDTO: {}, {}", responseDTO.getBytes().length, responseDTO.getFileName());
        response.setHeader(CONTENT_DISPOSITION, responseDTO.getFileName());
        response.setHeader(CONTENT_TYPE, "application/octet-stream");
        response.setHeader("Access-Control-Expose-Headers", CONTENT_DISPOSITION);
        response.getOutputStream().write(responseDTO.getBytes());
        response.getOutputStream().flush();
        response.getOutputStream().close();
    }

    @Operation(summary = "null")
    @GetMapping(value = "/anon/downloadFileByContract")
    public void anonDownloadFileByContract(@Parameter(name = "contractId", description = "合同id") @RequestParam String contractId,
                                           HttpServletResponse response) throws IOException {
        DownloadResponseDTO responseDTO = iContractTemplateService.downloadFileByContract(contractId);
        response.setHeader(CONTENT_DISPOSITION, "attachment;filename=" + responseDTO.getFileName());
        response.getOutputStream().write(responseDTO.getBytes());
        response.getOutputStream().flush();
        response.getOutputStream().close();
    }
}
