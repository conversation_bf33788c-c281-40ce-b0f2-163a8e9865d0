package com.ecommerce.web.seller.controller;


import com.ecommerce.common.exception.BusinessOperationException;import com.alibaba.fastjson.JSON;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.logistics.api.dto.DeliveryDetailDTO;
import com.ecommerce.logistics.api.dto.DeliveryListDTO;
import com.ecommerce.logistics.api.dto.DeliveryQueryDTO;
import com.ecommerce.logistics.api.dto.DeliveryRerouteCondDTO;
import com.ecommerce.logistics.api.dto.DeliveryRerouteDTO;
import com.ecommerce.logistics.api.dto.DeliveryRerouteInfoDTO;
import com.ecommerce.logistics.api.dto.DeliveryStatusChangeDTO;
import com.ecommerce.logistics.api.dto.MergeDeliveryAssignDTO;
import com.ecommerce.logistics.api.dto.PayableCarriagePriceQueryDTO;
import com.ecommerce.logistics.api.dto.WaitMergeDeliveryQueryDTO;
import com.ecommerce.logistics.api.dto.WarehouseChangeDTO;
import com.ecommerce.logistics.api.dto.assign.AssignShipDTO;
import com.ecommerce.logistics.api.dto.assign.AssignVehicleDTO;
import com.ecommerce.logistics.api.dto.auto.DispatchResultOperationDTO;
import com.ecommerce.logistics.api.dto.auto.InstantAutoDispatchCondDTO;
import com.ecommerce.logistics.api.dto.auto.PlanAutoDispatchCondDTO;
import com.ecommerce.logistics.api.dto.auto.VehicleAutoAssignDTO;
import com.ecommerce.logistics.api.dto.auto.VehicleAutoAssignQueryDTO;
import com.ecommerce.logistics.api.dto.auto.WaitPlanAutoDispatchDTO;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.pickingbill.AssignDetailDTO;
import com.ecommerce.logistics.api.dto.pickingbill.OptionDTO;
import com.ecommerce.logistics.api.enums.DeliveryOperationTypeEnum;
import com.ecommerce.logistics.api.enums.MemberRoleTypeEnum;
import com.ecommerce.logistics.api.enums.PickingBillTypeEnum;
import com.ecommerce.logistics.api.enums.TransportToolTypeEnum;
import com.ecommerce.logistics.api.enums.UserRoleEnum;
import com.ecommerce.logistics.api.service.IDeliveryBillService;
import com.ecommerce.member.api.dto.account.AccountDTO;
import com.ecommerce.member.api.session.LoginInfo;
import com.google.common.collect.Lists;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description: DeliveryBillController
 * <AUTHOR>
 * @Date: 13/01/2021 10:21
 */
@Slf4j
@Tag(name = "DeliveryBill", description = ": 委托单服务接口")
@RestController
@RequestMapping("/deliveryBill")
public class DeliveryBillController {

    @Autowired
    private IDeliveryBillService iDeliveryBillService;

    @Operation(summary = "委托单列表分页查询")
    @PostMapping(value="/queryDeliveryList")
    public ItemResult<PageData<DeliveryListDTO>> queryDeliveryList(@Parameter(hidden = true)LoginInfo loginInfo, @RequestBody PageQuery<DeliveryQueryDTO> pageQuery){
        if(pageQuery != null && pageQuery.getQueryDTO() != null ){
            if (CsStringUtils.isNotBlank(pageQuery.getQueryDTO().getStatus())) {
                List<String> statusList = pageQuery.getQueryDTO().getStatusList() == null ? Lists.newArrayList() : pageQuery.getQueryDTO().getStatusList();
                statusList.add(pageQuery.getQueryDTO().getStatus());
                pageQuery.getQueryDTO().setStatusList(statusList);
            }
            //设置承运人角色类型
            if (CsStringUtils.isNotBlank(pageQuery.getQueryDTO().getCarrierId())) {
                pageQuery.getQueryDTO().setCarrierRoleType(MemberRoleTypeEnum.SELLER.getCode());
            }
            pageQuery.getQueryDTO().setSaleRegionIdList(null);
            if (loginInfo.getAccountType() == AccountDTO.ACCOUNT_TYPE_MEMBER_SUB) {
                List<String> saleRegionIdList = loginInfo.getSaleRegionIdList();
                if (CollectionUtils.isEmpty(saleRegionIdList)) {
                    return new ItemResult<>(new PageData<>(Lists.newArrayList()));
                }
                pageQuery.getQueryDTO().setSaleRegionIdList(saleRegionIdList);
            }
            if (loginInfo.getAccountType() == AccountDTO.ACCOUNT_TYPE_MEMBER_SUB) {
                if (loginInfo.getSalesman()) {
                    pageQuery.getQueryDTO().setSalesmanId(loginInfo.getAccountId());
                }
            }
            //2021.0.26  begin -- 如果查询类型为空，则根据角色默认查询类型  --问题修改：卖家中心-我的物流-提货管理-我的调度管理、我的运单管理：汽运和船运业务板块未分开，我的调度管理和我的运单管理船运和汽运数据混合一块，对于专门的船运岗位来说，多出的汽运数据容易混淆视线。
            if(CollectionUtils.isNotEmpty(loginInfo.getRoleNameList())){
                int viewAll = 0;
                String type = pageQuery.getQueryDTO().getTransportToolType();
                if (loginInfo.getRoleNameList().stream().anyMatch(item -> CsStringUtils.equals(item, "waybill_view_road"))) {//汽运运单数据查看角色
                    pageQuery.getQueryDTO().setTransportToolType(TransportToolTypeEnum.ROAD_TRANSPORT.getCode());
                    viewAll++;
                }
                if (loginInfo.getRoleNameList().stream().anyMatch(item -> CsStringUtils.equals(item, "waybill_view_water"))) {//船运运运单数据查看角色
                    pageQuery.getQueryDTO().setTransportToolType(TransportToolTypeEnum.WATER_TRANSPORT.getCode());
                    viewAll++;
                }
                if( viewAll > 1 ){
                    pageQuery.getQueryDTO().setTransportToolType(type);
                }
            }
            //2021.0.26  end -- 如果查询类型为空，则根据角色默认查询类型  --问题修改：卖家中心-我的物流-提货管理-我的调度管理、我的运单管理：汽运和船运业务板块未分开，我的调度管理和我的运单管理船运和汽运数据混合一块，对于专门的船运岗位来说，多出的汽运数据容易混淆视线。
        }
        log.info("queryDeliveryList : {}", JSON.toJSONString(pageQuery));
        return iDeliveryBillService.queryDeliveryList(pageQuery);
    }

    @Operation(summary = "委托单详情查询")
    @PostMapping(value="/queryDeliveryDetailById")
    public ItemResult<DeliveryDetailDTO> queryDeliveryDetailById(@Parameter(description = "委托单ID") @RequestParam String deliveryBillId){
        return iDeliveryBillService.queryDeliveryDetailById(deliveryBillId);
    }

    @Operation(summary = "在委托单上指派承运商")
    @PostMapping(value="/assignCarrier")
    public ItemResult<Void> assignCarrier(@Parameter(hidden = true)LoginInfo loginInfo, @RequestBody List<AssignDetailDTO> assignCarrierDTOList, @Parameter(description = "委托单Id") @RequestParam String deliveryBillId){
        if(CollectionUtils.isNotEmpty(assignCarrierDTOList)){
            assignCarrierDTOList.forEach(assignDetailDTO -> assignDetailDTO.setCarrierRoleType(MemberRoleTypeEnum.CARRIER.getCode()));
        }
        return iDeliveryBillService.assignCarrier(assignCarrierDTOList,deliveryBillId, loginInfo.getAccountId(), loginInfo.getAccountName());
    }

    @Operation(summary = "在委托单上指派平台")
    @PostMapping(value="/assignPlatform")
    public ItemResult<Void> assignPlatform(@Parameter(hidden = true)LoginInfo loginInfo, @RequestBody AssignDetailDTO assignCarrierDTOList){
        assignCarrierDTOList.setCarrierRoleType(MemberRoleTypeEnum.PLATFORM.getCode());
        return iDeliveryBillService.assignCarrier(Lists.newArrayList(assignCarrierDTOList),assignCarrierDTOList.getDeliveryBillId(), loginInfo.getAccountId(), loginInfo.getAccountName());
    }

    @Operation(summary = "安排运力-委托单上指派船")
    @PostMapping(value="/assignShip")
    public ItemResult<Void> assignShip(@Parameter(hidden = true)LoginInfo loginInfo,@RequestBody List<AssignShipDTO> assignShipDTOList, @Parameter(description = "委托单Id") @RequestParam String deliveryBillId){
        if(CollectionUtils.isNotEmpty(assignShipDTOList)){
            assignShipDTOList.forEach(assignShipDTO -> {
                assignShipDTO.setDeliveryBillId(deliveryBillId);
            });
        }
        return iDeliveryBillService.assignShip(assignShipDTOList, deliveryBillId, loginInfo.getAccountId(), loginInfo.getAccountName());
    }

    @Operation(summary = "安排运力-委托单上指派车辆")
    @PostMapping(value="/assignVehicle")
    public ItemResult<Void> assignVehicle(@Parameter(hidden = true)LoginInfo loginInfo,@RequestBody List<AssignVehicleDTO> assignVehicleDTOList, @Parameter(description = "委托单Id") @RequestParam String deliveryBillId){
        if(CollectionUtils.isNotEmpty(assignVehicleDTOList)){
            assignVehicleDTOList.forEach(assignVehicleDTO -> {
                assignVehicleDTO.setDeliveryBillId(deliveryBillId);
            });
        }
        return iDeliveryBillService.assignVehicle(assignVehicleDTOList, deliveryBillId, loginInfo.getAccountId(), loginInfo.getAccountName());
    }

    @Operation(summary = "接受委托")
    @PostMapping(value="/acceptDelivery")
    public ItemResult<Void> acceptDelivery(@Parameter(hidden = true) LoginInfo loginInfo,@Parameter(description = "委托单ID") @RequestParam String deliveryBillId){
        DeliveryStatusChangeDTO deliveryStatusChangeDTO = new DeliveryStatusChangeDTO();
        deliveryStatusChangeDTO.setDeliveryBillId(deliveryBillId);
        deliveryStatusChangeDTO.setOperationType(DeliveryOperationTypeEnum.DELIVERY_ACCEPT.getCode());
        deliveryStatusChangeDTO.setOperationUserId(loginInfo.getAccountId());
        deliveryStatusChangeDTO.setOperationUserName(loginInfo.getAccountName());
        return iDeliveryBillService.changeDeliveryStatus(deliveryStatusChangeDTO);
    }

    @Operation(summary = "拒绝委托")
    @PostMapping(value="/rejectDelivery")
    public ItemResult<Void> rejectDelivery(@Parameter(hidden = true) LoginInfo loginInfo,
                                           @Parameter(description = "委托单ID") @RequestParam String deliveryBillId,
                                           @Parameter(description = "拒绝原因") @RequestParam String reason){
        DeliveryStatusChangeDTO deliveryStatusChangeDTO = new DeliveryStatusChangeDTO();
        deliveryStatusChangeDTO.setDeliveryBillId(deliveryBillId);
        deliveryStatusChangeDTO.setOperationType(DeliveryOperationTypeEnum.DELIVERY_REJECT.getCode());
        deliveryStatusChangeDTO.setReason(reason);
        deliveryStatusChangeDTO.setOperationUserId(loginInfo.getAccountId());
        deliveryStatusChangeDTO.setOperationUserName(loginInfo.getAccountName());
        return iDeliveryBillService.changeDeliveryStatus(deliveryStatusChangeDTO);
    }

    @Operation(summary = "取消委托")
    @PostMapping(value="/cancelDelivery")
    public ItemResult<Void> cancelDelivery(@Parameter(hidden = true) LoginInfo loginInfo,@Parameter(description = "委托单ID") @RequestParam String deliveryBillId){
        DeliveryStatusChangeDTO deliveryStatusChangeDTO = new DeliveryStatusChangeDTO();
        deliveryStatusChangeDTO.setDeliveryBillId(deliveryBillId);
        deliveryStatusChangeDTO.setOperationType(DeliveryOperationTypeEnum.DELIVERY_CANCEL.getCode());
        deliveryStatusChangeDTO.setOperationUserId(loginInfo.getAccountId());
        deliveryStatusChangeDTO.setOperationUserName(loginInfo.getAccountName());
        return iDeliveryBillService.changeDeliveryStatus(deliveryStatusChangeDTO);
    }

    @Operation(summary = "中止委托")
    @PostMapping(value="/stopDelivery")
    public ItemResult<Void> stopDelivery(@Parameter(hidden = true) LoginInfo loginInfo,
                                         @Parameter(description = "委托单ID") @RequestParam String deliveryBillId,
                                         @Parameter(description = "拒绝原因") @RequestParam String reason){
        DeliveryStatusChangeDTO deliveryStatusChangeDTO = new DeliveryStatusChangeDTO();
        deliveryStatusChangeDTO.setDeliveryBillId(deliveryBillId);
        deliveryStatusChangeDTO.setOperationType(DeliveryOperationTypeEnum.DELIVERY_STOP.getCode());
        deliveryStatusChangeDTO.setReason(reason);
        deliveryStatusChangeDTO.setOperationUserId(loginInfo.getAccountId());
        deliveryStatusChangeDTO.setOperationUserName(loginInfo.getAccountName());
        return iDeliveryBillService.changeDeliveryStatus(deliveryStatusChangeDTO);
    }

    @Operation(summary = "委托时查询运费单价")
    @PostMapping(value="/queryPayableCarriagePrice")
    public ItemResult<BigDecimal> queryPayableCarriagePrice(@RequestBody PayableCarriagePriceQueryDTO queryDTO){
        if(queryDTO == null){
            queryDTO = new PayableCarriagePriceQueryDTO();
        }
        if (CsStringUtils.equals(queryDTO.getCarrierType(), MemberRoleTypeEnum.PLATFORM.getCode())) {
            queryDTO.setPickingBillType(PickingBillTypeEnum.PLATFORM_DELIVERY.getCode());
            queryDTO.setRouteTariffType(1);
        }else {
            queryDTO.setPickingBillType(PickingBillTypeEnum.SELLER_DELIVERY.getCode());
            queryDTO.setRouteTariffType(2);
        }
        return iDeliveryBillService.queryPayableCarriagePrice(queryDTO);
    }

    /**
     * 改航
     * @param deliveryRerouteDTO
     * @return
     */
    @Operation(summary = "卖家改航")
    @PostMapping(value="/reroute")
    public ItemResult<Void> reroute(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody DeliveryRerouteDTO deliveryRerouteDTO) {
        deliveryRerouteDTO.setOperationUserId(loginInfo.getAccountId());
        deliveryRerouteDTO.setOperationUserName(loginInfo.getAccountName());
        return iDeliveryBillService.reroute(deliveryRerouteDTO);
    }

    /**
     * 改航页面的委托单详情
     * @param deliveryRerouteCondDTO
     * @return
     * @throws Exception
     */
    @Operation(summary = "改航页面的委托单详情")
    @PostMapping(value="/queryCanRerouteInfo")
    public ItemResult<DeliveryRerouteInfoDTO> queryCanRerouteInfo(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody DeliveryRerouteCondDTO deliveryRerouteCondDTO) {
        deliveryRerouteCondDTO.setSellerId(loginInfo.getMemberId());
        return iDeliveryBillService.queryCanRerouteInfo(deliveryRerouteCondDTO);
    }

    @Operation(summary = "待整合委托单列表分页查询")
    @PostMapping(value="/queryWaitMergeDeliveryList")
    public ItemResult<PageData<DeliveryListDTO>> queryWaitMergeDeliveryList(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody PageQuery<WaitMergeDeliveryQueryDTO> pageQuery) {
        if (pageQuery == null) {
            WaitMergeDeliveryQueryDTO queryDTO = new WaitMergeDeliveryQueryDTO();
            pageQuery = new PageQuery<>();
            pageQuery.setQueryDTO(queryDTO);
        }
        pageQuery.getQueryDTO().setCarrierId(loginInfo.getMemberId());
        return iDeliveryBillService.queryWaitMergeDeliveryList(pageQuery);
    }

    @Operation(summary = "整合运力")
    @PostMapping(value="/mergeAssign")
    public ItemResult<Void> mergeAssign(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody MergeDeliveryAssignDTO mergeDeliveryAssignDTO) {
        mergeDeliveryAssignDTO.setOperationUserId(loginInfo.getAccountId());
        mergeDeliveryAssignDTO.setOperationUserName(loginInfo.getAccountName());
        return iDeliveryBillService.mergeAssign(mergeDeliveryAssignDTO);
    }

    @Operation(summary = "即时车辆智能调度")
    @PostMapping(value="/instantVehicleDispatch")
    public ItemResult<List<VehicleAutoAssignDTO>> instantVehicleDispatch(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody InstantAutoDispatchCondDTO instantAutoDispatchCondDTO) {
        instantAutoDispatchCondDTO.setUserId(loginInfo.getMemberId());
        instantAutoDispatchCondDTO.setUserType(UserRoleEnum.SELLER.getCode());
        instantAutoDispatchCondDTO.setOperateUserId(loginInfo.getAccountId());
        instantAutoDispatchCondDTO.setOperateUserName(loginInfo.getAccountName());
        return iDeliveryBillService.instantVehicleDispatch(instantAutoDispatchCondDTO);
    }

    @Operation(summary = "智能调度,通过委托单列表创建批次ID")
    @PostMapping(value="/createAutoDispatchBatchId")
    public ItemResult<String> createAutoDispatchBatchId(@RequestBody List<String> deliveryBillIdList) {
        return iDeliveryBillService.createAutoDispatchBatchId(deliveryBillIdList);
    }

    /**
     * batchId 查询待计划调度的委托单信息
     * @param batchId
     * @return
     */
    @Operation(summary = "查询待计划调度的委托单信息")
    @PostMapping(value="/queryWaitAutoDispatchByBatchId")
    public ItemResult<WaitPlanAutoDispatchDTO> queryWaitAutoDispatchByBatchId(@RequestParam String batchId) {
        return iDeliveryBillService.queryWaitAutoDispatchByBatchId(batchId);
    }

    /**
     * 智能调度,生成批次内的所有调度运单项
     * @param planAutoDispatchCondDTO
     * @return
     */
    @Operation(summary = "智能调度,生成批次内的所有调度运单项")
    @PostMapping(value="/planVehicleDispatch")
    public ItemResult<Void> planVehicleDispatch(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody PlanAutoDispatchCondDTO planAutoDispatchCondDTO) {
        planAutoDispatchCondDTO.setUserId(loginInfo.getMemberId());
        planAutoDispatchCondDTO.setUserType(UserRoleEnum.SELLER.getCode());
        planAutoDispatchCondDTO.setOperateUserId(loginInfo.getAccountId());
        planAutoDispatchCondDTO.setOperateUserName(loginInfo.getAccountName());
        return iDeliveryBillService.planVehicleDispatch(planAutoDispatchCondDTO);
    }

    /**
     * 计划智能调度结果的分页查询
     * @return
     */
    @Operation(summary = "计划智能调度结果的分页查询")
    @PostMapping(value="/queryAutoWaitAssignList")
    public ItemResult<PageData<VehicleAutoAssignDTO>> queryAutoWaitAssignList(@RequestBody PageQuery<VehicleAutoAssignQueryDTO> vehicleAutoAssignQueryDTO) {
        return iDeliveryBillService.queryAutoWaitAssignList(vehicleAutoAssignQueryDTO);
    }

    /**
     * 批次ID查询仓库下拉列表
     * @param batchId
     * @return
     */
    @Operation(summary = "批次ID查询仓库下拉列表")
    @PostMapping(value="/warehouseOptionsByBatchId")
    public ItemResult<List<OptionDTO>> warehouseOptionsByBatchId(@RequestParam String batchId) {
        return iDeliveryBillService.warehouseOptionsByBatchId(batchId);
    }

    /**
     * 批次ID查询收货地址下拉列表
     * @param batchId
     * @return
     */
    @Operation(summary = "批次ID查询收货地址下拉列表")
    @PostMapping(value="/receiverAddressOptionsByBatchId")
    public ItemResult<List<OptionDTO>> receiverAddressOptionsByBatchId(@RequestParam String batchId) {
        return iDeliveryBillService.receiverAddressOptionsByBatchId(batchId);
    }

    /**
     * 确认智能调度
     * @param dispatchResultOperationDTO
     * @return
     */
    @Operation(summary = "确认智能调度")
    @PostMapping(value="/confirmAutoDispatch")
    public ItemResult<Void> confirmAutoDispatch(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody DispatchResultOperationDTO dispatchResultOperationDTO) {
        dispatchResultOperationDTO.setOperateUserId(loginInfo.getAccountId());
        dispatchResultOperationDTO.setOperateUserName(loginInfo.getAccountName());
        return iDeliveryBillService.confirmAutoDispatch(dispatchResultOperationDTO);
    }

    /**
     * 拒绝智能调度
     * @param dispatchResultOperationDTO
     * @return
     */
    @Operation(summary = "拒绝智能调度")
    @PostMapping(value="/rejectAutoDispatch")
    public ItemResult<Void> rejectAutoDispatch(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody DispatchResultOperationDTO dispatchResultOperationDTO) {
        dispatchResultOperationDTO.setOperateUserId(loginInfo.getAccountId());
        dispatchResultOperationDTO.setOperateUserName(loginInfo.getAccountName());
        return iDeliveryBillService.rejectAutoDispatch(dispatchResultOperationDTO);
    }

    /**
     * 修改委托单的仓库
     * @param warehouseChangeDTO
     * @return
     */
    @Operation(summary = "修改委托单的仓库")
    @PostMapping(value="/changeDeliveryWarehouse")
    public ItemResult<Void> changeDeliveryWarehouse(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody WarehouseChangeDTO warehouseChangeDTO) {
        warehouseChangeDTO.setOperateUserId(loginInfo.getAccountId());
        warehouseChangeDTO.setOperateUserName(loginInfo.getAccountName());
        return iDeliveryBillService.changeDeliveryWarehouse(warehouseChangeDTO);
    }

}
