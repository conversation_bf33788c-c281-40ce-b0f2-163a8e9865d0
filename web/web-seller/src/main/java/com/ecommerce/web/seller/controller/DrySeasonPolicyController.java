package com.ecommerce.web.seller.controller;

import com.ecommerce.common.enums.AppNames;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.dry.season.policy.DrySeasonPolicyCreateDTO;
import com.ecommerce.logistics.api.dto.dry.season.policy.DrySeasonPolicyDTO;
import com.ecommerce.logistics.api.dto.dry.season.policy.DrySeasonPolicyDeleteDTO;
import com.ecommerce.logistics.api.dto.dry.season.policy.DrySeasonPolicyListDTO;
import com.ecommerce.logistics.api.dto.dry.season.policy.DrySeasonPolicyQueryDTO;
import com.ecommerce.logistics.api.dto.dry.season.policy.DrySeasonPolicyUpdateDTO;
import com.ecommerce.logistics.api.dto.dry.season.policy.DrySeasonPolicyWaterLevelTemplateDTO;
import com.ecommerce.logistics.api.dto.dry.season.policy.DrySeasonPolicyWaterLevelTemplateEditDTO;
import com.ecommerce.logistics.api.dto.dry.season.policy.DrySeasonPolicyWaterLevelTemplateQueryDTO;
import com.ecommerce.logistics.api.dto.dry.season.policy.PolicyShippingRouteRelationAddDTO;
import com.ecommerce.logistics.api.dto.dry.season.policy.PolicyShippingRouteRelationRemoveDTO;
import com.ecommerce.logistics.api.enums.ShippingTypeEnum;
import com.ecommerce.logistics.api.service.IDrySeasonPolicyService;
import com.ecommerce.member.api.session.LoginInfo;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;


@Slf4j
@Tag(name = "DrySeasonPolicyController", description = "枯水期策略服务接口")
@CrossOrigin
@RestController
@RequestMapping(value = "/drySeasonPolicy")
public class DrySeasonPolicyController {

    @Autowired
    private IDrySeasonPolicyService drySeasonPolicyService;

    @Operation(summary = "添加策略")
    @PostMapping(value="/addPolicy")
    public ItemResult<Boolean> addPolicy(@Parameter(hidden = true)LoginInfo loginInfo, @RequestBody DrySeasonPolicyCreateDTO dto){
        dto.setCreateUser(loginInfo.getAccountId());
        dto.setBelongerId(loginInfo.getMemberId());
        return drySeasonPolicyService.addPolicy(dto);
    }

    @Operation(summary = "更新策略")
    @PostMapping(value="/updatePolicy")
    public ItemResult<Boolean> updatePolicy(@Parameter(hidden = true)LoginInfo loginInfo,@RequestBody DrySeasonPolicyUpdateDTO dto){
        dto.setAppName(AppNames.WEB_SERVICE_SELLER.getPlatform());
        dto.setOperator(loginInfo.getAccountId());
        dto.setOperatorMemberId(loginInfo.getMemberId());
        dto.setBelongerId(loginInfo.getMemberId());
        return drySeasonPolicyService.updatePolicy(dto);
    }

    @Operation(summary = "添加策略-线路绑定(航运线路编辑使用)")
    @PostMapping(value="/updatePolicyShippingRouteRelation")
    public ItemResult<Boolean> updatePolicyShippingRouteRelation(@Parameter(hidden = true)LoginInfo loginInfo,@RequestBody PolicyShippingRouteRelationAddDTO dto){
        dto.setAppName(AppNames.WEB_SERVICE_SELLER.getPlatform());
        dto.setOperator(loginInfo.getAccountId());
        dto.setOperatorMemberId(loginInfo.getMemberId());
        return drySeasonPolicyService.addPolicyShippingRouteRelation(dto);
    }

    @Operation(summary = "添加策略-线路绑定(航运线路编辑使用)")
    @PostMapping(value="/removePolicyShippingRouteRelation")
    public ItemResult<Boolean> updatePolicyShippingRouteRelation(@Parameter(hidden = true)LoginInfo loginInfo,@RequestBody PolicyShippingRouteRelationRemoveDTO dto){
        dto.setAppName(AppNames.WEB_SERVICE_SELLER.getPlatform());
        dto.setOperator(loginInfo.getAccountId());
        dto.setOperatorMemberId(loginInfo.getMemberId());
        return drySeasonPolicyService.removePolicyShippingRouteRelation(dto);
    }

    @Operation(summary = "删除策略")
    @PostMapping(value="/deletePolicy")
    public ItemResult<Boolean> deletePolicy(@Parameter(hidden = true)LoginInfo loginInfo,@RequestBody DrySeasonPolicyDeleteDTO dto){
        dto.setAppName(AppNames.WEB_SERVICE_SELLER.getPlatform());
        dto.setOperator(loginInfo.getAccountId());
        dto.setOperatorMemberId(loginInfo.getMemberId());
        return drySeasonPolicyService.deletePolicy(dto);
    }

    @Operation(summary = "翻页查询策略")
    @PostMapping(value="/pageInfoPolicy")
    public ItemResult<PageInfo<DrySeasonPolicyListDTO>> pageInfoPolicy(@Parameter(hidden = true)LoginInfo loginInfo,@RequestBody PageQuery<DrySeasonPolicyQueryDTO> dto){
        dto.getQueryDTO().setMemberId(loginInfo.getMemberId());
        return drySeasonPolicyService.pageInfoPolicy(dto);
    }

    @Operation(summary = "根据id查询策略")
    @GetMapping(value="/findById")
    public ItemResult<DrySeasonPolicyDTO> findById(@Parameter(hidden = true) LoginInfo loginInfo,@RequestParam String id){
        return drySeasonPolicyService.findById(id);
    }

    @Operation(summary = "根据航线id查询策略")
    @GetMapping(value="/findByShippingRouteId")
    public ItemResult<List<DrySeasonPolicyDTO>> findByShippingRouteId(@Parameter(hidden = true) LoginInfo loginInfo,@RequestParam String shippingRouteId){
        return drySeasonPolicyService.findByShippingRouteId(shippingRouteId);
    }

    @Operation(summary = "查询水位系数表母版")
    @GetMapping(value="/queryWaterLevelTemplate")
    public ItemResult<DrySeasonPolicyWaterLevelTemplateDTO> queryWaterLevelTemplate(@Parameter(hidden = true) LoginInfo loginInfo){
        DrySeasonPolicyWaterLevelTemplateQueryDTO dto = new DrySeasonPolicyWaterLevelTemplateQueryDTO();
        dto.setBelongerId(loginInfo.getMemberId());
        return drySeasonPolicyService.queryWaterLevelTemplate(dto);
    }

    @Operation(summary = "编辑修改水位系数表母版")
    @PostMapping(value="/editWaterLevelTemplate")
    public ItemResult<Boolean> editWaterLevelTemplate(@Parameter(hidden = true) LoginInfo loginInfo,@RequestBody DrySeasonPolicyWaterLevelTemplateEditDTO dto){
        dto.setOperator(loginInfo.getAccountId());
        dto.setMemberId(loginInfo.getMemberId());
        return drySeasonPolicyService.editWaterLevelTemplate(dto);
    }

    @Operation(summary = "船型枚举列表")
    @GetMapping(value="/shippingTypeList")
    public ItemResult<List<Map<String,String>>> shippingTypeList(){
        List<Map<String,String>> list = Lists.newArrayList();
        for (ShippingTypeEnum value : ShippingTypeEnum.values()) {
            list.add(ImmutableMap.of("id",value.getCode(),"name",value.getDesc()));
        }
        return new ItemResult<>(list);
    }
}
