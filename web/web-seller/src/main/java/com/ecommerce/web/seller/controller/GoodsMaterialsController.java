package com.ecommerce.web.seller.controller;


import com.ecommerce.common.result.ItemResult;
import com.ecommerce.goods.api.dto.MaterialsDTO;
import com.ecommerce.goods.api.dto.PageSellerMaterialsDTO;
import com.ecommerce.goods.api.service.IGoodsMaterialsService;
import com.ecommerce.member.api.session.LoginInfo;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.util.List;


/**
 * @Auther: chfu
 * @Description: GoodsMaterialsController
 * @Created：Fri Aug 31 18:47:03 CST 2019
 */
@Slf4j
@Tag(name = "GoodsMaterialsController", description = "商品物料服务")
@RestController
@RequestMapping("/goodsMaterials")
public class GoodsMaterialsController {

    @Autowired
    private IGoodsMaterialsService goodsMaterialsService;

    @Operation(summary = "同步ERP物料属性")
    @PostMapping(value = "/syncGoodsMaterials")
    public ItemResult<Boolean> syncGoodsMaterials(@Parameter(hidden = true) LoginInfo loginInfo,
                                                  @Parameter(name = "sellerId", description = "卖家id") @Valid @RequestParam String sellerId) {
        goodsMaterialsService.syncGoodsMaterials(sellerId, loginInfo.getAccountId());
        return new ItemResult<>(true);
    }

    @Operation(summary = "分页查询商品物料")
    @PostMapping(value = "/pageSellerMaterials")
    public ItemResult<PageInfo<MaterialsDTO>> pageSellerMaterials(@Parameter(hidden = true) LoginInfo loginInfo,
                                                                  @Parameter(name = "pageSellerMaterialsDTO", description = "卖家物料分页DTO") @Valid @RequestBody PageSellerMaterialsDTO pageSellerMaterialsDTO) {
        pageSellerMaterialsDTO.setSellerId(loginInfo.getMemberId());
        return new ItemResult<>(goodsMaterialsService.pageSellerMaterials(pageSellerMaterialsDTO));
    }

    @Operation(summary = "查询商品物料列表")
    @PostMapping(value = "/getSellerMaterialsList")
    public ItemResult<List<MaterialsDTO>> getSellerMaterialsList(@Parameter(hidden = true) LoginInfo loginInfo) {
        return new ItemResult<>(goodsMaterialsService.getSellerMaterialsList(loginInfo.getMemberId()));
    }

    @Operation(summary = "获取商品物料详情通过物料ID")
    @PostMapping(value = "/getMaterialsById")
    public ItemResult<MaterialsDTO> getMaterialsById(@Parameter(hidden = true) LoginInfo loginInfo,
                                                     @Parameter(name = "materialsId", description = "物料ID") @RequestParam String materialsId) {
        return new ItemResult<>(goodsMaterialsService.getMaterialsById(materialsId));
    }

    @Operation(summary = "获取商品物料详情通过物料商品编码")
    @PostMapping(value = "/getMaterialsByCode")
    public ItemResult<MaterialsDTO> getMaterialsByCode(@Parameter(hidden = true) LoginInfo loginInfo,
                                                       @Parameter(name = "commodityCode", description = "物料商品编码") @RequestParam String commodityCode) {
        return new ItemResult<>(goodsMaterialsService.getMaterialsByCode(commodityCode));
    }

    @Operation(summary = "通过商品获取物料编码")
    @PostMapping(value = "/getMaterialsCodeByGoods")
    public ItemResult<MaterialsDTO> getMaterialsCodeByGoods(@Parameter(hidden = true) LoginInfo loginInfo,
                                                            @Parameter(name = "goodsId", description = "商品id") @RequestParam String goodsId) {
        return new ItemResult<>(goodsMaterialsService.getMaterialsCodeByGoods(goodsId));
    }

    @Operation(summary = "通过资源获取物料编码")
    @PostMapping(value = "/getMaterialsCodeByResource")
    public ItemResult<MaterialsDTO> getMaterialsCodeByResource(@Parameter(hidden = true) LoginInfo loginInfo,
                                                               @Parameter(name = "resourceId", description = "资源id") @RequestParam String resourceId) {
        return new ItemResult<>(goodsMaterialsService.getMaterialsCodeByResource(resourceId));
    }
}
