package com.ecommerce.web.seller.controller;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.ReceiveAddressDTO;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.delivery.InternalAllocationDTO;
import com.ecommerce.logistics.api.dto.waybill.AllocationListQueryDTO;
import com.ecommerce.logistics.api.service.IInternalAllocationService;
import com.ecommerce.member.api.session.LoginInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created by hexinhui3 on 2021/8/11 17:23
 */
@Slf4j
@Tag(name = "InternalAllocation", description = ": 内部调拨服务")
@RestController
@RequestMapping("/internalAllocation")
public class InternalAllocationController {

    @Autowired
    private IInternalAllocationService allocationService;

    @Operation(summary = "获取内部调拨列表")
    @PostMapping(value="/queryInternalAllocationList")
    public ItemResult<PageData<InternalAllocationDTO>> queryInternalAllocationList(@Parameter(hidden = true) LoginInfo info, @RequestBody PageQuery<AllocationListQueryDTO> pageQuery){
        pageQuery.getQueryDTO().setOwnerId(info.getMemberId());
        return allocationService.queryInternalAllocationList(pageQuery);
    }

    @Operation(summary = "获取内部调拨详情")
    @PostMapping(value="/getInternalAllocation")
    public ItemResult<InternalAllocationDTO> getInternalAllocation(@Parameter(hidden = true) LoginInfo info,@RequestBody InternalAllocationDTO dto){
        dto.setOwnerId(info.getMemberId());
        return allocationService.getInternalAllocation(dto);
    }

    @Operation(summary = "保存内部调拨")
    @PostMapping(value="/saveInternalAllocation")
    public ItemResult<Void> saveInternalAllocation(@Parameter(hidden = true) LoginInfo info, @RequestBody InternalAllocationDTO dto){
        dto.setOwnerId(info.getMemberId());
        dto.setOwnerName(info.getMemberName());
        dto.setUserId(info.getAccountId());
        dto.setUserName(info.getAccountName());
        ItemResult<ReceiveAddressDTO> itemResult = allocationService.saveInternalAllocation(dto);
        if (itemResult == null || !itemResult.isSuccess()) {
            log.error("添加地址失败:{}", itemResult);
            return new ItemResult<>();
        }
        return new ItemResult<>(null);
    }


    @Operation(summary = "完成内部调拨运单")
    @PostMapping(value="/completeShipBill")
    public ItemResult<Void> completeShipBill(@Parameter(hidden = true) LoginInfo info,@RequestBody InternalAllocationDTO dto){
        dto.setOwnerId(info.getMemberId());
        dto.setOwnerName(info.getMemberName());
        dto.setUserId(info.getAccountId());
        dto.setUserName(info.getAccountName());
        return allocationService.completeShipBill(dto);
    }


    @Operation(summary = "取消内部调拨运单")
    @PostMapping(value="/cancelShipBill")
    public ItemResult<Void> cancelShipBill(@Parameter(hidden = true) LoginInfo info,@RequestBody InternalAllocationDTO dto){
        dto.setOwnerId(info.getMemberId());
        dto.setOwnerName(info.getMemberName());
        dto.setUserId(info.getAccountId());
        dto.setUserName(info.getAccountName());
        return allocationService.cancelShipBill(dto);
    }

}
