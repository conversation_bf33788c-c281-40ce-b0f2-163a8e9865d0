package com.ecommerce.web.seller.controller;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.invoice.ConfirmInvoiceReqDTO;
import com.ecommerce.logistics.api.dto.invoice.InvoiceBillCondDTO;
import com.ecommerce.logistics.api.dto.invoice.InvoiceBillListDTO;
import com.ecommerce.logistics.api.dto.invoice.PreviewInvoiceApplyReqDTO;
import com.ecommerce.logistics.api.dto.invoice.PreviewInvoiceApplyResDTO;
import com.ecommerce.logistics.api.enums.InvoiceTargetTypeEnum;
import com.ecommerce.logistics.api.service.ILgsElectricInvoiceService;
import com.ecommerce.member.api.session.LoginInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * : 物流费电子发票服务
 *
 * <AUTHOR>
 */

@RestController
@Tag(name = "LgsElectricInvoiceController", description = "物流费电子发票服务")
@RequestMapping("/lgsElectricInvoice")
public class LgsElectricInvoiceController {

    @Autowired
    private ILgsElectricInvoiceService iLgsElectricInvoiceService;

    @Operation(summary = "批量开票申请预览请求")
    @PostMapping(value = "/applyLgsWaybillsInvoice")
    public ItemResult<PreviewInvoiceApplyResDTO> applyLgsWaybillsInvoice(@Parameter(name = "previewInvoiceApplyReqDTO", description = "申请开票预览请求实体") @RequestBody PreviewInvoiceApplyReqDTO previewInvoiceApplyReqDTO) {
        return iLgsElectricInvoiceService.applyLgsWaybillsInvoice(previewInvoiceApplyReqDTO);
    }

    @Operation(summary = "查询可开物流费发票的运单列表")
    @PostMapping(value = "/queryLgsWaybillsForInvoice")
    public ItemResult<PageData<InvoiceBillListDTO>> queryLgsWaybillsForInvoice(@Parameter(hidden = true) LoginInfo loginInfo,
                                                                               @Parameter(name = "pageQuery", description = "开票对应单据查询条件实体分页查询对象") @RequestBody PageQuery<InvoiceBillCondDTO> pageQuery) {
        if (pageQuery == null) {
            pageQuery = new PageQuery<>();
        }
        if (pageQuery.getQueryDTO() == null) {
            pageQuery.setQueryDTO(new InvoiceBillCondDTO());
        }

        InvoiceBillCondDTO queryDTO = pageQuery.getQueryDTO();
        queryDTO.setInvoiceUserId(loginInfo.getMemberId());
        queryDTO.setInvoiceTargetType(InvoiceTargetTypeEnum.SELLER_INV.getCode());
        queryDTO.setRegionCodeList(loginInfo.getAccountRegionAdCodeList());

        return iLgsElectricInvoiceService.queryLgsWaybillsForInvoice(pageQuery);
    }

    @Operation(summary = "批量开票申请确认请求")
    @PostMapping(value = "/batchConfirmInvoiceApply")
    public ItemResult<Void> batchConfirmInvoiceApply(@Parameter(hidden = true) LoginInfo loginInfo,
                                                     @Parameter(name = "confirmInvoiceReqDTO", description = "确认开票请求实体") @RequestBody ConfirmInvoiceReqDTO confirmInvoiceReqDTO) {
        confirmInvoiceReqDTO.setCreateUser(loginInfo.getAccountId());
        return iLgsElectricInvoiceService.batchConfirmInvoiceApply(confirmInvoiceReqDTO);
    }
}
