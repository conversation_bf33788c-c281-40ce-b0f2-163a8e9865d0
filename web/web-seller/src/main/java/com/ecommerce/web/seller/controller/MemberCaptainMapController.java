package com.ecommerce.web.seller.controller;

import com.ecommerce.base.api.enums.UserRoleEnum;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.exception.BusinessOperationException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.shipping.BindCaptainLogDTO;
import com.ecommerce.logistics.api.service.IShippingInfoService;
import com.ecommerce.member.api.dto.account.AccountDTO;
import com.ecommerce.member.api.dto.base.PageQuery;
import com.ecommerce.member.api.dto.captain.*;
import com.ecommerce.member.api.service.IMemberCaptainMapService;
import com.ecommerce.member.api.session.LoginInfo;
import com.ecommerce.web.seller.dto.member.CaptainExtDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@Tag(name = "MemberCaptainMapController", description = "会员船长绑定映射服务")
@RestController
@RequestMapping("/memberCaptainMap")
public class MemberCaptainMapController {

    @Autowired
    private IMemberCaptainMapService memberCaptainMapService;

    @Autowired
    private IShippingInfoService shippingInfoService;

    @Operation(summary = "创建船长账号")
    @PostMapping(value = "/createCaptain")
    public ItemResult<AccountDTO> createCaptain(@Parameter(hidden = true) LoginInfo loginInfo,
                                                @Parameter(name = "captainRegisterDTO", description = "船长注册DTO") @RequestBody CaptainRegisterDTO captainRegisterDTO) {
        captainRegisterDTO.setOperatorId(loginInfo.getAccountId());
        captainRegisterDTO.setOperatorMemberId(loginInfo.getMemberId());
        captainRegisterDTO.setOperatorMemberName(loginInfo.getMemberName());
        captainRegisterDTO.setOperatorMemberType(UserRoleEnum.SELLER.getCode());
        captainRegisterDTO.setRegisterApp("web-seller");
        return memberCaptainMapService.createCaptain(captainRegisterDTO);
    }

    @Operation(summary = "绑定船长账号")
    @PostMapping(value = "/bindCaptain")
    public ItemResult<Void> bindCaptain(@Parameter(hidden = true) LoginInfo loginInfo,
                                        @Parameter(name = "captainCreateDTO", description = "船长绑定关系创建DTO") @RequestBody CaptainCreateDTO captainCreateDTO) {
        captainCreateDTO.setBindingMemberId(loginInfo.getMemberId());
        captainCreateDTO.setBindingMemberName(loginInfo.getMemberName());
        captainCreateDTO.setBindingMemberType(UserRoleEnum.SELLER.getCode());
        captainCreateDTO.setCreateUser(loginInfo.getAccountId());
        return memberCaptainMapService.bindCaptain(captainCreateDTO);
    }

    @Operation(summary = "解绑船长账号")
    @PostMapping(value = "/unbindCaptain")
    public ItemResult<Void> unbindCaptain(@Parameter(hidden = true) LoginInfo loginInfo,
                                          @Parameter(name = "list", description = "船长绑定关系修改DTO列表") @RequestBody List<CaptainUpdateDTO> list) {
        list.forEach(i -> {
            i.setBindingMemberId(loginInfo.getMemberId());
            i.setBindingMemberType(UserRoleEnum.SELLER.getCode());
            i.setUpdateUser(loginInfo.getAccountId());
        });
        return memberCaptainMapService.unbindCaptain(list);
    }

    @Operation(summary = "启用禁用船长账号")
    @PostMapping(value = "/updateCaptainMapDisableFlg")
    public ItemResult<Void> updateCaptainMapDisableFlg(@Parameter(hidden = true) LoginInfo loginInfo,
                                                       @Parameter(name = "list", description = "船长绑定关系修改DTO列表") @RequestBody List<CaptainUpdateDTO> list) {
        list.forEach(i -> {
            i.setBindingMemberId(loginInfo.getMemberId());
            i.setBindingMemberType(UserRoleEnum.SELLER.getCode());
            i.setUpdateUser(loginInfo.getAccountId());
        });
        return memberCaptainMapService.updateCaptainMapDisableFlg(list);
    }

    @Operation(summary = "查询会员船长绑定关系")
    @PostMapping(value = "/findCaptainByCondition")
    public ItemResult<CaptainDTO> findCaptainByCondition(@Parameter(hidden = true) LoginInfo loginInfo,
                                                         @Parameter(name = "queryDTO", description = "船长查询DTO") @RequestBody CaptainQueryDTO queryDTO) {
        queryDTO.setBindingMemberId(loginInfo.getMemberId());
        queryDTO.setBindingMemberType(UserRoleEnum.SELLER.getCode());
        ItemResult<CaptainDTO> captainItemResult = memberCaptainMapService.findCaptainByCondition(queryDTO);
        CaptainExtDTO captainExtDTO = new CaptainExtDTO();
        if (captainItemResult != null && captainItemResult.isSuccess() && captainItemResult.getData() != null) {
            BeanUtils.copyProperties(captainItemResult.getData(), captainExtDTO);
            ItemResult<List<BindCaptainLogDTO>> logItemResult = shippingInfoService.queryBindCaptainLog(captainItemResult.getData().getCaptainAccountName());
            if (logItemResult != null && logItemResult.isSuccess()) {
                captainExtDTO.setBindCaptainLogList(logItemResult.getData());
            }
        }
        return new ItemResult<>(captainExtDTO);
    }

    @Operation(summary = "列表查询会员船长绑定关系")
    @PostMapping(value = "/queryCaptainList")
    public ItemResult<List<CaptainDTO>> queryCaptainList(@Parameter(hidden = true) LoginInfo loginInfo,
                                                         @Parameter(name = "queryDTO", description = "船长查询DTO") @RequestBody CaptainQueryDTO queryDTO) {
        queryDTO.setBindingMemberId(loginInfo.getMemberId());
        queryDTO.setBindingMemberType(UserRoleEnum.SELLER.getCode());
        return memberCaptainMapService.queryCaptainList(queryDTO);
    }

    @Operation(summary = "分页查询会员船长绑定关系")
    @PostMapping(value = "/pageCaptainList")
    public ItemResult<PageData<CaptainDTO>> pageCaptainList(@Parameter(hidden = true) LoginInfo loginInfo,
                                                            @Parameter(name = "pageQuery", description = "船长查询DTO分页查询对象") @RequestBody PageQuery<CaptainQueryDTO> pageQuery) {
        if (pageQuery == null || pageQuery.getQueryDTO() == null) {
            throw new BizException(BasicCode.INVALID_PARAM, "查询对象不能为空");
        }
        pageQuery.getQueryDTO().setBindingMemberId(loginInfo.getMemberId());
        pageQuery.getQueryDTO().setBindingMemberType(UserRoleEnum.SELLER.getCode());
        return memberCaptainMapService.pageCaptainList(pageQuery);
    }
}
