package com.ecommerce.web.seller.controller;

import com.alibaba.fastjson.JSON;
import com.ecommerce.base.api.dto.warehouse.WarehouseDetailsDTO;
import com.ecommerce.base.api.service.IWarehouseService;
import com.ecommerce.common.enums.AppNames;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.exception.BusinessOperationException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.service.RedisService;
import com.ecommerce.common.utils.BeanConvertUtils;
import com.ecommerce.common.utils.IPUtils;
import com.ecommerce.common.utils.MD5;
import com.ecommerce.member.api.dto.member.enums.MemberPlatform;
import com.ecommerce.member.api.dto.member.enums.MemberTypeEnum;
import com.ecommerce.member.api.dto.relation.MemberRelationDTO;
import com.ecommerce.member.api.dto.relation.MemberRelationQueryDTO;
import com.ecommerce.member.api.service.IMemberRelationService;
import com.ecommerce.member.api.service.IMemberService;
import com.ecommerce.member.api.session.LoginInfo;
import com.ecommerce.open.api.dto.gnete.enums.ApplyTicketJumpTypeEnum;
import com.ecommerce.order.api.enums.PayDetailTypeEnum;
import com.ecommerce.pay.api.v2.dto.BankCityDTO;
import com.ecommerce.pay.api.v2.dto.BankInfoDTO;
import com.ecommerce.pay.api.v2.dto.BaseBankDTO;
import com.ecommerce.pay.api.v2.dto.ChannelCardDTO;
import com.ecommerce.pay.api.v2.dto.ChannelCardRequestDTO;
import com.ecommerce.pay.api.v2.dto.ChannelCardResponseDTO;
import com.ecommerce.pay.api.v2.dto.ChannelConfigDTO;
import com.ecommerce.pay.api.v2.dto.CreditMemberChannelDTO;
import com.ecommerce.pay.api.v2.dto.GneteAggregationChannelCloseDTO;
import com.ecommerce.pay.api.v2.dto.GneteAggregationChannelOpenDTO;
import com.ecommerce.pay.api.v2.dto.GneteWithholdGrantDTO;
import com.ecommerce.pay.api.v2.dto.GneteWithholdGrantQueryDTO;
import com.ecommerce.pay.api.v2.dto.KeyValueDTO;
import com.ecommerce.pay.api.v2.dto.MemberChannelDTO;
import com.ecommerce.pay.api.v2.dto.MemberChannelResponseDTO;
import com.ecommerce.pay.api.v2.dto.MemberCreditContextDTO;
import com.ecommerce.pay.api.v2.dto.MemberCreditDTO;
import com.ecommerce.pay.api.v2.dto.MemberCreditQueryDTO;
import com.ecommerce.pay.api.v2.dto.MemberPayChannelDTO;
import com.ecommerce.pay.api.v2.dto.PasswordRequestDTO;
import com.ecommerce.pay.api.v2.dto.PasswordResponseDTO;
import com.ecommerce.pay.api.v2.dto.RechargeRequestDTO;
import com.ecommerce.pay.api.v2.dto.RechargeResponseDTO;
import com.ecommerce.pay.api.v2.dto.SendVerificationCodeRequestDTO;
import com.ecommerce.pay.api.v2.dto.SendVerificationCodeResponseDTO;
import com.ecommerce.pay.api.v2.dto.SmallAmountTransferDTO;
import com.ecommerce.pay.api.v2.dto.bill.BillLogsDTO;
import com.ecommerce.pay.api.v2.dto.bill.TradingFlowExportDTO;
import com.ecommerce.pay.api.v2.dto.electricInvoiceAccount.PageQuery;
import com.ecommerce.pay.api.v2.dto.gnete.ApplyTicketReqDTO;
import com.ecommerce.pay.api.v2.dto.gnete.ApplyTicketResDTO;
import com.ecommerce.pay.api.v2.dto.pinganjz.CashWithdrawalDTO;
import com.ecommerce.pay.api.v2.dto.query.BalanceQueryDTO;
import com.ecommerce.pay.api.v2.dto.redis.key.PayRedisKey;
import com.ecommerce.pay.api.v2.enums.AggregationPayStatusEnum;
import com.ecommerce.pay.api.v2.enums.ChannelCodeEnum;
import com.ecommerce.pay.api.v2.enums.ChannelPaymentTypeEnum;
import com.ecommerce.pay.api.v2.enums.MemberChannelStatusEnum;
import com.ecommerce.pay.api.v2.service.IBankInfoService;
import com.ecommerce.pay.api.v2.service.IBillLogsService;
import com.ecommerce.pay.api.v2.service.IMemberChannelService;
import com.ecommerce.pay.api.v2.service.IMemberCreditService;
import com.ecommerce.pay.api.v2.service.IPaymentService;
import com.ecommerce.pay.api.v2.service.IWalletAccountService;
import com.ecommerce.web.seller.dto.member.MemberRelationV2DTO;
import com.ecommerce.web.seller.dto.pay.PaymentChannelDTO;
import com.ecommerce.web.seller.dto.pay.store.v2.PayChannelDTO;
import com.ecommerce.web.seller.util.TradingFlowExcelUtil;
import com.github.pagehelper.PageInfo;
import com.google.code.kaptcha.Producer;
import com.google.common.collect.Lists;
import feign.Response;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.common.utils.mail.StreamKit;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.apache.hc.core5.http.HttpHeaders.CACHE_CONTROL;


/**
 * @Created: 10:01 12/12/2018
 * <AUTHOR>
 * @Description: TODO
 */
@RestController
@RequestMapping("/memberChannel/v2")
@Slf4j
@Tag(name = "MemberChannelV2Controller", description = "会员支付渠道")
public class MemberChannelV2Controller {

    private static final String CAPTCHA_KEY = "captchaKey_";

    private static final String BIND_CARD_CAPTCHA_KEY = "bind_card";

    private static final String NO_CACHE = "no-cache";

    @Autowired
    private IMemberChannelService memberChannelService;
    @Autowired
    private IMemberService memberService;
    @Autowired
    private IBillLogsService billLogsService;

    @Autowired
    private IMemberRelationService memberRelationService;

    @Autowired
    private IPaymentService paymentService;

    @Autowired
    private IBankInfoService bankInfoService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private IWarehouseService warehouseService;

    @Autowired
    private IWalletAccountService walletAccountService;
    @Autowired
    private IMemberCreditService memberCreditService;
    //图形验证码服务
    @Autowired
    @Qualifier("kaptcha")
    private Producer producer;

    /**
     * 开通支付渠道
     */
    @Operation(summary = "开通支付渠道")
    @PostMapping("/openMemberChannel")
    public ItemResult<Object> openMemberChannel(@Parameter(hidden = true) LoginInfo loginInfo,
                                        @Parameter(name = "memberChannelDTO", description = "会员渠道DTO") @RequestBody MemberChannelDTO memberChannelDTO) {
        memberChannelDTO.setMemberId(loginInfo.getMemberId());
        memberChannelDTO.setMemberCode(loginInfo.getMemberCode());
        memberChannelDTO.setMemberName(loginInfo.getMemberName());
        String channelCode = memberChannelDTO.getChannelCode();
        if (CsStringUtils.isEmpty(channelCode)) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "请选择渠道");
        }
        //bug7050修改，判断移到中心层
        return new ItemResult<>(memberChannelService.openMemberChannel(memberChannelDTO, ChannelPaymentTypeEnum.PAYEE.getCode(),
                loginInfo.getAccountId()));
    }

    /**
     * 关闭支付渠道
     */
    @Operation(summary = "关闭支付渠道")
    @PostMapping("/closeMemberChannel")
    public ItemResult<Object> closeMemberChannel(@Parameter(hidden = true) LoginInfo loginInfo,
                                         @Parameter(name = "channelCode", description = "渠道编号") @RequestParam String channelCode) {
        MemberChannelDTO memberChannelDTO = new MemberChannelDTO();
        memberChannelDTO.setMemberId(loginInfo.getMemberId());
        memberChannelDTO.setChannelCode(channelCode);
        //bug7050 关闭渠道但是不删除银行卡，改为修改状态
        memberChannelService.closeMemberChannel(memberChannelDTO, ChannelPaymentTypeEnum.PAYEE.getCode(), loginInfo.getAccountId());
        return new ItemResult<>(true);
    }


    @Operation(summary = "解析银行卡")
    @GetMapping(value = "/searchBankCardInfo")
    public ItemResult<Object> searchBankCardInfo(@Parameter(name = "bankAccount", description = "银行账户") @RequestParam String bankAccount) {
        return new ItemResult<>(bankInfoService.searchBankCardInfo(bankAccount));
    }

    /**
     * 查询银行卡类型和图标
     */
    @Operation(summary = "查询银行卡类型和图标")
    @GetMapping("/searchBankCardInfoByBankName")
    public ItemResult<Object> searchBankCardInfoByBankName(@Parameter(name = "bankName", description = "银行名称") @RequestParam String bankName) {
        return new ItemResult<>(bankInfoService.searchBankCardInfoByBankName(bankName));
    }

    @Operation(summary = "解析银行卡并查出相应的超级网银号")
    @GetMapping(value = "/searchBankCardInfoAndSuperBank")
    public ItemResult<Object> searchBankCardInfoAndSuperBank(@Parameter(name = "bankAccount", description = "银行账户") @RequestParam String bankAccount) {
        return new ItemResult<>(bankInfoService.searchBankCardInfoAndSuperBank(bankAccount));
    }

    @Operation(summary = "分页查询银行信息，默认返回10条")
    @PostMapping(value = "/pageBankInfo")
    public ItemResult<Object> pageBankInfo(@Parameter(name = "query", description = "银行信息DTO") @RequestBody BankInfoDTO query) {
        // cityCode = cityOraareacode   bankclsCode  bankName
        PageInfo<BankInfoDTO> dtos = bankInfoService.pageBankInfo(query);
        return new ItemResult<>(dtos);
    }

    @Operation(summary = "查询基础银行")
    @GetMapping(value = "/searchBaseBankLimit")
    public ItemResult<Object> searchBaseBankLimit(@Parameter(name = "name", description = "基础银行名称") @RequestParam(required = false) String name) {
        List<BaseBankDTO> dtos = bankInfoService.searchBaseBankLimit(name);
        return new ItemResult<>(dtos);
    }

    @Operation(summary = "通过父code查询城市信息")
    @PostMapping(value = "/searchBankCity")
    public ItemResult<Object> searchBankCity(@Parameter(name = "parentCode", description = "父code") @RequestParam(required = false) String parentCode) {
        List<BankCityDTO> dtos = bankInfoService.searchBankCity(parentCode);
        return new ItemResult<>(dtos);
    }


    @Operation(summary = "查询超级网银银行")
    @GetMapping(value = "/searchSuperBankLimit")
    public ItemResult<Object> searchSuperBankLimit(@Parameter(name = "name", description = "超级网银银行名称") @RequestParam(required = false) String name) {
        List<BaseBankDTO> dtos = bankInfoService.searchSuperBankLimit(name);
        return new ItemResult<>(dtos);
    }

    @Operation(summary = "添加银行卡")
    @PostMapping(value = "/addChannelCard")
    public ItemResult<Object> addChannelCard(@Parameter(hidden = true) LoginInfo loginInfo,
                                     HttpServletRequest httpRequest,
                                     @Parameter(name = "request", description = "渠道银行卡请求DTO") @RequestBody ChannelCardRequestDTO request) {
        request.setMemberId(loginInfo.getMemberId());
        ChannelCardDTO channelCardDTO = request.getChannelCardDTO();
        if (channelCardDTO != null) {
            if (!channelCardDTO.getBankAccount().matches("[0-9]*")) {
                throw new BizException(BasicCode.INVALID_PARAM, "银行卡号");
            }
        }
        String captchaKey = CAPTCHA_KEY + MD5.getMD5(BIND_CARD_CAPTCHA_KEY);
        if (!checkCaptcha(captchaKey, request.getVerifyCode(), httpRequest)) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "验证码错误");
        }
        //如果是线下支付，则先判断支付渠道是否开通，如果没有开通，则先开通（如果有记录，但是是关闭状态，则修改状态即可）
        String channelCode = request.getChannelCode();
        if (ChannelCodeEnum.OFFLINE.getCode().equals(channelCode)) {
            List<MemberChannelDTO> dtos = memberChannelService.findByMemberIdAndChannelCode(loginInfo.getMemberId(), ChannelCodeEnum.OFFLINE.getCode());
            if (CollectionUtils.isEmpty(dtos)) {
                MemberChannelDTO dto = new MemberChannelDTO();
                dto.setMemberId(loginInfo.getMemberId());
                dto.setMemberCode(loginInfo.getMemberCode());
                dto.setMemberName(loginInfo.getMemberName());
                dto.setChannelCode(ChannelCodeEnum.OFFLINE.getCode());
                dto.setMemberChannelStatus(MemberChannelStatusEnum.BE_STOP.getCode());
                MemberChannelResponseDTO responseDTO = memberChannelService.openMemberChannel(dto, ChannelPaymentTypeEnum.PAYEE.getCode(), loginInfo.getAccountId());
                MemberChannelDTO memberChannelDTO = responseDTO.getMemberChannelDTO();
                if (memberChannelDTO == null) {
                    throw new BizException(BasicCode.UNDEFINED_ERROR, "预开通线下支付方式失败");
                }
                request.setMemberChannelId(memberChannelDTO.getMemberChannelId());
            } else {
                request.setMemberChannelId(dtos.get(0).getMemberChannelId());
            }
        }
        return new ItemResult<>(memberChannelService.doAddChannelCard(request, loginInfo.getAccountId()));
    }

    @Operation(summary = "根据获取银行卡")
    @GetMapping(value = "/getChannelCard")
    public ItemResult<List<ChannelCardDTO>> getChannelCard(@Parameter(hidden = true) LoginInfo loginInfo,
                                                           @Parameter(name = "memberChannelId", description = "会员渠道id") @RequestParam(required = false) String memberChannelId,
                                                           @Parameter(name = "channelCode", description = "渠道编号") @RequestParam(required = false) String channelCode) {
        if (CsStringUtils.isBlank(memberChannelId) && CsStringUtils.isNotBlank(channelCode)) {
            return new ItemResult<>(memberChannelService.getChannelCardByMemberIdAndChannelCode(loginInfo.getMemberId(), channelCode));
        }
        if (CsStringUtils.isBlank(memberChannelId) && CsStringUtils.isBlank(channelCode)) {
            log.warn("memberChannelId、channelCode不能同时为空");
            return new ItemResult<>(Lists.newArrayList());
        }
        return new ItemResult<>(memberChannelService.getChannelCard(memberChannelId));
    }

    @Operation(summary = "获取银行卡")
    @GetMapping(value = "/getChannelCardById")
    public ItemResult<Object> getChannelCardById(@Parameter(name = "channelCardId", description = "渠道银行卡id") @RequestParam String channelCardId) {
        return new ItemResult<>(memberChannelService.getChannelCardById(channelCardId));
    }

    @Operation(summary = "设置支付密码")
    @PostMapping(value = "/setPassword")
    public ItemResult<Object> setPassword(@Parameter(hidden = true) LoginInfo loginInfo,
                                  @Parameter(name = "passwordRequest", description = "密码请求DTO") @RequestBody PasswordRequestDTO passwordRequest) {
        passwordRequest.setOperator(loginInfo.getAccountCode());
        passwordRequest.setMemberId(loginInfo.getMemberId());
        PasswordResponseDTO dto = memberChannelService.setPassword(passwordRequest);
        return new ItemResult<>(dto);
    }


    @Operation(summary = "验证银行卡")
    @PostMapping(value = "/verifyChannelCard")
    public ItemResult<Object> verifyChannelCard(@Parameter(hidden = true) LoginInfo loginInfo,
                                        @Parameter(name = "request", description = "渠道银行卡请求DTO") @RequestBody ChannelCardRequestDTO request) {
        request.setOperator(loginInfo.getAccountCode());
        request.setMemberId(loginInfo.getMemberId());
        ChannelCardResponseDTO dto = memberChannelService.verifyChannelCard(request, loginInfo.getAccountId());
        return new ItemResult<>(dto);
    }

    @Operation(summary = "移除银行卡")
    @GetMapping(value = "/removeChannelCard")
    public ItemResult<Object> removeChannelCard(@Parameter(hidden = true) LoginInfo loginInfo,
                                        @Parameter(name = "channelCardId", description = "渠道银行卡id") @RequestParam String channelCardId) {
        ChannelCardResponseDTO dto = memberChannelService.doRemoveChannelCard(channelCardId, loginInfo.getMemberId(), loginInfo.getAccountId());
        return new ItemResult<>(dto);
    }

    @Operation(summary = "查询渠道定制化信息")
    @GetMapping(value = "/getMemberChannelInfoFromThird")
    public ItemResult<Object> getMemberChannelInfoFromThird(@Parameter(name = "memberChannelId", description = "会员渠道id") @RequestParam String memberChannelId,
                                                    HttpServletResponse response) {
        response.setHeader(CACHE_CONTROL, NO_CACHE);
        return new ItemResult<>(memberChannelService.getMemberChannelInfoFromThird(memberChannelId));
    }

    @Operation(summary = "查询渠道定制化信息")
    @PostMapping(value = "/getMemberChannelInfoFromThirdByQueryDTO")
    public ItemResult<Object> getMemberChannelInfoFromThirdByQueryDTO(@Parameter(hidden = true) LoginInfo loginInfo, @Parameter(name = "balanceQueryDTO", description = "余额查询DTO") @RequestBody BalanceQueryDTO balanceQueryDTO,
                                                              HttpServletResponse response) {
        response.setHeader(CACHE_CONTROL, NO_CACHE);
        if (!CsStringUtils.isEmpty(balanceQueryDTO.getWarehouse())) {
            WarehouseDetailsDTO detailsDTO = warehouseService.queryWarehouseDetails(balanceQueryDTO.getWarehouse());
            balanceQueryDTO.setWarehouse(detailsDTO == null ? "" : detailsDTO.getErpCode());
            balanceQueryDTO.setPickupPointOrgId(detailsDTO == null ? "" : detailsDTO.getErpOrgCode());
        }
        balanceQueryDTO.setPayeeMemberId(loginInfo.getMemberId());
        return new ItemResult<>(memberChannelService.getMemberChannelInfoFromThirdByQueryDTO(balanceQueryDTO));
    }

    @Operation(summary = "提现")
    @PostMapping(value = "/cashWithdrawal")
    public ItemResult<Object> cashWithdrawal(@Parameter(hidden = true) LoginInfo loginInfo,
                                     @Parameter(name = "cashWithdrawalDTO", description = "提现DTO") @RequestBody CashWithdrawalDTO cashWithdrawalDTO) {
        cashWithdrawalDTO.setOperatorId(loginInfo.getAccountId());
        PasswordResponseDTO dto = walletAccountService.cashWithdrawal(cashWithdrawalDTO);
        return new ItemResult<>(dto);
    }

    @Operation(summary = "发送交易验证短信验证码")
    @PostMapping(value = "/sendVerificationCode")
    public ItemResult<SendVerificationCodeResponseDTO> sendVerificationCode(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody SendVerificationCodeRequestDTO requestDTO) {
        requestDTO.setMemberId(loginInfo.getMemberId());
        return new ItemResult<>(paymentService.sendVerificationCode(requestDTO));
    }

    @Operation(summary = "回填验证码")
    @PostMapping(value = "/verificationCode")
    public ItemResult<Boolean> verificationCode(@Parameter(hidden = true) LoginInfo loginInfo,@RequestBody SendVerificationCodeRequestDTO requestDTO) {
        requestDTO.setMemberId(loginInfo.getMemberId());
        return new ItemResult<>(paymentService.verificationCode(requestDTO));
    }

    @Operation(summary = "充值")
    @PostMapping(value = "/recharge")
    public ItemResult<Object> recharge(@Parameter(hidden = true) LoginInfo loginInfo,
                               @Parameter(name = "rechargeRequestDTO", description = "充值请求DTO") @RequestBody RechargeRequestDTO rechargeRequestDTO) {
        rechargeRequestDTO.setOperator(loginInfo.getAccountId());
        rechargeRequestDTO.setChannelPaymentType(ChannelPaymentTypeEnum.PAYEE.getCode());
        rechargeRequestDTO.setMemberId(loginInfo.getMemberId());
        RechargeResponseDTO dto = walletAccountService.recharge(rechargeRequestDTO);
        return new ItemResult<>(dto);
    }


    @Operation(summary = "查询支付单状态")
    @GetMapping(value = "/getPaymentBillStatus")
    public ItemResult<Object> getPaymentBillStatus(@Parameter(name = "paymentBillNo", description = "支付单编号") @RequestParam String paymentBillNo) {
        return redisService.get(PayRedisKey.PAY_BILL + paymentBillNo, ItemResult.class);
    }

    /**
     * 获取会员支付渠道信息
     */
    @Operation(summary = "获取会员支付渠道信息")
    @GetMapping("/getMemberChannelInfo")
    public ItemResult<Object> getMemberChannelInfo(@Parameter(name = "memberChannelId", description = "会员渠道id") @RequestParam String memberChannelId) {
        return new ItemResult<>(memberChannelService.getMemberChannelInfo(memberChannelId));
    }

    @Operation(summary = "分页查询该卖家下开通授信额度的会员")
    @PostMapping("/pageMemberChannel")
    public ItemResult<Object> pageMemberChannel(LoginInfo loginInfo,
                                        @Parameter(name = "memberChannelDTO", description = "会员渠道DTO") @RequestBody MemberChannelDTO memberChannelDTO) {
        memberChannelDTO.setMemberName(null);
        memberChannelDTO.setPayeeMemberId(loginInfo.getMemberId());
        memberChannelDTO.setChannelCode(ChannelCodeEnum.CREDIT.getCode());
        PageInfo<MemberChannelDTO> pageInfo = memberChannelService.pageMemberChannel(memberChannelDTO);
        if(pageInfo != null && CollectionUtils.isNotEmpty(pageInfo.getList())){
            List<MemberCreditContextDTO> list = memberCreditService.findMemberCreditContext(BeanConvertUtils.convertList(pageInfo.getList(), MemberCreditQueryDTO.class));
            if(CollectionUtils.isNotEmpty(list)){
                Map<String, MemberCreditContextDTO> map = list.stream().collect(Collectors.toMap(item -> item.getPayeeMemberId() + "-" + item.getPayerMemberId(), Function.identity(), (k1, k2) -> k2));
                pageInfo.getList().forEach(item->{
                    if(item.getEffectiveBeginDate() == null)
                    {
                        MemberCreditContextDTO contextDTO = map.get(item.getPayeeMemberId() + "-" + item.getMemberId());
                        MemberCreditDTO memberCreditDTO = contextDTO == null ? null : contextDTO.getNextMemberCreditDTO();
                        memberCreditDTO = memberCreditDTO == null ? contextDTO.getPreviousMemberCreditDTO() : memberCreditDTO;
                        if (memberCreditDTO != null)
                        {
                            item.setEffectiveBeginDate(memberCreditDTO.getStartDate());
                            item.setEffectiveEndDate(memberCreditDTO.getEndDate());
                            item.setBalanceAmount(memberCreditDTO.getCreditAmount());
                        }
                    }
                });
            }
        }
        return new ItemResult<>(pageInfo);
    }

    @Operation(summary = "获取会员支付流水")
    @PostMapping("/pageMemberChannelBill")
    public ItemResult<Object> pageMemberChannelBill(@Parameter(hidden = true) LoginInfo loginInfo,
                                            @Parameter(name = "queryDTO", description = "交易日志分页查询对象") @RequestBody PageQuery<BillLogsDTO> queryDTO) {
        if (queryDTO.getQueryDTO() == null) {
            queryDTO.setQueryDTO(new BillLogsDTO());
        }
        queryDTO.getQueryDTO().setMemberId(loginInfo.getMemberId());
        if (CsStringUtils.isBlank(queryDTO.getQueryDTO().getQueryMemberType())) {
            queryDTO.getQueryDTO().setQueryMemberType(MemberTypeEnum.ENTERPRISE_SELLER.getCode());
        }
        return new ItemResult<>(billLogsService.pageBillLogs(queryDTO));
    }

    @Operation(summary = "下载会员支付流水")
    @PostMapping("/downloadMemberChannelBill")
    public void downloadBillLogs(@Parameter(hidden = true) LoginInfo loginInfo,
                                 @Parameter(name = "queryDTO", description = "交易日志分页查询对象") @RequestBody PageQuery<BillLogsDTO> queryDTO,
                                 @Parameter(hidden = true) HttpServletRequest request,
                                 @Parameter(hidden = true) HttpServletResponse response) {
        if (queryDTO.getQueryDTO() == null) {
            queryDTO.setQueryDTO(new BillLogsDTO());
        }
        queryDTO.getQueryDTO().setMemberId(loginInfo.getMemberId());
        if (CsStringUtils.isBlank(queryDTO.getQueryDTO().getQueryMemberType())) {
            queryDTO.getQueryDTO().setQueryMemberType(MemberTypeEnum.ENTERPRISE_SELLER.getCode());
        }
        String userAgent = request.getHeader(HttpHeaders.USER_AGENT);
        queryDTO.getQueryDTO().setUserAgent(userAgent);
        log.info("userAgent:{}", userAgent);
        InputStream inputStream = null;
        OutputStream outputStream = null;
        try {
            Response response1 = billLogsService.excelExport(queryDTO);
            inputStream = response1.body().asInputStream();
            log.info("inputStream.available():{}", inputStream.available());
            outputStream = response.getOutputStream();

            String contentDisposition = response1.headers().get(HttpHeaders.CONTENT_DISPOSITION).iterator().next();
            log.info(HttpHeaders.CONTENT_DISPOSITION + " : {}", contentDisposition);
            String fileName = contentDisposition != null && contentDisposition.contains("=") ? contentDisposition.split("=")[1] : UUID.randomUUID().toString().replaceAll("-", "") + ".xls";
            response.setCharacterEncoding("utf-8");
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename=" + new String(fileName.getBytes(), "ISO8859-1"));
            response.setHeader(HttpHeaders.CONTENT_TYPE, "application/vnd.ms-excel;charset=utf-8");
            response.setHeader(HttpHeaders.CACHE_CONTROL, NO_CACHE);
            response.setHeader(HttpHeaders.ACCESS_CONTROL_EXPOSE_HEADERS, "Content-disposition");
            response.setHeader(HttpHeaders.PRAGMA, NO_CACHE);

            StreamKit.copy(inputStream, outputStream);
            outputStream.flush();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            StreamKit.closeQuietly(inputStream);
            StreamKit.closeQuietly(outputStream);
        }
    }

    @Operation(summary = "导出交易流水")
    @PostMapping("/exportTradingFlow")
    public void exportTradingFlow(@Parameter(hidden = true) LoginInfo loginInfo,
                                  @Parameter(name = "queryDTO", description = "交易日志对象") @RequestBody BillLogsDTO queryDTO,
                                  @Parameter(hidden = true) HttpServletResponse response) {
        if (loginInfo == null) {
            return;
        }
        queryDTO.setMemberId(loginInfo.getMemberId());
        if (CsStringUtils.isBlank(queryDTO.getQueryMemberType())) {
            queryDTO.setQueryMemberType(MemberTypeEnum.ENTERPRISE_SELLER.getCode());
        }
        log.info("queryDTO : {}", queryDTO);
        List<TradingFlowExportDTO> result = billLogsService.exportTradingFlow(queryDTO);
        if (result != null) {
            TradingFlowExcelUtil.exportExcel(result, response, ChannelCodeEnum.CREDIT.getCode().equals(queryDTO.getChannelCode()));
        }
    }

    @Operation(summary = "获取支付流水的支付渠道信息")
    @GetMapping("/getOrderPayType")
    public ItemResult<Object> getOrderPayType() {
        List<KeyValueDTO> collect = Stream.of(PayDetailTypeEnum.values())
                .map(i -> {
                    KeyValueDTO keyValueDTO = new KeyValueDTO();
                    keyValueDTO.setKey(i.getCode());
                    keyValueDTO.setValue(i.getMessage());
                    return keyValueDTO;
                })
                .toList();
        return new ItemResult<>(collect);
    }

    @Operation(summary = "获取支付流水的支付渠道信息2")
    @GetMapping("/findMemberPayChannel")
    public ItemResult<Object> findMemberPayChannel(LoginInfo loginInfo) {
        Set<String> channels = billLogsService.findMemberPayChannelForQuery(loginInfo.getMemberId(), 0);

        List<MemberPayChannelDTO> res = new ArrayList<>();
        for(ChannelCodeEnum _enum : ChannelCodeEnum.values())
        {
            if(channels.contains(_enum.getCode()) == false)
            {
                continue;
            }
            res.add(new MemberPayChannelDTO(_enum.getCode(), _enum.getMessage()));
        }

        return new ItemResult<>(res);
    }

    @Operation(summary = "获取支付流水的支付渠道信息")
    @GetMapping("/findMemberChannelByLogs")
    public ItemResult<Object> findMemberChannelByLogs(LoginInfo loginInfo) {
        List<MemberChannelDTO> list = memberChannelService.findMemberChannelListByPlatform(loginInfo.getMemberId());
        List<PaymentChannelDTO> res = new ArrayList<>();
        if (list != null && !list.isEmpty()) {
            for (MemberChannelDTO memberChannelDTO : list) {
                PaymentChannelDTO paymentChannelDTO = new PaymentChannelDTO();
                BeanUtils.copyProperties(memberChannelDTO, paymentChannelDTO);
                paymentChannelDTO.setOpenTime(memberChannelDTO.getCreateTime());
                res.add(paymentChannelDTO);
            }
        }
        return new ItemResult<>(res);
    }

    @Operation(summary = "设置默认银行卡")
    @PostMapping("/updateDefaultChannelCard")
    public ItemResult<Object> updateDefaultChannelCard(LoginInfo loginInfo,
                                               @Parameter(name = "memberChannelId", description = "会员渠道id") @RequestParam String memberChannelId,
                                               @Parameter(name = "channelCardId", description = "渠道银行卡id") @RequestParam String channelCardId) {
        memberChannelService.updateDefaultChannelCard(memberChannelId, channelCardId, loginInfo.getAccountId());
        return new ItemResult<>(true);
    }

    /**
     * 获取会员的所有支付渠道信息
     */
    @Operation(summary = "获取会员的所有支付渠道信息")
    @GetMapping("/findMemberChannelListByPlatform")
    public ItemResult<Object> findMemberChannelListByPlatform(LoginInfo loginInfo) {
        List<ChannelConfigDTO> channels = memberChannelService.getPlatformAvailChannels();
        // 会员拥有的支付渠道
        List<MemberChannelDTO> list = memberChannelService.findMemberChannelListByPlatform(loginInfo.getMemberId());

        if (CollectionUtils.isEmpty(list)) {
            list = new ArrayList<>();
        }

        List<MemberChannelDTO> finalList = list;
        List<PayChannelDTO> collect = channels.stream()
                .filter(ChannelConfigDTO::getNeedPayeeReg)
                .flatMap(item -> {
                    PayChannelDTO payChannelDTO = new PayChannelDTO();
                    BeanUtils.copyProperties(item, payChannelDTO);
                    payChannelDTO.setMemberChannelStatus(MemberChannelStatusEnum.BE_STOP.getCode());
                    Optional<MemberChannelDTO> optional = finalList.stream()
                            .filter(i -> i.getChannelId().equals(item.getChannelId()) && i.getAllowReceive()).findFirst();
                    if (optional.isPresent()) {
                        MemberChannelDTO memberChannelDTO = optional.get();
                        BeanUtils.copyProperties(memberChannelDTO, payChannelDTO);
                    }
                    if (ChannelCodeEnum.PINGANJZ.getCode().equals(item.getChannelCode())) {
                        PayChannelDTO t = new PayChannelDTO();
                        BeanUtils.copyProperties(payChannelDTO, t);
                        t.setChannelName("聚合支付");
                        channels.stream()
                                .filter(i -> i.getReceiveChannelId().equals(item.getChannelId()) &&
                                        !ChannelCodeEnum.PINGANJZPAY.getCode().equals(i.getChannelCode()))
                                .findFirst().ifPresent(i -> {
                            t.setChannelLogo(i.getChannelLogo());
                            t.setChannelInfo(i.getChannelInfo());
                            t.setChannelSequence(i.getChannelSequence());
                        });
                        if (optional.isPresent() && AggregationPayStatusEnum.SUPPORT_AGGREGATION.getCode().equals(optional.get().getExt2())) {
                            t.setMemberChannelStatus(MemberChannelStatusEnum.OPEND.getCode());
                        } else {
                            t.setMemberChannelStatus(MemberChannelStatusEnum.BE_STOP.getCode());
                        }
                        String aggs = "aggs";
                        t.setChannelCode(aggs);
                        t.setChannelType(aggs);
                        return Stream.of(payChannelDTO, t);
                    }
                    return Stream.of(payChannelDTO);
                }).sorted(Comparator.comparingInt(PayChannelDTO::getChannelSequence)).toList();

        log.info("获取会员的所有支付渠道 {}", JSON.toJSONString(collect));
        return new ItemResult<>(collect);
    }

    @Operation(summary = "分页查询该卖家下开通授信额度的会员")
    @PostMapping("/pageCreditMemberChannel")
    public ItemResult<Object> pageCreditMemberChannel(LoginInfo loginInfo,
                                              @Parameter(name = "memberChannelDTO", description = "会员渠道DTO") @RequestBody MemberChannelDTO memberChannelDTO) {
        memberChannelDTO.setPayeeMemberId(loginInfo.getMemberId());
        memberChannelDTO.setChannelCode(ChannelCodeEnum.CREDIT.getCode());
        PageInfo<MemberChannelDTO> pageInfo = memberChannelService.pageMemberChannel(memberChannelDTO);
        return new ItemResult<>(pageInfo);
    }

    @Operation(summary = "分页查询该卖家下开通授信额度的会员")
    @PostMapping("/pageErpMemberChannel")
    public ItemResult<Object> pageErpMemberChannel(LoginInfo loginInfo,
                                           @Parameter(name = "memberChannelDTO", description = "会员渠道DTO") @RequestBody MemberChannelDTO memberChannelDTO) {
        memberChannelDTO.setPayeeMemberId(loginInfo.getMemberId());
        memberChannelDTO.setChannelCode(ChannelCodeEnum.ERP.getCode());
        PageInfo<MemberChannelDTO> pageInfo = memberChannelService.pageMemberChannel(memberChannelDTO);
        return new ItemResult<>(pageInfo);
    }


    @Operation(summary = "获取余额支付流水")
    @PostMapping("/pageErpMemberChannelBill")
    public ItemResult<Object> pageErpMemberChannelBill(LoginInfo loginInfo,
                                               @Parameter(name = "queryDTO", description = "交易日志分页查询对象") @RequestBody PageQuery<BillLogsDTO> queryDTO) {
        if (queryDTO.getQueryDTO() == null) {
            queryDTO.setQueryDTO(new BillLogsDTO());
        }
        queryDTO.getQueryDTO().setMemberId(loginInfo.getMemberId());
        queryDTO.getQueryDTO().setChannelCode(ChannelCodeEnum.ERP.getCode());
        if (CsStringUtils.isBlank(queryDTO.getQueryDTO().getQueryMemberType())) {
            queryDTO.getQueryDTO().setQueryMemberType(MemberTypeEnum.ENTERPRISE_SELLER.getCode());
        }
        return new ItemResult<>(billLogsService.pageBillLogs(queryDTO));
    }

    @Operation(summary = "设置聚合支付")
    @PostMapping("/aggregationPaySetting")
    public ItemResult<Object> aggregationPaySetting(LoginInfo loginInfo,
                                            @Parameter(name = "memberChannelId", description = "会员渠道id") @RequestParam String memberChannelId,
                                            @Parameter(name = "support", description = "是否支持") @RequestParam boolean support) {
        PasswordResponseDTO dto = memberChannelService.aggregationPaySetting(memberChannelId, support, loginInfo.getAccountId());
        return new ItemResult<>(dto);
    }

    @Operation(summary = "设置小额免密")
    @PostMapping("/freePasswordSetting")
    public ItemResult<Object> freePasswordSetting(LoginInfo loginInfo,
                                          @Parameter(name = "memberChannelId", description = "会员渠道id") @RequestParam String memberChannelId,
                                          @Parameter(name = "support", description = "是否支持") @RequestParam boolean support) {
        PasswordResponseDTO dto = memberChannelService.freePasswordSetting(memberChannelId, support, loginInfo.getAccountId());
        return new ItemResult<>(dto);
    }

    @Operation(summary = "卖家调整会员的授信额度或者还款")
    @PostMapping("/updateCreditMemberChannel")
    public ItemResult<Boolean> updateCreditMemberChannel(LoginInfo loginInfo,
                                                         @Parameter(name = "memberChannelDTO", description = "会员渠道DTO") @RequestBody MemberChannelDTO memberChannelDTO) {
        String operator = loginInfo.getAccountId();
        memberChannelService.updateCreditMemberChannel(memberChannelDTO, operator);
        return new ItemResult<>(Boolean.TRUE);
    }

    @Operation(summary = "获取授信支付流水")
    @PostMapping("/pageCreditMemberChannelBill")
    public ItemResult<Object> pageCreditMemberChannelBill(LoginInfo loginInfo,
                                                  @Parameter(name = "queryDTO", description = "交易日志分页查询对象") @RequestBody PageQuery<BillLogsDTO> queryDTO) {
        if (queryDTO.getQueryDTO() == null) {
            queryDTO.setQueryDTO(new BillLogsDTO());
        }
        queryDTO.getQueryDTO().setMemberId(loginInfo.getMemberId());
        queryDTO.getQueryDTO().setChannelCode(ChannelCodeEnum.CREDIT.getCode());
        if (CsStringUtils.isBlank(queryDTO.getQueryDTO().getQueryMemberType())) {
            queryDTO.getQueryDTO().setQueryMemberType(MemberTypeEnum.ENTERPRISE_SELLER.getCode());
        }
        return new ItemResult<>(billLogsService.pageBillLogs(queryDTO));
    }

    @Operation(summary = "分页查询跟卖家有关系的会员")
    @PostMapping("/pageMemberRelation")
    public ItemResult<Object> pageMemberRelation(@Parameter(hidden = true) LoginInfo loginInfo,
                                         @Parameter(name = "queryDTO", description = "会员关系查询DTO") @RequestBody MemberRelationQueryDTO queryDTO) {
        int page = 1;
        int pageSize = 20;
        List<MemberRelationV2DTO> result = new ArrayList<>();
        List<MemberRelationDTO> moreResult;
        queryDTO.setMemberId(loginInfo.getMemberId());
        do {
            moreResult = getMoreMemberRelation(queryDTO, loginInfo.getMemberId(), page, pageSize);

            if (moreResult != null) {
                List<MemberRelationV2DTO> dtos = moreResult.stream().map(dto -> {

                    MemberRelationV2DTO v2DTO = new MemberRelationV2DTO();
                    BeanUtils.copyProperties(dto, v2DTO);
                    v2DTO.setId("");

                    return v2DTO;
                }).toList();

                result.addAll(dtos);
            }
            page++;
        } while (moreResult != null);

        return new ItemResult<>(result);
    }

    /**
     * 获取会员关系中没有授过信的会员
     *
     * @param queryDTO
     * @param sellerMemberId
     * @param page
     * @param pageSize
     * @return
     */
    private List<MemberRelationDTO> getMoreMemberRelation(MemberRelationQueryDTO queryDTO, String sellerMemberId, int page, int pageSize) {

        PageInfo<MemberRelationDTO> pageInfo = memberRelationService.pageRelationMember(queryDTO, page, pageSize);

        log.info("pageInfo: {}", JSON.toJSONString(pageInfo));

        if (CollectionUtils.isEmpty(pageInfo.getList())) {
            return null;
        }
        List<String> payerMemberIds = pageInfo.getList().stream().map(MemberRelationDTO::getCustomerId).toList();
        // 查询渠道
        List<MemberChannelDTO> memberChannels = this.memberChannelService.findMemberChannelDTOList(sellerMemberId,
                payerMemberIds);

        log.info("memberChannels: {}", JSON.toJSONString(memberChannels));

        List<MemberRelationDTO> result = pageInfo.getList();
        if (CollectionUtils.isNotEmpty(memberChannels)) {
            result = pageInfo.getList().stream()
                    .filter(item -> memberChannels.stream().noneMatch(i -> i.getMemberId().equals(item.getCustomerId())))
                    .toList();
        }

        return result;
    }

    @Operation(summary = "批量新增授信渠道")
    @PostMapping("/batchAddCreditMemberChannel")
    public ItemResult<Boolean> batchAddCreditMemberChannel(LoginInfo loginInfo,
                                                           @Parameter(name = "creditMemberChannelDTOS", description = "授信渠道DTO列表") @RequestBody List<CreditMemberChannelDTO> creditMemberChannelDTOS) {
        String operator = loginInfo.getAccountId();
        String sellerId = loginInfo.getMemberId();
        String sellerCode = loginInfo.getMemberCode();
        String sellerName = loginInfo.getMemberName();
        memberChannelService.batchAddCreditMemberChannel(creditMemberChannelDTOS, sellerId, sellerCode, sellerName,
                operator);
        return new ItemResult<>(Boolean.TRUE);
    }

    @GetMapping("/getCaptchaWithBindCard")
    public ItemResult<Object> getCaptchaWithBindCard(HttpServletRequest request) {
        String code = producer.createText();
        String captcha = getCaptcha(code);
        String key = CAPTCHA_KEY + MD5.getMD5(BIND_CARD_CAPTCHA_KEY);
        request.getSession().setAttribute(key, code);
        log.info("===>获取到的图形验证码：sessionId: {},key: {},code: {}", request.getSession().getId(), key, code);

        return new ItemResult<>(captcha);
    }

    @Operation(summary = "添加对公银行账户(查询小额鉴权转账结果)")
    @PostMapping(value = "/findSmallAmountTransfer")
    public ItemResult<SmallAmountTransferDTO> findSmallAmountTransfer(LoginInfo loginInfo,@RequestBody SmallAmountTransferDTO dto) {
        dto.setMemberId(loginInfo.getMemberId());
        return new ItemResult<>(memberChannelService.findSmallAmountTransfer(dto));
    }

    /**
     * 开通支付渠道 type取值范围
     * @see ApplyTicketJumpTypeEnum
     */
    @Operation(summary = "银联支付操作调用(type取值范围: [1~11])")
    @GetMapping("/getTicket")
    public ItemResult<String> getTicket(@Parameter(hidden = true) LoginInfo loginInfo,
                                        @Parameter(name = "type", description = "跳转类型") @RequestParam Integer type,
                                        @Parameter(hidden = true) HttpServletRequest request) {
        if( type == null || type < 1 || type > 11 ){
            throw new BizException(BasicCode.CUSTOM_ERROR,"type不正确");
        }
        ApplyTicketReqDTO dto = new ApplyTicketReqDTO();
        dto.setAppName(AppNames.WEB_SERVICE_SELLER.getPlatform());
        dto.setMemberId(loginInfo.getMemberId());
        dto.setMemberName(loginInfo.getMemberName());
        dto.setMemberCode(loginInfo.getMemberCode());
        dto.setMemberType(memberService.findMemberSimpleById(loginInfo.getMemberId()).getMemberType());
        dto.setSellerFlg( !MemberPlatform.PLATFORM_MEMBERID.getId().equals(loginInfo.getMemberId())  && loginInfo.getSellerFlg()!= null && loginInfo.getSellerFlg() ==1 );
        dto.setOperator(loginInfo.getAccountId());
        dto.setJumpType(type);
        dto.setClientIp(IPUtils.getClientIpAddr(request));
        ApplyTicketResDTO applyTicketResDTO = memberChannelService.getTicket(dto);
        if (CsStringUtils.isNotBlank(applyTicketResDTO.getErrorMessage())) {
            throw new BizException(BasicCode.CUSTOM_ERROR,applyTicketResDTO.getErrorMessage());
        }
        return new ItemResult<>(applyTicketResDTO.getUrl());
    }

    @Operation(summary = "银联支付-收款-签约")
    @PostMapping("/withholdGrant")
    public ItemResult<GneteWithholdGrantQueryDTO> withholdGrant(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody GneteWithholdGrantDTO dto){
        dto.setOprtType(1);
        dto.setMemberId(loginInfo.getMemberId());
        dto.setOperator(loginInfo.getAccountId());
        return new ItemResult<>(memberChannelService.withholdGrant(dto));
    }

    @Operation(summary = "银联支付-收款-解约")
    @PostMapping("/cancelGrant")
    public ItemResult<Boolean> cancelGrant(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody GneteWithholdGrantDTO dto){
        dto.setOprtType(2);
        dto.setMemberId(loginInfo.getMemberId());
        dto.setOperator(loginInfo.getAccountId());
        memberChannelService.withholdGrant(dto);
        return new ItemResult<>(true);
    }

    @Operation(summary = "银联支付-收款-签约详情")
    @PostMapping("/withholdGrantQuery")
    public ItemResult<GneteWithholdGrantQueryDTO> withholdGrantQuery(@Parameter(hidden = true) LoginInfo loginInfo){
        GneteWithholdGrantDTO dto = new GneteWithholdGrantDTO();
        dto.setOprtType(3);
        dto.setMemberId(loginInfo.getMemberId());
        dto.setOperator(loginInfo.getAccountId());
        return new ItemResult<>(memberChannelService.withholdGrant(dto));
    }

    @Operation(summary = "银联支付-开通聚合支付渠道")
    @PostMapping("/openGneteAggregationChannel")
    public ItemResult<Boolean> openGneteAggregationChannel(@Parameter(hidden = true) LoginInfo loginInfo){
        GneteAggregationChannelOpenDTO dto = new GneteAggregationChannelOpenDTO();
        dto.setMemberId(loginInfo.getMemberId());
        dto.setOperator(loginInfo.getAccountId());
        return new ItemResult<>(memberChannelService.openGneteAggregationChannel(dto));
    }

    @Operation(summary = "银联支付-关闭-聚合支付渠道")
    @PostMapping("/closeGneteAggregationChannel")
    public ItemResult<Boolean> closeGneteAggregationChannel(@Parameter(hidden = true) LoginInfo loginInfo){
        GneteAggregationChannelCloseDTO dto = new GneteAggregationChannelCloseDTO();
        dto.setMemberId(loginInfo.getMemberId());
        dto.setOperator(loginInfo.getAccountId());
        return new ItemResult<>(memberChannelService.closeGneteAggregationChannel(dto));
    }

    private String getCaptcha(String code) {
        try {
            BufferedImage bi = producer.createImage(code);
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(bi, "jpg", baos);

            return Base64.getEncoder().encodeToString(baos.toByteArray());
        } catch (IOException e) {
            throw new BizException(BasicCode.UNKNOWN_ERROR, "校验码生成错误");
        }
    }

    private boolean checkCaptcha(String key, String captchaCode, HttpServletRequest request) {
        if (CsStringUtils.isBlank(captchaCode)) {
            log.info("图形验证码为空");
            return false;
        }
        Object objValue = request.getSession().getAttribute(key);
        log.info("===>校验图形验证码，获取到的图形验证码：sessionId: {},key: {},code: {}",
                request.getSession().getId(), key, objValue);
        return captchaCode.equalsIgnoreCase(String.valueOf(objValue));
    }
}
