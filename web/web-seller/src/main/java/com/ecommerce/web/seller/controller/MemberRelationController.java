package com.ecommerce.web.seller.controller;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.member.api.dto.base.PageQuery;
import com.ecommerce.member.api.dto.relation.ContractBatchAdjustPriceFlgUpdateDTO;
import com.ecommerce.member.api.dto.relation.CustomerDetailDTO;
import com.ecommerce.member.api.dto.relation.ErpCustomCondDTO;
import com.ecommerce.member.api.dto.relation.ErpCustomInfoDTO;
import com.ecommerce.member.api.dto.relation.ErpCustomRelationGroupDTO;
import com.ecommerce.member.api.dto.relation.ErpCustomerListDTO;
import com.ecommerce.member.api.dto.relation.ErpCustomerQueryDTO;
import com.ecommerce.member.api.dto.relation.ErpMultiCustomerInfoDTO;
import com.ecommerce.member.api.dto.relation.ErpRelationMdmCodeDTO;
import com.ecommerce.member.api.dto.relation.MemberBlacklistDTO;
import com.ecommerce.member.api.dto.relation.MemberRelationAddDTO;
import com.ecommerce.member.api.dto.relation.MemberRelationDTO;
import com.ecommerce.member.api.dto.relation.MemberRelationInvoiceTypeUpdateDTO;
import com.ecommerce.member.api.dto.relation.MemberRelationQueryDTO;
import com.ecommerce.member.api.dto.relation.MemberTagDTO;
import com.ecommerce.member.api.dto.relation.MemberTagGroupDTO;
import com.ecommerce.member.api.service.IMemberRelationService;
import com.ecommerce.member.api.session.LoginInfo;
import com.ecommerce.web.seller.dto.relation.MemberBlacklistQueryPageDTO;
import com.ecommerce.web.seller.dto.relation.MemberTagCreateDTO;
import com.ecommerce.web.seller.dto.relation.MemberTagGroupQueryDTO;
import com.ecommerce.web.seller.dto.relation.MemberTagGroupUpdateDTO;
import com.ecommerce.web.seller.dto.relation.MemberTagUpdateDTO;
import com.ecommerce.web.seller.util.MemberRelationExcelUtil;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * TODO
 *
 * <AUTHOR>
 */
@RestController
@Tag(name = "MemberRelationController", description = "会员关系")
@RequestMapping("/relation")
public class MemberRelationController {

    @Autowired
    private IMemberRelationService iMemberRelationService;

    @Operation(summary = "创建标签")
    @PostMapping(value = "/createMemberTag")
    public ItemResult<Object> createMemberTag(@Parameter(hidden = true) LoginInfo loginInfo,
                                      @Parameter(name = "memberTagCreateDTO", description = "会员标签创建DTO") @Valid @RequestBody MemberTagCreateDTO memberTagCreateDTO) {
        MemberTagDTO memberTagDTO = new MemberTagDTO();
        BeanUtils.copyProperties(memberTagCreateDTO, memberTagDTO);
        memberTagDTO.setMemberId(loginInfo.getMemberId());
        iMemberRelationService.createMemberTag(memberTagDTO, loginInfo.getAccountId());
        return new ItemResult<>(new Object());
    }


    @Operation(summary = "删除标签")
    @PostMapping(value = "/deleteMemberTag")
    public ItemResult<Object> deleteMemberTag(@Parameter(hidden = true) LoginInfo loginInfo,
                                      @Parameter(name = "memberTagId", description = "会员标签id") @RequestParam String memberTagId) {
        iMemberRelationService.deleteMemberTag(memberTagId, loginInfo.getAccountId());
        return new ItemResult<>(new Object());
    }


    @Operation(summary = "修改标签信息")
    @PostMapping(value = "/updateMemberTag")
    public ItemResult<Object> updateMemberTag(@Parameter(hidden = true) LoginInfo loginInfo,
                                      @Parameter(name = "memberTagUpdateDTO", description = "会员标签更新DTO") @RequestBody MemberTagUpdateDTO memberTagUpdateDTO) {
        MemberTagDTO memberTagDTO = new MemberTagDTO();
        BeanUtils.copyProperties(memberTagUpdateDTO, memberTagDTO);
        memberTagDTO.setMemberId(loginInfo.getMemberId());
        iMemberRelationService.updateMemberTag(memberTagDTO, loginInfo.getAccountId());
        return new ItemResult<>(new Object());
    }


    @Operation(summary = "查询标签组")
    @PostMapping(value = "/findMemberGroup")
    public ItemResult<List<MemberTagGroupDTO>> findMemberGroup(@Parameter(hidden = true) LoginInfo loginInfo,
                                                               @Parameter(name = "memberTagGroupQueryDTO", description = "会员标签组查询DTO") @RequestBody MemberTagGroupQueryDTO memberTagGroupQueryDTO) {
        MemberTagGroupDTO memberTagGroupDTO = new MemberTagGroupDTO();
        BeanUtils.copyProperties(memberTagGroupQueryDTO, memberTagGroupDTO);
        memberTagGroupDTO.setMemberId(loginInfo.getMemberId());
        List<MemberTagGroupDTO> memberGroup = iMemberRelationService.findMemberGroup(memberTagGroupDTO);
        return new ItemResult<>(memberGroup);
    }


    @Operation(summary = "查询标签组内的标签")
    @PostMapping(value = "/listMemberGroup")
    public ItemResult<List<MemberTagDTO>> listMemberGroup(@Parameter(name = "memberTagGroupId", description = "会员标签组id") @RequestParam String memberTagGroupId) {
        List<MemberTagDTO> memberTagDTOS = iMemberRelationService.listMemberGroup(memberTagGroupId);
        return new ItemResult<>(memberTagDTOS);
    }


    @Operation(summary = "查询标签详情")
    @PostMapping(value = "/getMemberTag")
    public ItemResult<MemberTagDTO> getMemberTag(@Parameter(name = "memberTagId", description = "会员标签id") @RequestParam String memberTagId) {
        MemberTagDTO memberTag = iMemberRelationService.getMemberTag(memberTagId);
        return new ItemResult<>(memberTag);
    }


    @Operation(summary = "判断两个对象是否有黑名单关系（myself，target）")
    @PostMapping(value = "/isDefriend")
    public ItemResult<Boolean> isDefriend(@Parameter(hidden = true) LoginInfo loginInfo,
                                          @Parameter(name = "customerId", description = "客户id") @RequestParam String customerId) {
        Boolean defriend = iMemberRelationService.isDefriend(loginInfo.getMemberId(), customerId);
        return new ItemResult<>(defriend);
    }


    @Operation(summary = "为客户关系添加标签")
    @PostMapping(value = "/addTagRelation")
    public ItemResult<Object> addTagRelation(@Parameter(hidden = true) LoginInfo loginInfo,
                                     @Parameter(name = "relationId", description = "关系id") @RequestParam String relationId,
                                     @Parameter(name = "tagId", description = "标签id") @RequestParam String tagId) {
        iMemberRelationService.addTagRelation(relationId, tagId, loginInfo.getAccountId());
        return new ItemResult<>(new Object());
    }


    @Operation(summary = "根据关系ID查询会员关系详情")
    @PostMapping(value = "/getMemberRelationDetailByRelationId")
    public ItemResult<MemberRelationDTO> getMemberRelationDetailByRelationId(@Parameter(name = "relationId", description = "关系id") @RequestParam String relationId) {
        MemberRelationDTO detail = iMemberRelationService.getMemberRelationDetailByRelationId(relationId);
        return new ItemResult<>(detail);
    }


    @Operation(summary = "根据会员ID和客户ID查询会员关系详情")
    @PostMapping(value = "/getMemberRelationDetailByMemberIdAndCustomerId")
    public ItemResult<MemberRelationDTO> getMemberRelationDetailByMemberIdAndCustomerId(@Parameter(hidden = true) LoginInfo loginInfo,
                                                                                        @Parameter(name = "customerId", description = "客户id") @RequestParam String customerId) {
        MemberRelationDTO detail = iMemberRelationService.getMemberRelationDetailByMemberIdAndCustomerId(loginInfo.getMemberId(), customerId);
        return new ItemResult<>(detail);
    }


    @Operation(summary = "移除客户关系")
    @PostMapping(value = "/removeMemberRelationByMemberIdAndCustomerId")
    public ItemResult<Object> removeMemberRelationByMemberIdAndCustomerId(@Parameter(hidden = true) LoginInfo loginInfo,
                                                                  @Parameter(name = "customerId", description = "客户id") @RequestParam String customerId) {
        iMemberRelationService.removeMemberRelationByMemberIdAndCustomerId(loginInfo.getMemberId(), customerId, loginInfo.getAccountId());
        return new ItemResult<>(new Object());
    }


    @Operation(summary = "查询标签组详情")
    @PostMapping(value = "/findMemberTagGroupDetail")
    public ItemResult<MemberTagGroupDTO> findMemberTagGroupDetail(@Parameter(name = "memberTagGroupId", description = "会员标签组id") @RequestParam String memberTagGroupId) {
        MemberTagGroupDTO groupDetail = iMemberRelationService.findMemberTagGroupDetail(memberTagGroupId);
        return new ItemResult<>(groupDetail);
    }


    @Operation(summary = "为客户关系移除标签")
    @PostMapping(value = "/removeTagRelation")
    public ItemResult<Object> removeTagRelation(@Parameter(name = "relationId", description = "关系id") @RequestParam String relationId,
                                        @Parameter(name = "tagId", description = "标签id") @RequestParam String tagId) {
        iMemberRelationService.removeTagRelation(relationId, tagId);
        return new ItemResult<>(new Object());
    }


    @Operation(summary = "批量得到某会员所有黑名单信息")
    @PostMapping(value = "/pageBlacklist")
    public ItemResult<PageInfo<MemberBlacklistDTO>> pageBlacklist(@Parameter(hidden = true) LoginInfo loginInfo,
                                                                  @Parameter(name = "dto", description = "会员黑名单查询分页DTO") @RequestBody MemberBlacklistQueryPageDTO dto) {
        if (dto.getPageNum() == null) {
            dto.setPageNum(1);
        }
        if (dto.getPageSize() == null) {
            dto.setPageSize(10);
        }
        dto.getMemberBlacklistQueryDTO().setMemberId(loginInfo.getMemberId());
        PageInfo<MemberBlacklistDTO> blacklist = iMemberRelationService.pageBlacklist(dto.getMemberBlacklistQueryDTO(), dto.getPageNum(), dto.getPageSize());
        return new ItemResult<>(blacklist);
    }


    @Operation(summary = "修改会员关系")
    @PostMapping(value = "/updateMemberRelation")
    public ItemResult<Object> updateMemberRelation(@Parameter(hidden = true) LoginInfo loginInfo,
                                           @Parameter(name = "memberRelationDTO", description = "会员关系DTO") @Valid @RequestBody MemberRelationDTO memberRelationDTO) {
        memberRelationDTO.setMemberId(loginInfo.getMemberId());
        iMemberRelationService.updateMemberRelation(memberRelationDTO, loginInfo.getAccountId());
        return new ItemResult<>(new Object());
    }


    @Operation(summary = "把某对象从某会员黑名单列表中删除")
    @PostMapping(value = "/removeObjFromBlacklist")
    public ItemResult<Object> removeObjFromBlacklist(@Parameter(hidden = true) LoginInfo loginInfo,
                                             @Parameter(name = "blackMemberId", description = "黑名单会员id") @RequestParam String blackMemberId,
                                             @Parameter(name = "remarks", description = "备注") @RequestParam String remarks) {
        iMemberRelationService.removeObjFromBlacklist(loginInfo.getMemberId(), blackMemberId, loginInfo.getMemberName(), remarks);
        return new ItemResult<>(new Object());
    }


    @Operation(summary = "通过交易添加会员关系")
    @PostMapping(value = "/addMemberRelationByTrade")
    public ItemResult<Object> addMemberRelationByTrade(@Parameter(hidden = true) LoginInfo loginInfo,
                                               @Parameter(name = "customerMemberId", description = "客户会员id") @RequestParam String customerMemberId) {
        iMemberRelationService.addMemberRelationByTrade(loginInfo.getMemberId(), customerMemberId);
        return new ItemResult<>(new Object());
    }


    @Operation(summary = "是否可以删除标签")
    @PostMapping(value = "/canDeleteMemberTag")
    public ItemResult<Boolean> canDeleteMemberTag(@Parameter(name = "memberTagId", description = "会员标签id") @RequestParam String memberTagId) {
        boolean b = iMemberRelationService.canDeleteMemberTag(memberTagId);
        return new ItemResult<>(b);
    }


    @Operation(summary = "由商家自己添加客户关系")
    @PostMapping(value = "/addMemberRelationBySelf")
    public ItemResult<Object> addMemberRelationBySelf(@Parameter(hidden = true) LoginInfo loginInfo,
                                              @Parameter(name = "customerMemberId", description = "客户会员id") @RequestParam String customerMemberId) {
        MemberRelationAddDTO memberRelationAddDTO = new MemberRelationAddDTO();
        memberRelationAddDTO.setMemberId(loginInfo.getMemberId());
        memberRelationAddDTO.setCustomerId(customerMemberId);
        iMemberRelationService.addMemberRelationBySelf(memberRelationAddDTO);
        return new ItemResult<>(new Object());
    }


    @Operation(summary = "管理员添加会员关系")
    @PostMapping(value = "/addMemberRelationByAdmin")
    public ItemResult<Object> addMemberRelationByAdmin(@Parameter(hidden = true) LoginInfo loginInfo,
                                               @Parameter(name = "customerMemberId", description = "客户会员id") @RequestParam String customerMemberId) {
        iMemberRelationService.addMemberRelationByAdmin(loginInfo.getMemberId(), customerMemberId, loginInfo.getAccountId());
        return new ItemResult<>(new Object());
    }


    @Operation(summary = "通过系统添加会员关系")
    @PostMapping(value = "/addMemberRelationBySystem")
    public ItemResult<Object> addMemberRelationBySystem(@Parameter(hidden = true) LoginInfo loginInfo,
                                                @Parameter(name = "customerMemberId", description = "客户会员id") @RequestParam String customerMemberId) {
        MemberRelationAddDTO memberRelationAddDTO = new MemberRelationAddDTO();
        memberRelationAddDTO.setMemberId(loginInfo.getMemberId());
        memberRelationAddDTO.setCustomerId(customerMemberId);
        iMemberRelationService.addMemberRelationBySystem(memberRelationAddDTO);
        return new ItemResult<>(new Object());
    }


    @Operation(summary = "移除客户关系")
    @PostMapping(value = "/removeMemberRelationByRelationId")
    public ItemResult<Object> removeMemberRelationByRelationId(@Parameter(hidden = true) LoginInfo loginInfo,
                                                       @Parameter(name = "relationId", description = "关系id") @RequestParam String relationId) {
        iMemberRelationService.removeMemberRelationByRelationId(relationId, loginInfo.getAccountId());
        return new ItemResult<>(new Object());
    }


    @Operation(summary = "删除标签组")
    @PostMapping(value = "/deleteMemberTagGroup")
    public ItemResult<Object> deleteMemberTagGroup(@Parameter(hidden = true) LoginInfo loginInfo,
                                           @Parameter(name = "memberTagGroupId", description = "会员标签组id") @RequestParam String memberTagGroupId) {
        iMemberRelationService.deleteMemberTagGroup(memberTagGroupId, loginInfo.getAccountId());
        return new ItemResult<>(new Object());
    }


    @Operation(summary = "修改标签组信息")
    @PostMapping(value = "/updateMemberGroup")
    public ItemResult<Object> updateMemberGroup(@Parameter(hidden = true) LoginInfo loginInfo,
                                        @Parameter(name = "memberTagGroupUpdateDTO", description = "会员标签组更新DTO") @Valid @RequestBody MemberTagGroupUpdateDTO memberTagGroupUpdateDTO) {
        MemberTagGroupDTO memberTagGroupDTO = new MemberTagGroupDTO();
        BeanUtils.copyProperties(memberTagGroupUpdateDTO, memberTagGroupDTO);
        memberTagGroupDTO.setMemberId(loginInfo.getMemberId());
        iMemberRelationService.updateMemberGroup(memberTagGroupDTO, loginInfo.getAccountId());
        return new ItemResult<>(new Object());
    }


    @Operation(summary = "增加某对象进黑名单")
    @PostMapping(value = "/addObjToBlacklist")
    public ItemResult<Object> addObjToBlacklist(@Parameter(hidden = true) LoginInfo loginInfo,
                                        @Parameter(name = "blackMemberId", description = "黑名单会员id") @RequestParam String blackMemberId,
                                        @Parameter(name = "remarks", description = "备注") @RequestParam String remarks) {
        iMemberRelationService.addObjToBlacklist(loginInfo.getMemberId(), blackMemberId, loginInfo.getMemberName(), remarks);
        return new ItemResult<>(new Object());
    }


    @Operation(summary = "创建标签组 （数量控制）")
    @PostMapping(value = "/createMemberTagGroup")
    public ItemResult<Object> createMemberTagGroup(@Parameter(hidden = true) LoginInfo loginInfo,
                                           @Parameter(name = "memberTagGroupDTO", description = "会员标签组DTO") @RequestBody MemberTagGroupDTO memberTagGroupDTO) {
        memberTagGroupDTO.setMemberId(loginInfo.getMemberId());
        iMemberRelationService.createMemberTagGroup(memberTagGroupDTO, loginInfo.getAccountId());
        return new ItemResult<>(new Object());
    }


    @Operation(summary = "分页查询客户（分类型）加了delFlg")
    @PostMapping(value = "/pageRelationMember")
    public ItemResult<PageInfo<MemberRelationDTO>> pageRelationMember(@Parameter(hidden = true) LoginInfo loginInfo,
                                                                      @Parameter(name = "queryDTO", description = "会员关系查询DTO分页查询对象") @RequestBody PageQuery<MemberRelationQueryDTO> queryDTO) {
        if (queryDTO.getQueryDTO() == null) {
            queryDTO.setQueryDTO(new MemberRelationQueryDTO());
        }
        queryDTO.getQueryDTO().setMemberId(loginInfo.getMemberId());
        PageInfo<MemberRelationDTO> pageInfo = iMemberRelationService.pageRelationMember(queryDTO.getQueryDTO(), queryDTO.getPageNum(), queryDTO.getPageSize());
        return new ItemResult<>(pageInfo);
    }

    @Operation(summary = "导出客户列表")
    @PostMapping(value = "/downloadRelationMember")
    public ItemResult<String> downloadRelationMember(@Parameter(hidden = true) LoginInfo loginInfo,
                                                                          @Parameter(name = "query", description = "会员关系查询DTO") @RequestBody MemberRelationQueryDTO query) {
        query.setMemberId(loginInfo.getMemberId());
        List<MemberRelationDTO> memberRelationDTOList = iMemberRelationService.findRelationMemberList(query);
        return new ItemResult<>(MemberRelationExcelUtil.htmlExcelFormat(memberRelationDTOList));
    }

    @Operation(summary = "是否可以删除标签组")
    @PostMapping(value = "/canDeleteMemberTagGroup")
    public ItemResult<Boolean> canDeleteMemberTagGroup(@Parameter(name = "memberGroupId", description = "会员组id") @RequestParam String memberGroupId) {
        boolean b = iMemberRelationService.canDeleteMemberTagGroup(memberGroupId);
        return new ItemResult<>(b);
    }


    @Operation(summary = "查询会员关系返回list")
    @PostMapping(value = "/findRelationMemberList")
    public ItemResult<List<MemberRelationDTO>> findRelationMemberList(@Parameter(hidden = true) LoginInfo loginInfo,
                                                                      @Parameter(name = "query", description = "会员关系查询DTO") @RequestBody MemberRelationQueryDTO query) {
        query.setMemberId(loginInfo.getMemberId());
        List<MemberRelationDTO> b = iMemberRelationService.findRelationMemberList(query);
        return new ItemResult<>(b);
    }

    @Operation(summary = "根据条件查询会员的erp客户信息")
    @PostMapping(value = "/findErpCustomer")
    public ItemResult<List<ErpCustomerListDTO>> findErpCustomer(@Parameter(hidden = true) LoginInfo loginInfo,
                                                                @Parameter(name = "dto", description = "ERP客户查询DTO") @RequestBody ErpCustomerQueryDTO dto) {
        if (dto == null) {
            dto = new ErpCustomerQueryDTO();
        }
        dto.setSellerMemberId(loginInfo.getMemberId());
        return new ItemResult<>(iMemberRelationService.findErpCustomer(dto));
    }

    @Operation(summary = "客户信息详情")
    @GetMapping(value = "/findCustomerDetail")
    public ItemResult<CustomerDetailDTO> findCustomerDetail(@Parameter(hidden = true) LoginInfo loginInfo,
                                                            @Parameter(name = "customerId", description = "客户id") @RequestParam("customerId") String custtomerId) {
        return new ItemResult<>(iMemberRelationService.findCustomerDetail(loginInfo.getMemberId(), custtomerId));
    }

    @Operation(summary = "更新买家是否参与合同批量调价的标记字段")
    @PostMapping(value = "/updateContractBatchAdjustPriceFlg")
    public ItemResult<Boolean> updateContractBatchAdjustPriceFlg(@Parameter(hidden = true) LoginInfo loginInfo,@RequestBody ContractBatchAdjustPriceFlgUpdateDTO dto) {
        dto.setSellerId(loginInfo.getMemberId());
        dto.setOperId(loginInfo.getAccountId());
        return new ItemResult<>(iMemberRelationService.updateContractBatchAdjustPriceFlg(dto));
    }

    @Operation(summary = "更新经销商标记(注意:方法返回值为方法调用结果)")
    @PostMapping(value = "/updateAgentFlag")
    public ItemResult<Boolean> updateAgentFlag(@Parameter(hidden = true) LoginInfo loginInfo,@RequestBody ContractBatchAdjustPriceFlgUpdateDTO dto) {
        return new ItemResult<>(iMemberRelationService.updateAgentFlag(dto.getRelationId(),loginInfo.getMemberId(),loginInfo.getAccountId()));
    }

    @Operation(summary = "更新票制(1一票制、2两票制 默认1)")
    @PostMapping(value = "/updateInvoiceType")
    public ItemResult<Boolean> updateInvoiceType(@Parameter(hidden = true) LoginInfo loginInfo,@RequestBody MemberRelationInvoiceTypeUpdateDTO dto) {
        dto.setMemberId(loginInfo.getMemberId());
        dto.setOperatorId(loginInfo.getAccountId());
        return new ItemResult<>(iMemberRelationService.updateInvoiceType(dto));
    }


    @Operation(summary = "当前会员是否已经是其它会员的经销商")
    @GetMapping(value = "/hasAgentFlag")
    public ItemResult<Boolean> agentFlag(@Parameter(hidden = true) LoginInfo loginInfo) {
        return new ItemResult<>(iMemberRelationService.agentFlag(loginInfo.getMemberId()));
    }

    /**
     * 前端创建会员关系时使用
     * 根据条件查询会员的erp客户信息,并按照erpMemberName做聚合分组
     * @param loginInfo
     * @param dto
     * @return
     */
    @Operation(summary = "根据条件查询会员的erp客户信息,并按照erpMemberName做聚合分组")
    @PostMapping(value = "/findErpCustomGroup")
    public ItemResult<List<ErpCustomRelationGroupDTO>> findErpCustomGroup(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody ErpCustomerQueryDTO dto) {
        dto.setSellerMemberId(loginInfo.getMemberId());
        return new ItemResult<>(iMemberRelationService.findErpCustomGroup((dto)));
    }

    /**
     * 客户关系查询erp的客户信息
     * @param erpCustomCondDTO
     * @return
     */
    @Operation(summary = "客户关系查询erp的客户信息")
    @PostMapping(value = "/queryCustomInfoByRelation")
    public ItemResult<ErpCustomInfoDTO> queryCustomInfoByRelation(@RequestBody ErpCustomCondDTO erpCustomCondDTO) {
        return new ItemResult<>(iMemberRelationService.queryCustomInfoByRelation(erpCustomCondDTO));
    }

    /**
     * 买家视角,分页查询erp信息
     * @param pageQuery
     * @return
     */
    @Operation(summary = "买家视角,分页查询erp信息")
    @PostMapping(value = "/pageCustomerInfoBuyerView")
    public ItemResult<PageInfo<ErpMultiCustomerInfoDTO>> pageCustomerInfoBuyerView(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody PageQuery<ErpCustomCondDTO> pageQuery) {
        if (pageQuery == null) {
            pageQuery = new PageQuery<>();
            pageQuery.setPageSize(20);
            pageQuery.setPageNum(1);
        }

        if (pageQuery.getQueryDTO() == null) {
            pageQuery.setQueryDTO(new ErpCustomCondDTO());
        }
        //卖家中心erp账号数据权限授权查询列表使用if
        if (CsStringUtils.isNotBlank(pageQuery.getQueryDTO().getCustomerId()) &&
                CsStringUtils.isNotBlank(pageQuery.getQueryDTO().getMemberName())) {
            pageQuery.getQueryDTO().setCustomerId(pageQuery.getQueryDTO().getCustomerId());
        }else {
            pageQuery.getQueryDTO().setMemberId(loginInfo.getMemberId());
        }


        return new ItemResult<>(iMemberRelationService.pageCustomerInfoBuyerView(pageQuery));
    }


    /**
     * 根据会员ID和客户ID查询会员所有mdmCode
     * @param customerId
     * @return
     */
    @Operation(summary = "根据会员ID和客户ID查询会员所有mdmCode")
    @PostMapping(value = "/getMdmCodeByMemberIdAndCustomerId")
    public ItemResult<List<ErpRelationMdmCodeDTO>> getMdmCodeByMemberIdAndCustomerId(@Parameter(hidden = true) LoginInfo loginInfo,
                                                                         @RequestParam String customerId) {
        return new ItemResult<>(iMemberRelationService.getMdmCodeByMemberIdAndCustomerId(loginInfo.getMemberId(), customerId));
    }

}
