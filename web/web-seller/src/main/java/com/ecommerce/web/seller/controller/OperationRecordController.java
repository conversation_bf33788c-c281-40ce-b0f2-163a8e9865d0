package com.ecommerce.web.seller.controller;

import java.util.List;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.logistics.api.dto.operationrecord.OperationRecordAddDTO;
import com.ecommerce.logistics.api.dto.operationrecord.OperationRecordListDTO;
import com.ecommerce.logistics.api.service.IOperationRecordService;


/**
 * 物流模块操作记录服务
 *
 * <AUTHOR>
 * @Date: 2018年9月9日 下午5:49:52
 * @Description:
 */
@RestController
@Tag(name = "OperationRecordController", description = "物流模块操作记录服务")
@RequestMapping("/operationRecord")
public class OperationRecordController {

    @Autowired
    private IOperationRecordService iOperationRecordService;

    @PostMapping("/addOperationRecord")
    public ItemResult<Void> addOperationRecord(@Parameter(name = "dto", description = "操作记录添加实体") @RequestBody OperationRecordAddDTO dto) {
        return iOperationRecordService.addOperationRecord(dto);
    }

    @PostMapping("/queryOperationRecordList")
    public ItemResult<List<OperationRecordListDTO>> queryOperationRecordList(@Parameter(name = "entryId", description = "id") @RequestParam String entryId) {
        return iOperationRecordService.queryOperationRecordList(entryId);
    }

}
