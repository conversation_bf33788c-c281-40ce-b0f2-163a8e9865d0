package com.ecommerce.web.seller.controller;


import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.common.result.ItemResult;
import com.ecommerce.member.api.dto.organization.OrgInfoDTO;
import com.ecommerce.member.api.dto.organization.OrgInfoListDTO;
import com.ecommerce.member.api.dto.organization.OrgInfoQueryDTO;
import com.ecommerce.member.api.dto.organization.OrgInfoUpdateDTO;
import com.ecommerce.member.api.service.IOrgInfoService;
import com.ecommerce.member.api.session.LoginInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.ArrayList;
import java.util.List;


/**
 * TODO
 *
 * <AUTHOR>
 */
@RestController
@Tag(name = "OrgInfoController", description = "企业组织机构")
@RequestMapping("/organization")
public class OrgInfoController {

    @Autowired
    private IOrgInfoService iOrgInfoService;


    @Operation(summary = "修改企业组织机构节点内容")
    @PostMapping(value="/update")
    public ItemResult<Object> update(@Parameter(hidden = true) LoginInfo loginInfo,
                             @Valid @RequestBody OrgInfoUpdateDTO orgInfoUpdateDTO){
        iOrgInfoService.update(orgInfoUpdateDTO, loginInfo.getAccountId());
        return new ItemResult<>(new Object());
    }


    @Operation(summary = "删除企业组织机构以及所有下级")
    @PostMapping(value="/delete")
    public ItemResult<Object> delete(@Parameter(hidden = true) LoginInfo loginInfo,
                             @RequestParam String orgId){
        iOrgInfoService.delete(orgId, loginInfo.getAccountId());
        return new ItemResult<>(new Object());
    }


    @Operation(summary = "创建企业组织机构")
    @PostMapping(value="/create")
    public ItemResult<Object> create(@Parameter(hidden = true) LoginInfo loginInfo,
                             @Valid @RequestBody OrgInfoQueryDTO orgInfoQueryDTO){
        iOrgInfoService.create(orgInfoQueryDTO, loginInfo.getAccountId());
        return new ItemResult<>(new Object());
    }


    @Operation(summary = "根据orgId获取父节点id")
    @PostMapping(value="/findParentIdByOrgId")
    public ItemResult<String> findParentIdByOrgId(@RequestParam String orgId){
        String parentId = iOrgInfoService.findParentIdByOrgId(orgId);
        parentId = parentId == null ? "" : parentId;
        return new ItemResult<>(parentId);
    }


    @Operation(summary = "根据orgid获取org节点详情")
    @PostMapping(value="/findOrgInfoByOrgId")
    public ItemResult<OrgInfoDTO> findOrgInfoByOrgId(@RequestParam String orgId){
        OrgInfoDTO orgInfoByOrgId = iOrgInfoService.findOrgInfoByOrgId(orgId);
        return new ItemResult<>(orgInfoByOrgId);
    }


    @Operation(summary = "根据orgId获取所有子节点id")
    @PostMapping(value="/findChildrenIdsByOrgId")
    public ItemResult<List<String>> findChildrenIdsByOrgId(@RequestParam String orgId){
        List<String> idsByOrgId = iOrgInfoService.findChildrenIdsByOrgId(orgId);
        return new ItemResult<>(idsByOrgId);
    }


    @Operation(summary = "按层级获取某企业组织机构")
    @PostMapping(value="/findByFloorAndMemberId")
    public ItemResult<List<OrgInfoDTO>> findByFloorAndMemberId(@RequestParam Integer floor,
                                                               @RequestParam String memberId){
        List<OrgInfoDTO> orgInfoDTOList = iOrgInfoService.findByFloorAndMemberId(floor, memberId);
        return new ItemResult<>(orgInfoDTOList);
    }


    @Operation(summary = "上移/下移组织机构（排序）")
    @PostMapping(value="/updateOrder")
    public ItemResult<Object> updateOrder(@Parameter(hidden = true) LoginInfo loginInfo,
                                  @RequestParam String orgId,
                                  @RequestParam Integer order){
        iOrgInfoService.updateOrder(orgId, order, loginInfo.getAccountId());
        return new ItemResult<>(new Object());
    }


    @Operation(summary = "根据组织机构id获取下级组织机构")
    @PostMapping(value="/findChildOrgByOrgId")
    public ItemResult<OrgInfoDTO> findChildOrgByOrgId(@RequestParam String orgId){
        OrgInfoDTO orgInfoDTO = iOrgInfoService.findChildOrgByOrgId(orgId);
        return new ItemResult<>(orgInfoDTO);
    }

    @Operation(summary = "根据会员id获取所有组织机构")
    @PostMapping(value="/findOrgByMemberId")
    public ItemResult<List<OrgInfoDTO>> findOrgByMemberId(@RequestParam String memberId){
        OrgInfoDTO orgInfoDTO = iOrgInfoService.findOrgByMemberId(memberId);
        List<OrgInfoDTO> res = new ArrayList<>();
        if(orgInfoDTO != null ) {
            res.add(orgInfoDTO);
        }
        return new ItemResult<>(res);
    }

    @Operation(summary = "根据会员id获取所有组织机构列表")
    @PostMapping(value="/findOrgListByMemberId")
    public ItemResult<List<OrgInfoListDTO>> findOrgListByMemberId(@RequestParam String memberId){
        List<OrgInfoListDTO> orgInfoDTO = iOrgInfoService.findOrgListByMemberId(memberId);
        return new ItemResult<>(orgInfoDTO);
    }

}
