package com.ecommerce.web.seller.controller;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.member.api.session.LoginInfo;
import com.ecommerce.pay.api.v2.dto.PaymentExceptionDTO;
import com.ecommerce.pay.api.v2.dto.electricInvoiceAccount.PageQuery;
import com.ecommerce.pay.api.v2.service.IPaymentExceptionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * TODO
 *
 * <AUTHOR>
 */
@RestController
@Tag(name = "PaymentExceptionController", description = "支付异常")
@RequestMapping("/paymentException")
public class PaymentExceptionController {

    @Autowired
    private IPaymentExceptionService paymentExceptionService;

    @Operation(summary = "重试")
    @PostMapping(value = "/redo")
    public ItemResult<Object> redo(@Parameter(hidden = true) LoginInfo loginInfo,
                           @Parameter(name = "id", description = "id") @RequestParam String id) {
        paymentExceptionService.redo(id, loginInfo.getAccountId());
        return new ItemResult<>(true);
    }

    @Operation(summary = "根据id查找")
    @PostMapping(value = "/findById")
    public ItemResult<Object> findById(@Parameter(name = "id", description = "id") @RequestParam String id) {
        return new ItemResult<>(paymentExceptionService.findById(id));
    }

    @Operation(summary = "分页查询")
    @PostMapping(value = "/pageByObjectId")
    public ItemResult<Object> pageByObjectId(LoginInfo loginInfo,
                                     @Parameter(name = "query", description = "支付异常DTO分页查询对象") @RequestBody PageQuery<PaymentExceptionDTO> query) {
        PaymentExceptionDTO queryDTO = query.getQueryDTO();
        if (queryDTO == null) {
            queryDTO = new PaymentExceptionDTO();
        }
        queryDTO.setObjId(loginInfo.getMemberId());
        return new ItemResult<>(paymentExceptionService.pageByObjectId(query));
    }

}
