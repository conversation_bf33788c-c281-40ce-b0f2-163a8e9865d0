package com.ecommerce.web.seller.controller;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.storehouse.TSSellerViewItem;
import com.ecommerce.logistics.api.dto.storehouse.TurnoverQueryDTO;
import com.ecommerce.logistics.api.service.IPlatformStockLogService;
import com.ecommerce.member.api.session.LoginInfo;
import com.ecommerce.web.seller.util.OrderExcelUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.util.List;

/**
 * @Auther: colu
 * @Date: 2019-09-16 15:20
 * @Description: PlatformStockLogController
 */
@Slf4j
@Tag(name = "PlatformStockLogController", description = "周转量服务")
@RestController
@RequestMapping("/platformStockLog")
public class PlatformStockLogController {

    @Autowired
    private IPlatformStockLogService platformStockLogService;

    /**
     * 为卖家提供查询周转量
     *
     * @param pageQuery
     * @return
     */
    @Operation(summary = "卖家查看周转量")
    @PostMapping("/turnoverForSeller")
    public ItemResult<PageData<TSSellerViewItem>> turnoverForSeller(@Parameter(hidden = true) LoginInfo loginInfo,
                                                                    @Parameter(name = "pageQuery", description = "周转量查询条件类分页查询对象") @RequestBody PageQuery<TurnoverQueryDTO> pageQuery) {
        if (pageQuery == null) {
            pageQuery = new PageQuery<>();
            pageQuery.setQueryDTO(new TurnoverQueryDTO());
        }
        pageQuery.getQueryDTO().setSellerId(loginInfo.getMemberId());
        return platformStockLogService.turnoverForSeller(pageQuery);
    }

    @Schema(description = "导出周转量")
    @PostMapping("/downloadTurnoverForSeller")
    public void downloadTurnoverForSeller(@Parameter(hidden = true) LoginInfo loginInfo,
                                          @Parameter(name = "turnoverQueryDTO", description = "周转量查询条件类") @RequestBody TurnoverQueryDTO turnoverQueryDTO,
                                          HttpServletResponse response) {

        if (turnoverQueryDTO == null) {
            turnoverQueryDTO = new TurnoverQueryDTO();
        }
        turnoverQueryDTO.setSellerId(loginInfo.getMemberId());
        List<TSSellerViewItem> tsSellerViewItemList = platformStockLogService.queryTurnoverForSeller(turnoverQueryDTO).getData();
        //excel标题
        String[] title = {"商品名称", "时间段", "累计入库量", "累计出库量"};
        //excel文件名
        String fileName = "仓库周转量" + System.currentTimeMillis() + ".xls";
        //sheet名
        String sheetName = "仓库周转量";
        String[][] content = null;
        if (CollectionUtils.isNotEmpty(tsSellerViewItemList)) {
            content = new String[tsSellerViewItemList.size()][];
            for (int i = 0; i < tsSellerViewItemList.size(); i++) {
                content[i] = new String[title.length];
                TSSellerViewItem obj = tsSellerViewItemList.get(i);
                content[i][0] = obj.getProductName();
                content[i][1] = obj.getStartDateStr() + '至' + obj.getEndDateStr();
                content[i][2] = obj.getTotalInQuantity() == null ? "-" : obj.getTotalInQuantity().toString() + '吨';
                content[i][3] = obj.getTotalOutQuantity() == null ? "-" : obj.getTotalOutQuantity().toString() + '吨';
            }
        }
//创建HSSFWorkbook
        HSSFWorkbook wb = OrderExcelUtil.getHSSFWorkbook(sheetName, title, content, null);
//响应到客户端
        try {
            this.setResponseHeader(response, fileName);
            OutputStream os = response.getOutputStream();
            wb.write(os);
            os.flush();
            os.close();
        } catch (Exception e) {
            log.error("周转量统计导出异常:{}", turnoverQueryDTO, e);
        }
    }

    public void setResponseHeader(HttpServletResponse response, String fileName) {
        try {
            try {
                fileName = new String(fileName.getBytes(), "utf-8");
            } catch (UnsupportedEncodingException e) {
                // TODO Auto-generated catch block
                log.error(e.getMessage(), e);
            }
            response.setContentType("application/octet-stream;charset=utf-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
            response.addHeader("Pargam", "no-cache");
            response.addHeader("Cache-Control", "no-cache");
        } catch (Exception ex) {
            log.error("set response header error : ", ex);
        }
    }

}
