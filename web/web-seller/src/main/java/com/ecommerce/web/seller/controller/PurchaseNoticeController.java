package com.ecommerce.web.seller.controller;

import com.ecommerce.base.api.dto.cloud.AttachmentinfoDTO;
import com.ecommerce.base.api.service.IAttachmentService;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.information.api.dto.purchase.PurchaseNoticeAddDTO;
import com.ecommerce.information.api.dto.purchase.PurchaseNoticeAnonDTO;
import com.ecommerce.information.api.dto.purchase.PurchaseNoticeCondDTO;
import com.ecommerce.information.api.dto.purchase.PurchaseNoticeDeleteDTO;
import com.ecommerce.information.api.dto.purchase.PurchaseNoticeDetailDTO;
import com.ecommerce.information.api.dto.purchase.PurchaseNoticeListDTO;
import com.ecommerce.information.api.dto.purchase.PurchaseNoticeStatusDTO;
import com.ecommerce.information.api.dto.purchase.PurchaseNoticeUpdateDTO;
import com.ecommerce.information.api.service.IPurchaseNoticeService;
import com.ecommerce.member.api.session.LoginInfo;
import com.ecommerce.web.seller.dto.information.PurchaseNoticeWithAttDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * 采购招标公告服务
 *
 *  PurchaseNoticeController
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/purchaseNotice")
@Slf4j
@Tag(name = "PurchaseNoticeController", description = "采购招标公告服务")
public class PurchaseNoticeController {

    @Autowired
    private IPurchaseNoticeService purchaseNoticeService;

    @Autowired
    private IAttachmentService attachmentService;

    /**
     * 采购公告添加
     *
     * @param purchaseNoticeAddDTO
     * @return
     */
    @Operation(summary = "采购公告添加")
    @PostMapping(value = "/createNotice")
    public ItemResult<String> createNotice(@Parameter(hidden = true) LoginInfo loginInfo,
                                           @Parameter(name = "purchaseNoticeAddDTO", description = "采购公告添加实体") @RequestBody PurchaseNoticeAddDTO purchaseNoticeAddDTO) {
        purchaseNoticeAddDTO.setMemberId(loginInfo.getMemberId());
        purchaseNoticeAddDTO.setMemberName(loginInfo.getMemberName());
        //责任编辑人由前端指定
        purchaseNoticeAddDTO.setCreateUser(loginInfo.getAccountId());
        return purchaseNoticeService.createNotice(purchaseNoticeAddDTO);
    }

    /**
     * 批量删除公告
     *
     * @param purchaseNoticeIdList
     * @return
     */
    @Operation(summary = "批量删除公告")
    @PostMapping(value = "/deleteNoticeList")
    public ItemResult<Void> deleteNoticeList(@Parameter(hidden = true) LoginInfo loginInfo,
                                             @Parameter(name = "purchaseNoticeIdList", description = "采购公告id列表") @RequestBody List<String> purchaseNoticeIdList) {
        PurchaseNoticeDeleteDTO purchaseNoticeDeleteDTO = new PurchaseNoticeDeleteDTO();
        purchaseNoticeDeleteDTO.setUpdateUser(loginInfo.getAccountId());
        purchaseNoticeDeleteDTO.setPurchaseNoticeIdList(purchaseNoticeIdList);
        return purchaseNoticeService.deleteNoticeList(purchaseNoticeDeleteDTO);
    }

    /**
     * 匿名查看公告
     *
     * @param purchaseNoticeId
     * @return
     */
    @Operation(summary = "匿名查看公告")
    @PostMapping(value = "/anon/anonQueryById")
    public ItemResult<PurchaseNoticeAnonDTO> anonQueryById(@Parameter(name = "purchaseNoticeId", description = "采购公告id") @RequestParam String purchaseNoticeId) {
        return purchaseNoticeService.anonQueryById(purchaseNoticeId);
    }

    /**
     * 明细查询
     *
     * @param purchaseNoticeId
     * @return
     */
    @Operation(summary = "明细查询")
    @PostMapping(value = "/queryDetailById")
    public ItemResult<PurchaseNoticeWithAttDTO> queryDetailById(@Parameter(hidden = true) LoginInfo loginInfo,
                                                                @Parameter(name = "purchaseNoticeId", description = "采购公告id") @RequestParam String purchaseNoticeId) {
        ItemResult<PurchaseNoticeDetailDTO> dbDetail = purchaseNoticeService.queryDetailById(purchaseNoticeId);
        if (dbDetail == null || dbDetail.getData() == null) {
            return new ItemResult<>(null);
        }

        PurchaseNoticeDetailDTO infoDTO = dbDetail.getData();
        PurchaseNoticeWithAttDTO withAttDTO = new PurchaseNoticeWithAttDTO();
        BeanUtils.copyProperties(infoDTO, withAttDTO);
        List<AttachmentinfoDTO> attachmentinfoDTOList = attachmentService.getAttachmentByBID(infoDTO.getAttachmentBid());
        withAttDTO.setAttachmentinfoDTOList(attachmentinfoDTOList);
        return new ItemResult<>(withAttDTO);
    }

    /**
     * 分页条件查询
     *
     * @param purchaseNoticeCondDTO
     * @return
     */
    @Operation(summary = "分页条件查询")
    @PostMapping(value = "/queryByCond")
    public ItemResult<PageData<PurchaseNoticeListDTO>> queryByCond(@Parameter(hidden = true) LoginInfo loginInfo,
                                                                   @Parameter(name = "purchaseNoticeCondDTO", description = "采购查询条件实体") @RequestBody PurchaseNoticeCondDTO purchaseNoticeCondDTO) {
        purchaseNoticeCondDTO.setMemberId(loginInfo.getMemberId());
        return purchaseNoticeService.queryByCond(purchaseNoticeCondDTO);
    }

    /**
     * 更新公告内容
     *
     * @param purchaseNoticeUpdateDTO
     * @return
     */
    @Operation(summary = "更新公告内容")
    @PostMapping(value = "/updateNoticeInfo")
    public ItemResult<Void> updateNoticeInfo(@Parameter(hidden = true) LoginInfo loginInfo,
                                             @Parameter(name = "purchaseNoticeUpdateDTO", description = "采购公告更新实体") @RequestBody PurchaseNoticeUpdateDTO purchaseNoticeUpdateDTO) {
        log.info("purchaseNoticeUpdateDTO : {}", purchaseNoticeUpdateDTO);
        purchaseNoticeUpdateDTO.setMemberId(loginInfo.getMemberId());
        purchaseNoticeUpdateDTO.setMemberName(loginInfo.getMemberName());
        purchaseNoticeUpdateDTO.setUpdateUser(loginInfo.getAccountId());
        return purchaseNoticeService.updateNoticeInfo(purchaseNoticeUpdateDTO);
    }

    /**
     * 更改公告状态
     *
     * @param purchaseNoticeStatusDTO
     * @return
     */
    @Operation(summary = "更改公告状态")
    @PostMapping(value = "/updateStatus")
    public ItemResult<Void> updateStatus(@Parameter(hidden = true) LoginInfo loginInfo,
                                         @Parameter(name = "purchaseNoticeStatusDTO", description = "公告状态更改实体") @RequestBody PurchaseNoticeStatusDTO purchaseNoticeStatusDTO) {
        purchaseNoticeStatusDTO.setUpdateUser(loginInfo.getAccountId());
        return purchaseNoticeService.updateStatus(purchaseNoticeStatusDTO);
    }

}
