package com.ecommerce.web.seller.controller;

import com.alibaba.fastjson.JSON;
import com.ecommerce.base.api.dto.MemberSearchDTO;
import com.ecommerce.base.api.dto.ReceivingAddressCreateDTO;
import com.ecommerce.base.api.dto.ReceivingAddressDTO;
import com.ecommerce.base.api.dto.ReceivingAddressErpInfoDTO;
import com.ecommerce.base.api.dto.ReceivingAddressErpInfoUpdateDTO;
import com.ecommerce.base.api.dto.ReceivingAddressMapAuditDTO;
import com.ecommerce.base.api.dto.ReceivingAddressMapDTO;
import com.ecommerce.base.api.dto.ReceivingAddressMapQueryDTO;
import com.ecommerce.base.api.dto.ReceivingAddressQueryDTO;
import com.ecommerce.base.api.dto.ReceivingAddressUpdateDTO;
import com.ecommerce.base.api.dto.warehouse.PageQuery;
import com.ecommerce.base.api.dto.wharf.ReceivingAddrMapWharfDTO;
import com.ecommerce.base.api.service.IReceivingAddressService;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.exception.BusinessOperationException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.member.api.service.IMemberRelationService;
import com.ecommerce.member.api.session.LoginInfo;
import com.ecommerce.web.common.PageQueryUtil;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Sets;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import com.ecommerce.common.utils.CsStringUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.util.List;


/**
 * 收货地址相关service
 *
 * <AUTHOR>
 */

@Slf4j
@Tag(name = "ReceivingAddress", description = "收货地址")
@RestController
@RequestMapping("/receivingAddress")
public class ReceivingAddressController {

    @Autowired
    private IReceivingAddressService iReceivingAddressService;
    @Autowired
    private IMemberRelationService memberRelationService;

    @Operation(summary = "更新收货地址中的销售区域")
    @PostMapping(value = "/updateAddressMap")
    public ItemResult<Object> updateAddressMap(LoginInfo loginInfo, @RequestBody ReceivingAddressMapDTO dto) {
        dto.setOperator(loginInfo.getMemberId());
        iReceivingAddressService.updateAddressMap(dto);

        return new ItemResult<>(true);
    }

    @Operation(summary = "买家卖家厂家查询")
    @GetMapping(value = "/memberSearch")
    public ItemResult<List<MemberSearchDTO>> memberSearch(LoginInfo loginInfo, @RequestParam String keyword, @Parameter @RequestParam int searchType) {
        MemberSearchDTO dto = new MemberSearchDTO();
        dto.setMemberName(keyword);
        dto.setMemberType(loginInfo.getHasErp() ? 30 : 20);
        dto.setSearchType(searchType);
        dto.setMemberId(loginInfo.getMemberId());

        return new ItemResult<>(iReceivingAddressService.memberSearch(dto));
    }

    @Operation(summary = "买家收货地址Map")
    @PostMapping(value = "/addressMap")
    public ItemResult<PageInfo<ReceivingAddressMapDTO>> addressMap(LoginInfo loginInfo, @RequestBody ReceivingAddressMapQueryDTO dto) {
        if (CollectionUtils.isNotEmpty(loginInfo.getRoleNameList()) && loginInfo.getRoleNameList().contains("seller_agent")) {
            dto.setSellerId(loginInfo.getMemberId());
        } else {
            dto.setFirmId(loginInfo.getMemberId());
            if (dto.getAuditStatus() == null) {
                dto.setAuditStatusList(Sets.newHashSet(20, 30));
            }
        }


        PageInfo<ReceivingAddressMapDTO> page = iReceivingAddressService.addressMap(dto);
        return new ItemResult<>(page);
    }

    @Operation(summary = "审核买家收货地址")
    @PostMapping(value = "/addressAudit")
    public ItemResult<Object> addressAudit(LoginInfo loginInfo, @RequestBody List<ReceivingAddressMapAuditDTO> dataList) {
        boolean sellerAgent = CollectionUtils.isNotEmpty(loginInfo.getRoleNameList()) && loginInfo.getRoleNameList().contains("seller_agent");
        // 判断是否是厂商
        int memberType = sellerAgent ? 20 : 30;
        iReceivingAddressService.addressAudit(memberType, loginInfo.getMemberId(), dataList);

        return new ItemResult<>(new Object());
    }

    @Operation(summary = "修改自己的收货地址")
    @PostMapping(value = "/update")
    public ItemResult<Object> update(@Parameter(hidden = true) LoginInfo loginInfo, @Valid @RequestBody ReceivingAddressUpdateDTO receivingAddressUpdateDTO) {
        if (CsStringUtils.isBlank(receivingAddressUpdateDTO.getId())) {
            return new ItemResult<>("id不能为空");
        }
        if (!checkPermission(receivingAddressUpdateDTO.getId(), loginInfo.getMemberId())) {
            return new ItemResult<>("无权限修改地址");
        }
        receivingAddressUpdateDTO.setOperator(loginInfo.getAccountId());
        receivingAddressUpdateDTO.setMemberId(loginInfo.getMemberId());
        iReceivingAddressService.update(receivingAddressUpdateDTO);
        return new ItemResult<>(new Object());
    }

    @Operation(summary = "修改客户的收货地址")
    @PostMapping(value = "/customer/update")
    public ItemResult<Object> customerUpdate(@Parameter(hidden = true) LoginInfo loginInfo, @Valid @RequestBody ReceivingAddressUpdateDTO receivingAddressUpdateDTO) {
        if (CsStringUtils.isBlank(receivingAddressUpdateDTO.getId())) {
            throw new BizException(BasicCode.INVALID_PARAM, ":id不能为空");
        }
        if (CsStringUtils.isBlank(receivingAddressUpdateDTO.getMemberId())) {
            throw new BizException(BasicCode.INVALID_PARAM, ":客户会员你的不能为空");
        }
        ReceivingAddressDTO receivingAddressDTO = iReceivingAddressService.findById(receivingAddressUpdateDTO.getId());
        if (receivingAddressDTO == null) {
            throw new BizException(BasicCode.INVALID_PARAM, ":客户会员不能为空");
        }
        if (!CsStringUtils.equals(loginInfo.getAccountId(), receivingAddressDTO.getCreateUser())) {
            throw new BizException(BasicCode.INVALID_PARAM, ":只能修改自己创建的收货地");
        }
        Boolean bl = memberRelationService.relationIsExists(loginInfo.getMemberId(), receivingAddressUpdateDTO.getMemberId());
        if (bl == null || !bl.booleanValue()) {
            throw new BizException(BasicCode.INVALID_PARAM, ":只能修改自己的客户");
        }
        receivingAddressUpdateDTO.setOperator(loginInfo.getAccountId());
        iReceivingAddressService.update(receivingAddressUpdateDTO);
        return new ItemResult<>(new Object());
    }


    @Operation(summary = "创建收货地址")
    @PostMapping(value = "/create")
    public ItemResult<Object> create(@Parameter(hidden = true) LoginInfo loginInfo, @Valid @RequestBody ReceivingAddressCreateDTO receivingAddressCreateDTO) {
        receivingAddressCreateDTO.setOperator(loginInfo.getAccountId());
        receivingAddressCreateDTO.setMemberId(loginInfo.getMemberId());
        ItemResult<String> baseResult = iReceivingAddressService.create(receivingAddressCreateDTO);
        if (baseResult == null || !baseResult.isSuccess()) {
            log.error("添加地址失败:{}", baseResult);
            return ItemResult.fail(baseResult.getDescription());
        }
        return new ItemResult<>(new Object());
    }

    @Operation(summary = "创建客户的收货地址")
    @PostMapping(value = "/customer/create")
    public ItemResult<Object> customerCreate(@Parameter(hidden = true) LoginInfo loginInfo, @Valid @RequestBody ReceivingAddressCreateDTO receivingAddressCreateDTO) {
        if (CsStringUtils.isBlank(receivingAddressCreateDTO.getMemberId())) {
            throw new BizException(BasicCode.INVALID_PARAM, ":客户会员id不能为空");
        }
        Boolean bl = memberRelationService.relationIsExists(loginInfo.getMemberId(), receivingAddressCreateDTO.getMemberId());
        if (bl == null || !bl.booleanValue()) {
            throw new BizException(BasicCode.INVALID_PARAM, ":只能修改自己的客户");
        }
        receivingAddressCreateDTO.setOperator(loginInfo.getAccountId());
        ItemResult<String> baseResult = iReceivingAddressService.create(receivingAddressCreateDTO);
        if (baseResult == null || !baseResult.isSuccess()) {
            log.error("添加地址失败:{}", baseResult);
            return ItemResult.fail(baseResult.getDescription());
        }
        return new ItemResult<>(new Object());
    }


    @Operation(summary = "查询当前会员的所有收货地址")
    @PostMapping(value = "/findByMemberId")
    public ItemResult<PageInfo<ReceivingAddressDTO>> findByMemberId(@Parameter(hidden = true) LoginInfo loginInfo,
                                                                    @Parameter(name = "pageSize", description = "每页条数") @RequestParam(required = false) Integer pageSize,
                                                                    @Parameter(name = "pageNum", description = "当前页码") @RequestParam(required = false) Integer pageNum,
                                                                    @Parameter(name = "keyWords", description = "关键字") @RequestParam(required = false) String keyWords,
                                                                    @Parameter(name = "type", description = "类型") @RequestParam(required = false) Integer type) {
        PageQuery<ReceivingAddressQueryDTO> pageQuery = new PageQuery<>();
        PageQueryUtil.initPage(pageQuery, pageNum, pageSize);
        ReceivingAddressQueryDTO receivingAddressQueryDTO = new ReceivingAddressQueryDTO();
        receivingAddressQueryDTO.setMemberId(loginInfo.getMemberId());
        if (CsStringUtils.isNotBlank(keyWords)) {
            receivingAddressQueryDTO.setKeyWords(keyWords);
        }
        if (type != null) {
            receivingAddressQueryDTO.setType(type);
        }
        pageQuery.setQueryDTO(receivingAddressQueryDTO);
        return new ItemResult<>(iReceivingAddressService.findPageByMemberId(pageQuery));
    }

    @Operation(summary = "查询当前会员的所有收货地址")
    @PostMapping(value = "/customer/findByMemberId")
    public ItemResult<List<ReceivingAddressDTO>> customerFindByMemberId(@Parameter(hidden = true) LoginInfo loginInfo,
                                                                        @RequestParam String memberId,
                                                                        @Parameter(name = "keyWords", description = "关键字") @RequestParam(required = false) String keyWords,
                                                                        @Parameter(name = "type", description = "类型") @RequestParam(required = false) Integer type,
                                                                        @Parameter(name = "saleRegionId", description = "销售区域ID") @RequestParam(required = false) String saleRegionId) {
        if (CsStringUtils.isBlank(memberId)) {
            throw new BizException(BasicCode.INVALID_PARAM, ":客户会员id不能为空");
        }
        Boolean bl = memberRelationService.relationIsExists(loginInfo.getMemberId(), memberId);
        if (bl == null || !bl.booleanValue()) {
            throw new BizException(BasicCode.INVALID_PARAM, ":只能查询关系客户");
        }
        PageQuery<ReceivingAddressQueryDTO> pageQuery = new PageQuery<>();
        Integer pageNum = 1;
        Integer pageSize = 1000;
        pageQuery.setPageNum(pageNum);
        pageQuery.setPageSize(pageSize);
        ReceivingAddressQueryDTO receivingAddressQueryDTO = new ReceivingAddressQueryDTO();
        receivingAddressQueryDTO.setMemberId(memberId);
        if (type != null) {
            receivingAddressQueryDTO.setType(type);
        }
        receivingAddressQueryDTO.setKeyWords(keyWords);
        receivingAddressQueryDTO.setSaleRegionId(saleRegionId);
        pageQuery.setQueryDTO(receivingAddressQueryDTO);
        PageInfo<ReceivingAddressDTO> pageByMemberId = iReceivingAddressService.findPageByMemberId(pageQuery);
        return new ItemResult<>(pageByMemberId.getList());
    }


    @Operation(summary = "根据id删除收货地址")
    @PostMapping(value = "/deleteById")
    public ItemResult<Object> deleteById(@Parameter(hidden = true) LoginInfo loginInfo, @RequestParam String id) {
        if (!checkPermission(id, loginInfo.getMemberId())) {
            throw new BizException(BasicCode.INVALID_PARAM, ":无权限删除地址");
        }
        iReceivingAddressService.deleteById(id, loginInfo.getAccountId());
        return new ItemResult<>(new Object());
    }

    @Operation(summary = "根据id删除客户的收货地址")
    @PostMapping(value = "/customer/deleteById")
    public ItemResult<Object> customerDeleteById(@Parameter(hidden = true) LoginInfo loginInfo, @RequestParam String id, @RequestParam String memberId) {
        ReceivingAddressDTO receivingAddressDTO = iReceivingAddressService.findById(id);
        if (receivingAddressDTO != null && CsStringUtils.equals(loginInfo.getAccountId(), receivingAddressDTO.getCreateUser())) {
            iReceivingAddressService.deleteById(id, loginInfo.getAccountId());
        }
        return new ItemResult<>(new Object());
    }


    @Operation(summary = "根据id查找收货地址")
    @PostMapping(value = "/findById")
    public ItemResult<ReceivingAddressDTO> findById(@Parameter(hidden = true) LoginInfo loginInfo, @RequestParam String id, @RequestParam(required = false) String memberId) {
        ReceivingAddressDTO receivingAddressDTO = iReceivingAddressService.findById(id);
        if (receivingAddressDTO == null) {
            return new ItemResult<>(null);
        }
        if (CsStringUtils.equals(receivingAddressDTO.getMemberId(), loginInfo.getMemberId())) {
            return new ItemResult<>(receivingAddressDTO);
        }
        if (CsStringUtils.isNotBlank(memberId) && CsStringUtils.equals(receivingAddressDTO.getMemberId(), memberId)) {
            return new ItemResult<>(receivingAddressDTO);
        }
        throw new BizException(BasicCode.INVALID_PARAM, ":无权限查看地址");
    }


    @Operation(summary = "设置默认收货地址")
    @PostMapping(value = "/setDefaultById")
    public ItemResult<Object> setDefaultById(@Parameter(hidden = true) LoginInfo loginInfo, @RequestParam String id) {
        if (!checkPermission(id, loginInfo.getMemberId())) {
            throw new BizException(BasicCode.INVALID_PARAM, ":无权限设置默认地址");
        }
        iReceivingAddressService.setDefaultById(id, loginInfo.getMemberId(), loginInfo.getAccountId());
        return new ItemResult<>(new Object());
    }


    @Operation(summary = "根据会员id获取默认收货地址（企业员工则根据企业id获取默认收货地址）")
    @PostMapping(value = "/getDefaultByMemberId")
    public ItemResult<ReceivingAddressDTO> getDefaultByMemberId(@Parameter(hidden = true) LoginInfo loginInfo) {
        return new ItemResult<>(iReceivingAddressService.getDefaultByMemberId(loginInfo.getMemberId()));
    }

    @Operation(summary = "根据会员id获取默认收货地址（企业员工则根据企业id获取默认收货地址）")
    @PostMapping(value = "/findErpInfoBySellerIdAndBuyerId")
    public ItemResult<List<ReceivingAddressErpInfoDTO>> findErpInfoBySellerIdAndBuyerId(@Parameter(hidden = true) LoginInfo loginInfo, @RequestParam String buyerId) {
        return new ItemResult<>(iReceivingAddressService.findErpInfoBySellerIdAndBuyerId(loginInfo.getMemberId(), buyerId));
    }

    @Operation(summary = "根据会员id获取默认收货地址（企业员工则根据企业id获取默认收货地址）")
    @PostMapping(value = "/updateErpInfo")
    public ItemResult<Boolean> updateErpInfo(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody ReceivingAddressErpInfoUpdateDTO dto) {
        dto.setOperatorId(loginInfo.getAccountId());
        dto.setSellerId(loginInfo.getMemberId());
        iReceivingAddressService.updateErpInfo(dto);
        return new ItemResult<>(true);
    }


    @Operation(summary = "ERP码头管理列表")
    @PostMapping(value = "/queryErpWharfListByQuery")
    public ItemResult<PageInfo<ReceivingAddressMapDTO>> queryErpWharfListByQuery(@Parameter(hidden = true) LoginInfo loginInfo,
                                                                                 @RequestBody ReceivingAddressMapQueryDTO dto) {
        log.info("登录态信息：{}", JSON.toJSON(loginInfo));
        dto.setSellerId(loginInfo.getMemberId());
        //如果是经销商，则查询数据库，否则直接返回空数据
        if (CollectionUtils.isNotEmpty(loginInfo.getRoleNameList()) && loginInfo.getRoleNameList().contains("seller_agent")) {
            log.info("经销商查询参数：{}", dto);
            return new ItemResult<>(iReceivingAddressService.queryErpWharfListByQuery(dto));
        } else {
            log.info("非经销商不查询数据库");
            return new ItemResult<>(null);
        }
    }


    @Operation(summary = "修改ERP码头名称")
    @PostMapping(value = "/updateErpWharf")
    public ItemResult<Boolean> updateErpWharf(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody ReceivingAddressMapDTO dto) {
        dto.setOperator(loginInfo.getAccountId());
        dto.setSellerId(loginInfo.getMemberId());
        iReceivingAddressService.updateErpWharf(dto);
        return new ItemResult<>(true);
    }


    @Operation(summary = "通过码头名称模糊搜索ERP码头")
    @PostMapping(value = "/queryErpWharfList")
    public ItemResult<List<ReceivingAddrMapWharfDTO>> queryErpWharfList(@Parameter(hidden = true) LoginInfo loginInfo, @Parameter(name = "erpWharfName", description = "电商码头名称") @RequestParam String erpWharfName) {
        ReceivingAddressMapQueryDTO dto = new ReceivingAddressMapQueryDTO();
        dto.setSellerId(loginInfo.getMemberId());
        dto.setErpWharfName(erpWharfName);
        return new ItemResult<>(iReceivingAddressService.queryErpWharfList(dto));
    }


    private Boolean checkPermission(String id, String memberId) {
        ReceivingAddressDTO receivingAddressDTO = iReceivingAddressService.findById(id);
        if (receivingAddressDTO != null && receivingAddressDTO.getMemberId().equals(memberId)) {
            return true;
        }
        return false;
    }

    @Operation(summary = "根据卖家ID模糊查询地址")
    @PostMapping(value = "/queryAddressBySellerId")
    public ItemResult<List<ReceivingAddressMapDTO>> queryAddressBySellerId(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody ReceivingAddressMapQueryDTO dto) {
        dto.setSellerId(loginInfo.getMemberId());
        return new ItemResult<>(iReceivingAddressService.queryAddressBySellerId(dto));
    }

}
