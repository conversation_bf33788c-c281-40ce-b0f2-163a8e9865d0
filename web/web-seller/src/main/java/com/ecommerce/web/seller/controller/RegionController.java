package com.ecommerce.web.seller.controller;



import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.base.api.dto.region.RegionDTO;
import com.ecommerce.base.api.dto.region.RegionLabelValueDTO;
import com.ecommerce.base.api.dto.region.RegionSampleDTO;
import com.ecommerce.base.api.service.IRegionService;
import com.ecommerce.common.result.ItemResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <AUTHOR>
 * @Date: 12/09/2018 14:08
 * @DESCRIPTION:
 */
@RestController
@Tag(name = "RegionController", description = "行政区域查询服务")
@RequestMapping("/region")
public class RegionController {

    @Autowired
    private IRegionService iRegionService;

    @Operation(summary = "根据区域代码查询区/县或街道")
    @PostMapping(value="/findByAdcodeAndLevel")
    public ItemResult<List<RegionDTO>> findByAdcodeAndLevel(@Parameter(name = "adcode", description = "区县code") @RequestParam String adcode,
                                                            @Parameter(name = "level", description = "级别") @RequestParam String level){
        return new ItemResult<>(iRegionService.findByAdcodeAndLevel(adcode,level));
    }


    @Operation(summary = "查询城市")
    @PostMapping(value="/findCityByNameLike")
    public ItemResult<List<RegionDTO>> findCityByNameLike(@Parameter(name = "cityNameLike", description = "城市名称like") @RequestParam String cityNameLike){
        return new ItemResult<>(iRegionService.findCityByNameLike(cityNameLike));
    }


    @Operation(summary = "根据区域代码查询区/县或街道")
    @PostMapping(value="/findByAdcodeAndLevelAndNameLike")
    public ItemResult<List<RegionDTO>> findByAdcodeAndLevelAndNameLike(@Parameter(name = "adcode", description = "区县code") @RequestParam String adcode,
                                                                       @Parameter(name = "level", description = "级别") @RequestParam String level,
                                                                       @Parameter(name = "nameLike", description = "名称like") @RequestParam String nameLike){
        return new ItemResult<>(iRegionService.findByAdcodeAndLevelAndNameLike(adcode,level,nameLike));
    }


    @Operation(summary = "查询省份")
    @PostMapping(value="/findProvinceByNameLike")
    public ItemResult<List<RegionDTO>> findProvinceByNameLike(@Parameter(name = "provinceNameLike", description = "省份名称like") @RequestParam String provinceNameLike){
        return new ItemResult<>(iRegionService.findProvinceByNameLike(provinceNameLike));
    }


    @Operation(summary = "查询所有数据")
    @GetMapping(value="/findAll")
    public ItemResult<List<RegionDTO>> findAll(){
        return new ItemResult<>(iRegionService.findAll());
    }


    @Operation(summary = "根据区关键字，并且返回子区域，功能类似该接口功能（https://lbs.amap.com/api/webservice/guide/api/district），但比它简单点")
    @PostMapping(value="/findDistrict")
    public ItemResult<List<RegionDTO>> findDistrict(@Parameter(name = "keywords", description = "区关键字") @RequestParam String keywords,
                                                    @Parameter(name = "subdistrict", description = "子区域") @RequestBody int subdistrict,
                                                    @Parameter(name = "pageSize", description = "每页条数") @RequestBody int pageSize,
                                                    @Parameter(name = "needPolyline", description = "是否需要折线") @RequestBody boolean needPolyline){
        return new ItemResult<>(iRegionService.findDistrict(keywords,subdistrict,pageSize,needPolyline));
    }


    @Operation(summary = "查询所有数据并转换成label-Value格式")
    @GetMapping(value="/findAll2LabelValue")
    public List<RegionLabelValueDTO> findAll2LabelValue() {
        return iRegionService.findAll2LabelValue();
    }

    @Operation(summary = "根据当前层级adcode查找下一层级的数据")
    @GetMapping(value={"/findByParentAdCode","/findByParentAdCode/anon"})
    public ItemResult<List<RegionSampleDTO>> findByParentAdCode(@Parameter(name = "parentAdcode", description = "当前层级adcode") String parentAdcode) {
        parentAdcode = parentAdcode == null ? "100000" : parentAdcode;
        return new ItemResult<>(iRegionService.findByParentAdCode(parentAdcode));
    }

    @Operation(summary = "查询所有省份")
    @GetMapping(value="/findAllProvince")
    public ItemResult<List<RegionDTO>> findAllProvince() {
        return new ItemResult<>(iRegionService.findAllProvince());
    }
}
