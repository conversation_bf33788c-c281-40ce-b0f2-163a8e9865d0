package com.ecommerce.web.seller.controller;

import com.alibaba.fastjson.JSON;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.exception.BusinessOperationException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.goods.api.dto.BatchUpdateResourceDTO;
import com.ecommerce.goods.api.dto.ChangeHistoryDTO;
import com.ecommerce.goods.api.dto.ChangePriceAndNumDTO;
import com.ecommerce.goods.api.dto.CreateResourceDTO;
import com.ecommerce.goods.api.dto.GetGoodsResourceStoreDTO;
import com.ecommerce.goods.api.dto.GoodsPriceDTO;
import com.ecommerce.goods.api.dto.ManualOnSaleDTO;
import com.ecommerce.goods.api.dto.PriceModeDTO;
import com.ecommerce.goods.api.dto.PromptOnsaleResourceDTO;
import com.ecommerce.goods.api.dto.ReqChangePriceAndNumDTO;
import com.ecommerce.goods.api.dto.ReqPromptOnsaleResourceDTO;
import com.ecommerce.goods.api.dto.ReqResourceSellerDTO;
import com.ecommerce.goods.api.dto.ResourceDTO;
import com.ecommerce.goods.api.dto.ResourceSellerDTO;
import com.ecommerce.goods.api.dto.UpdateResourceDTO;
import com.ecommerce.goods.api.service.IResourceListingService;
import com.ecommerce.goods.api.service.IResourceQueryService;
import com.ecommerce.goods.api.service.IResourceService;
import com.ecommerce.member.api.dto.account.AccountDTO;
import com.ecommerce.member.api.dto.exception.MemberCode;
import com.ecommerce.member.api.session.LoginInfo;
import com.ecommerce.pay.api.v2.dto.MemberChannelDTO;
import com.ecommerce.pay.api.v2.enums.ChannelPaymentTypeEnum;
import com.ecommerce.pay.api.v2.enums.ClientType;
import com.ecommerce.pay.api.v2.service.IMemberChannelService;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import com.ecommerce.common.utils.CsStringUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;


/**
 * 挂牌管理-Web端控制器，挂牌管理相关功能对外服务接口
 *
 * <AUTHOR>
 */
@Slf4j
@Tag(name = "ResourceController", description = "web端控制器，挂牌管理相关功能对外服务接口")
@RestController
@RequestMapping("/resource")
public class ResourceController {


    @Autowired
    private IResourceService resourceService;
    @Autowired
    private IMemberChannelService memberChannelService;
    @Autowired
    private IResourceQueryService resourceQueryService;
    @Autowired
    private IResourceListingService resourceListingService;

    @Operation(summary = "卖家挂牌管理列表")
    @PostMapping(value="/pageResourceSeller")
    public ItemResult<PageInfo<ResourceSellerDTO>> pageResourceSeller(@Parameter(hidden = true) LoginInfo loginInfo, @Valid @RequestBody ReqResourceSellerDTO reqResourceSellerDTO){
        reqResourceSellerDTO.setSellerId(loginInfo.getMemberId());
        //非主账号，才添加数据权限过滤
        if( loginInfo.getAccountType() == null || loginInfo.getAccountType() != AccountDTO.ACCOUNT_TYPE_MEMBER_MASTER ) {
            reqResourceSellerDTO.setRegionList(loginInfo.getSaleRegionIdList());
        }
        log.info("reqResourceSellerDTO,req:{}", reqResourceSellerDTO.toString());
        PageInfo<ResourceSellerDTO> pageInfo = resourceQueryService.pageResourceSeller(reqResourceSellerDTO);
        List<MemberChannelDTO> channels = Lists.newArrayList();
        try {
            channels = memberChannelService.getMemberAvailChannelsByCustomer(reqResourceSellerDTO.getSellerId(),
                    ClientType.PC.code(), ChannelPaymentTypeEnum.PAYEE.getCode());
        }catch (Exception e){
            log.error("获取支付方式接口异常,异常信息:{}"+e.getMessage());
        }
        List<ResourceSellerDTO> list = pageInfo.getList();
        if (!CollectionUtils.isEmpty(list)) {
            for (ResourceSellerDTO resourceSellerDTO : list) {
                List<String> payWays = new ArrayList<>();
                ResourceDTO resourceDTO = resourceQueryService.getSimpleResourceDetail(resourceSellerDTO.getResourceId());
                List<String> payWayIds = resourceDTO.getPayWay();
                if (CollectionUtils.isNotEmpty(payWayIds)) {
                    for (String arr : payWayIds) {
                        if (channels != null && !channels.isEmpty()) {
                            for (MemberChannelDTO channel : channels) {
                                if (arr.equals(channel.getChannelId())) {
                                    payWays.add(channel.getChannelName());
                                }
                            }
                        }
                    }
                }
                resourceSellerDTO.setPayWays(payWays);
            }
        }
        pageInfo.setList(list);
        return  new ItemResult<PageInfo<ResourceSellerDTO>>(pageInfo);
    }

    @Deprecated(since = "2.1.4-RELEASE")
    @Operation(summary = "资源是否可选择该区域定价")
    @GetMapping(value="/ifCanSelectAreaPrice")
    public ItemResult<Boolean> ifCanSelectAreaPrice(@Parameter(name = "sellerId", description = "卖家Id") @Valid @RequestParam String sellerId,
                                                    @Parameter(name = "goodsId", description = "商品Id") @Valid @RequestParam String goodsId,
                                                    @Parameter(name = "level", description = "层级") @Valid @RequestParam Integer level){
        return  new ItemResult<Boolean>(resourceService.ifCanSelectAreaPrice(sellerId,goodsId,level));
    }

    @Deprecated(since = "2.1.4-RELEASE")
    @Operation(summary = "查询卖家定价模式")
    @GetMapping(value="/getPriceMode")
    public ItemResult<List<PriceModeDTO>> getPriceMode(@Parameter(hidden = true) LoginInfo loginInfo,
                                                       @Parameter(name = "sellerId", description = "卖家Id") @Valid @RequestParam String sellerId){
        List<PriceModeDTO> result = resourceService.getPriceMode(loginInfo.getMemberId());
        return new ItemResult<List<PriceModeDTO>>(result);
    }

    @Deprecated(since = "2.1.4-RELEASE")
    @Operation(summary = "查询商品的资源仓库详情")
    @GetMapping(value="/getGoodsResourceStore")
    public ItemResult<GoodsPriceDTO> getGoodsResourceStore(@Parameter(hidden = true) LoginInfo loginInfo,
                                                           @Parameter(name = "level", description = "层级") @Valid @RequestParam Integer level,
                                                           @Parameter(name = "goodsId", description = "商品Id") @Valid @RequestParam String goodsId,
                                                           @Parameter(name = "sellerId", description = "卖家Id") @Valid @RequestParam String sellerId){
        checkLogin(loginInfo);
        GetGoodsResourceStoreDTO getGoodsResourceStoreDTO = new GetGoodsResourceStoreDTO();
        getGoodsResourceStoreDTO.setLevel(level);
        getGoodsResourceStoreDTO.setGoodsId(goodsId);
        getGoodsResourceStoreDTO.setSellerId(loginInfo.getMemberId());
        getGoodsResourceStoreDTO.setAccountId(loginInfo.getAccountId());
        getGoodsResourceStoreDTO.setFilterIdList(loginInfo.getSaleRegionIdList());
        log.info("===getGoodsResourceStore=req={}", JSON.toJSONString(getGoodsResourceStoreDTO));
        GoodsPriceDTO result = resourceService.getGoodsResourceStore(getGoodsResourceStoreDTO);
        return new ItemResult<GoodsPriceDTO>(result);
    }

    @Operation(summary = "创建资源(立即挂牌)")
    @PostMapping(value="/createResource")
    public ItemResult<Boolean> createResource(@Parameter(hidden = true) LoginInfo loginInfo, @Valid @RequestBody CreateResourceDTO reqCreateResourceDTO){
        reqCreateResourceDTO.setSellerId(loginInfo.getMemberId());
        reqCreateResourceDTO.setSalesId(loginInfo.getAccountId());
        reqCreateResourceDTO.setContactPhone(loginInfo.getMobile());
        if( reqCreateResourceDTO.getIfUp() != null && reqCreateResourceDTO.getIfUp() && reqCreateResourceDTO.getFixUptime() == null ){
            throw new BizException(BasicCode.CUSTOM_ERROR,"定时上架时间不可为空");
        }
        resourceListingService.createResource(reqCreateResourceDTO, loginInfo.getAccountId());
        return new ItemResult<>(true);
    }

    @Operation(summary = "创建资源(预约挂牌)")
    @PostMapping(value="/createResourceTask")
    public ItemResult<Boolean> createResourceTask(@Parameter(hidden = true) LoginInfo loginInfo, @Valid @RequestBody CreateResourceDTO reqCreateResourceDTO){
        reqCreateResourceDTO.setSellerId(loginInfo.getMemberId());
        reqCreateResourceDTO.setSalesId(loginInfo.getAccountId());
        reqCreateResourceDTO.setContactPhone(loginInfo.getMobile());
        reqCreateResourceDTO.setIfUp(true);
        if( reqCreateResourceDTO.getFixUptime() == null ){
            throw new BizException(BasicCode.CUSTOM_ERROR,"定时上架时间不可为空");
        }
        resourceListingService.createResource(reqCreateResourceDTO, loginInfo.getAccountId());
        return new ItemResult<>(true);
    }

    @Operation(summary = "批量更新资源")
    @PostMapping(value="/batchUpdateResource")
    public ItemResult<Boolean> batchUpdateResource(@Parameter(hidden = true) LoginInfo loginInfo,
                                                   @RequestBody BatchUpdateResourceDTO batchUpdateResourceDTO){
        batchUpdateResourceDTO.setSellerId(loginInfo.getMemberId());
        batchUpdateResourceDTO.setOperator(loginInfo.getAccountId());
        resourceListingService.batchUpdateResource(batchUpdateResourceDTO);
        return new ItemResult<>(true);
    }

    @Operation(summary = "修改资源")
    @PostMapping(value="/updateResource")
    public ItemResult<Boolean> updateResource(@Parameter(hidden = true) LoginInfo loginInfo, @Valid @RequestBody UpdateResourceDTO resourceDTO){
        checkLogin(loginInfo);
        resourceListingService.updateResource(resourceDTO,loginInfo.getAccountId());
        return new ItemResult<Boolean>(true);
    }

    @Operation(summary = "删除资源")
    @PostMapping(value="/deleteResource")
    public ItemResult<Boolean> deleteResource(@Parameter(hidden = true) LoginInfo loginInfo,
                                              @Parameter(name = "resourceIds", description = "资源Id集合") @Valid @RequestParam List<String> resourceIds,
                                              @Parameter(name = "operator", description = "操作人") @RequestParam String operator){
        checkLogin(loginInfo);
        if (CsStringUtils.isEmpty(operator)) {
            operator = loginInfo.getAccountId();
        }
        resourceListingService.deleteResource(resourceIds,operator);
        return new ItemResult<Boolean>(true);
    }

    @Operation(summary = "获取资源详情，订单不可使用此接口来获取资源详情")
    @GetMapping(value="/getResourceDetail")
    public  ItemResult<ResourceDTO> getResourceDetail(@Parameter(name = "resourceId", description = "资源Id") @Valid @RequestParam String resourceId){
        return  new ItemResult<ResourceDTO>(resourceQueryService.getComplexResourceDetail(resourceId));
    }

    @Operation(summary = "获取改量改价列表")
    @GetMapping(value="/getChangePriceAndNumList")
    public  ItemResult<List<ChangePriceAndNumDTO>> getChangePriceAndNumList(@Parameter(hidden = true) LoginInfo loginInfo,
                                                                            @Parameter(name = "resourceIds", description = "资源Id集合") @Valid @RequestParam List<String> resourceIds,
                                                                            @Parameter(name = "operator", description = "操作人") @RequestParam String operator){
        checkLogin(loginInfo);
        if (CsStringUtils.isEmpty(operator)) {
            operator = loginInfo.getMemberId();
        }
        return  new ItemResult<List<ChangePriceAndNumDTO>>(resourceService.getChangePriceAndNumList(resourceIds,operator));
    }

    @Operation(summary = "获取历史修改记录")
    @GetMapping(value="/getChangeHistoryList")
    public  ItemResult<List<ChangeHistoryDTO>> getChangeHistoryList(@Parameter(hidden = true) LoginInfo loginInfo,
                                                                    @Parameter(name = "resourceId", description = "资源Id") @Valid @RequestParam String resourceId,
                                                                    @Parameter(name = "operator", description = "操作人") @RequestParam String operator){
        checkLogin(loginInfo);
        return  new ItemResult<>(resourceService.getChangeHistoryList(resourceId,loginInfo.getMemberId()));
    }

    @Operation(summary = "改量改价(单个或批量)")
    @PostMapping(value="/changePriceAndNum")
    public ItemResult<Boolean> changePriceAndNum(@Parameter(hidden = true) LoginInfo loginInfo,
                                                 @Valid @RequestBody ReqChangePriceAndNumDTO reqChangePriceAndNumDTO,
                                                 @Parameter(name = "operator", description = "操作人") @RequestParam String operator){
        checkLogin(loginInfo);
        if (CsStringUtils.isEmpty(operator)) {
            operator = loginInfo.getAccountId();
        }
        resourceService.changePriceAndNum(reqChangePriceAndNumDTO,operator);
        return new ItemResult<Boolean>(true);
    }

    @Operation(summary = "撤销未生效资源修改")
    @PostMapping(value="/repealResourceUpdate")
    public ItemResult<Boolean> repealResourceUpdate(@Parameter(hidden = true) LoginInfo loginInfo,
                                                    @Parameter(name = "resourceHistoryId", description = "资源历史Id") @Valid @RequestParam String resourceHistoryId,
                                                    @Parameter(name = "operator", description = "操作人") @RequestParam String operator){
        checkLogin(loginInfo);
        if (CsStringUtils.isEmpty(operator)) {
            operator = loginInfo.getAccountId();
        }
        resourceService.repealResourceUpdate(resourceHistoryId,operator);
        return new ItemResult<Boolean>(true);
    }

    @Operation(summary = "批量平台强制上架")
    @PostMapping(value="/onSaleResourceBatchPlatform")
    public ItemResult<Boolean> onSaleResourceBatchPlatform(@Parameter(hidden = true) LoginInfo loginInfo,
                                                           @Parameter(name = "resourceIds", description = "资源Id集合") @Valid @RequestParam List<String> resourceIds,
                                                           @Parameter(name = "operator", description = "操作人") @RequestParam String operator){
        checkLogin(loginInfo);
        ManualOnSaleDTO manualOnSaleDTO = new ManualOnSaleDTO();
        manualOnSaleDTO.setResourceIds(resourceIds);
        manualOnSaleDTO.setOperator(loginInfo.getAccountId());
        manualOnSaleDTO.setSellerId(loginInfo.getMemberId());
        resourceListingService.mandatoryOnSale(manualOnSaleDTO);
        return  new ItemResult<Boolean>(true);
    }

    @Operation(summary = "批量平台强制下架")
    @PostMapping(value="/offSaleResourceBatchPlatform")
    public ItemResult<Boolean> offSaleResourceBatchPlatform(@Parameter(hidden = true) LoginInfo loginInfo,
                                                            @Parameter(name = "resourceIds", description = "资源Id集合") @Valid @RequestParam List<String> resourceIds,
                                                            @Parameter(name = "operator", description = "操作人") @RequestParam String operator){
        checkLogin(loginInfo);
        if (CsStringUtils.isEmpty(operator)) {
            operator = loginInfo.getAccountId();
        }
        resourceListingService.mandatoryOffSale(resourceIds,operator);
        return  new ItemResult<Boolean>(true);
    }

    @Operation(summary = "批量上架资源")
    @PostMapping(value="/onSaleResourceBatch")
    public ItemResult<Boolean> onSaleResourceBatch(@Parameter(hidden = true) LoginInfo loginInfo,
                                                   @Parameter(name = "resourceIds", description = "资源Id集合") @Valid @RequestParam List<String> resourceIds,
                                                   @Parameter(name = "operator", description = "操作人") @RequestParam String operator){
        checkLogin(loginInfo);
        ManualOnSaleDTO manualOnSaleDTO = new ManualOnSaleDTO();
        manualOnSaleDTO.setResourceIds(resourceIds);
        manualOnSaleDTO.setOperator(loginInfo.getAccountId());
        manualOnSaleDTO.setSellerId(loginInfo.getMemberId());
        resourceListingService.manualOnSale(manualOnSaleDTO);
        return  new ItemResult<Boolean>(true);
    }

    @Operation(summary = "批量下架/撤牌资源")
    @PostMapping(value="/offSaleResourceBatch")
    public ItemResult<Boolean> offSaleResourceBatch(@Parameter(hidden = true) LoginInfo loginInfo,
                                                    @Parameter(name = "resourceIds", description = "资源Id集合") @Valid @RequestParam List<String> resourceIds,
                                                    @Parameter(name = "operator", description = "操作人") @RequestParam String operator){
        checkLogin(loginInfo);
        if(loginInfo.getMemberId() == null){
            log.error("not find memberId");
            throw new BizException(BasicCode.TOKEN_ERROR);
        }
        if (CsStringUtils.isEmpty(operator)) {
            operator = loginInfo.getMemberId();
        }
        resourceListingService.manualOffSale(resourceIds,operator);
        return  new ItemResult<Boolean>(true);
    }

    @Deprecated(since = "2.1.4-RELEASE")
    @Operation(summary = "提示已挂牌资源")
    @PostMapping(value="/promptOnsaleResource")
    public ItemResult<List<PromptOnsaleResourceDTO>> promptOnsaleResource(@Parameter(hidden = true) LoginInfo loginInfo, @Valid @RequestBody ReqPromptOnsaleResourceDTO resourceDTO){
        checkLogin(loginInfo);
        resourceDTO.setRegionList(loginInfo.getSaleRegionIdList());
        log.info("promptOnsaleResource,req:{}", resourceDTO.toString());
        return  new ItemResult<List<PromptOnsaleResourceDTO>>(resourceService.promptOnsaleResource(resourceDTO));
    }

    private void checkLogin(LoginInfo loginInfo){
        if (loginInfo == null || CsStringUtils.isBlank(loginInfo.getAccountId())) {
            throw new BizException(MemberCode.LOGIN_ERROR,"你还没有登录，请先登录");
        }
        if (CsStringUtils.isBlank(loginInfo.getMemberId())) {
            throw new BizException(MemberCode.LOGIN_ERROR,"你还没有登录，请先登录");
        }
    }

}
