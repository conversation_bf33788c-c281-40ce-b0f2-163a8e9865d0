package com.ecommerce.web.seller.controller;


import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.base.api.dto.authRes.RoleAuthenticateDTO;
import com.ecommerce.base.api.dto.role.AccountRoleDTO;
import com.ecommerce.base.api.dto.role.MemberRoleDTO;
import com.ecommerce.base.api.dto.role.RoleDTO;
import com.ecommerce.base.api.dto.role.RoleQueryDTO;
import com.ecommerce.base.api.service.IRoleService;
import com.ecommerce.base.api.service.IValueSetService;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.member.api.dto.account.AccountSimpleDTO;
import com.ecommerce.member.api.dto.member.MemberSimpleDTO;
import com.ecommerce.member.api.service.IAccountService;
import com.ecommerce.member.api.service.IMemberService;
import com.ecommerce.member.api.session.LoginInfo;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 角色相关service
 *
 * <AUTHOR>
*/

@RestController
@Tag(name = "Role", description = "角色相关service")
@RequestMapping("/role")
@CrossOrigin
public class RoleController {

   @Autowired
   private IRoleService iRoleService;
   @Autowired
   private IAccountService accountService;
   @Autowired
   private IMemberService memberService;
   @Autowired
   private IValueSetService valueSetService;

   private static final String SELLER = "seller";

   @Operation(summary = "创建role")
   @PostMapping(value="/createRole")
   public ItemResult<Object> createRole(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody RoleDTO role){
      role.setCreateUser(loginInfo.getAccountId());
      role.setCreateMemberId(loginInfo.getMemberId());
      return new ItemResult<>(iRoleService.createRole(role));
   }

   @Operation(summary = "根据id删除角色")
   @PostMapping(value="/deleteRoleById")
   public ItemResult<Object> deleteRoleById(@Parameter(hidden = true) LoginInfo loginInfo,@Parameter(name = "id", description = "角色Id") @RequestParam Integer id){
      RoleDTO roleDTO = iRoleService.findById(id);
       if (roleDTO != null && CsStringUtils.equals(loginInfo.getMemberId(), roleDTO.getCreateMemberId())) {
         iRoleService.deleteRoleById(id, loginInfo.getAccountId());
      }
      return new ItemResult<>(new Object());
   }

   @Operation(summary = "更新角色")
   @PostMapping(value="/updateRole")
   public ItemResult<Object> updateRole(@Parameter(hidden = true) LoginInfo loginInfo,@RequestBody RoleDTO roleDTO){
      RoleDTO role = iRoleService.findById(roleDTO.getRoleId());
       if (role != null && CsStringUtils.equals(loginInfo.getMemberId(), role.getCreateMemberId())) {
         roleDTO.setUpdateUser(loginInfo.getAccountId());
         iRoleService.updateRole(roleDTO);
      }
      return new ItemResult<>(new Object());
   }

   @Operation(summary = "翻页查询")
   @PostMapping(value="/findAll")
   public ItemResult<PageInfo<RoleDTO>> findAll(@Parameter(hidden = true) LoginInfo loginInfo,@RequestBody RoleQueryDTO dto){
      return new ItemResult<>(iRoleService.findAll(dto));
   }

   @Operation(summary = "根据id查询")
   @GetMapping(value="/findById")
   public ItemResult<RoleDTO> findById(@Parameter(name = "roleId", description = "角色Id") Integer roleId){
      return new ItemResult<>(iRoleService.findById(roleId));
   }

   @Operation(summary = "根据id查询")
   @GetMapping(value="/findByIds")
   public ItemResult<List<RoleDTO>> findByIds(@RequestBody List<Integer> roleIds){
      return new ItemResult<>(iRoleService.findByIds(roleIds));
   }

   @Operation(summary = "根据角色代码查询")
   @GetMapping(value="/findByRoleName")
   public ItemResult<RoleDTO> findByRoleName(@Parameter(name = "roleName", description = "角色名称") String roleName){
      return new ItemResult<>(iRoleService.findByRoleName(roleName));
   }

   @Operation(summary = "根据角色代码查询")
   @GetMapping(value="/findByRoleNameList")
   public ItemResult<List<RoleDTO>> findByRoleName(@RequestBody List<String> roleNameList){
      return new ItemResult<>(iRoleService.findByRoleNameList(roleNameList));
   }


   @Operation(summary = "根据账户id查询已分配的角色")
   @PostMapping(value="/getRoleByAccountId")
   public ItemResult<AccountRoleDTO> getRoleByAccountId(@Parameter(hidden = true) LoginInfo loginInfo,
                                                        @Parameter(name = "accountId", description = "账号Id") @RequestParam String accountId,
                                                        @Parameter(name = "platform", description = "角色类型") @RequestParam(defaultValue = "seller") String platform){
      AccountRoleDTO dto = new AccountRoleDTO();
      dto.setAccountId(accountId);
      dto.setMemberId(loginInfo.getMemberId());
       dto.setPlatform(CsStringUtils.isBlank(platform) || SELLER.equals(platform) ? SELLER : "supplier");
      AccountSimpleDTO accountSimpleDTO = accountService.findSimpleById(accountId);
      dto = iRoleService.getRoleByAccountId2(dto);
      if( accountSimpleDTO != null ){
         dto.setAccountId(accountId);
         dto.setAccountName( accountSimpleDTO.getAccountName());
         dto.setMemberName( accountSimpleDTO.getMemberName() );
         dto.setRealName ( accountSimpleDTO.getRealName() );
         dto.setEmployeeId( accountSimpleDTO.getEmployeeId() );
         dto.setMemberCode( accountSimpleDTO.getMemberCode() );
         dto.setAccountCode( accountSimpleDTO.getAccountCode() );
         dto.setMobile( accountSimpleDTO.getMobile() );
         dto.setAccountType(accountSimpleDTO.getAccountType());
      }
      return new ItemResult<>(dto);
   }

   @Operation(summary = "获取会员可用的权限")
   @PostMapping(value="/getRoleByMemberId")
   public ItemResult<MemberRoleDTO> getRoleByMemberId(@Parameter(name = "memberId", description = "会员Id") @RequestParam String memberId,
                                                      @Parameter(name = "platform", description = "角色类型") @RequestParam(defaultValue = "seller") String platform){
      MemberRoleDTO dto = new MemberRoleDTO();
      dto.setMemberId(memberId);
       dto.setPlatform(CsStringUtils.isBlank(platform) || SELLER.equals(platform) ? SELLER : "supplier");
      MemberSimpleDTO memberSimpleDTO = memberService.findMemberSimpleById(memberId);
      dto = iRoleService.getRoleByMemberId2(dto);
      if( dto != null && memberSimpleDTO != null ){
         dto.setMemberCode(memberSimpleDTO.getMemberCode());
         dto.setMemberName(memberSimpleDTO.getMemberName());
         dto.setMemberType(memberSimpleDTO.getSellerType());
         String memberType = memberSimpleDTO.getMemberType();
          if (CsStringUtils.isBlank(memberType) || CsStringUtils.equals(memberType, "101")) {
            dto.setMemberType("个人买家");
          } else if (CsStringUtils.equals(memberType, "102")) {
            dto.setMemberType("个人司机");
         }else if(memberType.startsWith("2")){
            memberType = "企业买家";
            if( memberSimpleDTO.getSellerFlg() != null && memberSimpleDTO.getSellerFlg().intValue() == 1 ){
               memberType += "、企业卖家";
            }
            if( memberSimpleDTO.getCarrierFlg() != null && memberSimpleDTO.getCarrierFlg().intValue() == 1 ){

               memberType += "、承运商";
            }
            if( memberSimpleDTO.getSupplierFlg() != null && memberSimpleDTO.getSupplierFlg().intValue() == 1 ){
               memberType += "、供应商";
            }
            dto.setMemberType(memberType);
         }
      }
      return new ItemResult<>(dto);
   }

   @Operation(summary = "获取所有角色，不分页，按会员id查")
   @PostMapping(value="/getAllRoleListAndMemberSelected")
   public ItemResult<List<RoleDTO>> getAllRoleListAndMemberSelected(@Parameter(hidden = true) LoginInfo loginInfo,
                                                                    @Parameter(name = "memberId", description = "会员Id") @RequestParam String memberId){
      return new ItemResult<>(iRoleService.getAllRoleListAndMemberSelected(memberId));
   }

   @Operation(summary = "更新账户角色")
   @PostMapping(value="/updateRoleByAccountId")
   public ItemResult<Object> updateRoleByAccountId(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody AccountRoleDTO dto){
      dto.setOperatorId(loginInfo.getAccountId());
      iRoleService.updateAccountRole(dto);
      return new ItemResult<>(new Object());
   }

   @Operation(summary = "更新会员角色")
   @PostMapping(value="/updateMemberRole")
   public ItemResult<Object> updateMemberRole(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody MemberRoleDTO dto){
      dto.setOperatorId(loginInfo.getAccountId());
      iRoleService.updateMemberRole(dto);
      return new ItemResult<>(new Object());
   }

   @Operation(summary = "根据id查询角色和角色资源")
   @GetMapping(value="/findRoleAndPermission")
   public ItemResult<RoleAuthenticateDTO> findRoleAndPermission(@Parameter(name = "roleId", description = "角色Id") Integer roleId) {
      return new ItemResult<>(iRoleService.findRoleAndPermission(roleId));
   }
}
