package com.ecommerce.web.seller.controller;

import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.exception.BusinessOperationException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.member.api.dto.base.PageQuery;
import com.ecommerce.member.api.dto.relation2.RelationExistsDTO;
import com.ecommerce.member.api.dto.relation2.SellerCarrierBuyerRelationCreateDTO;
import com.ecommerce.member.api.dto.relation2.SellerCarrierBuyerRelationDTO;
import com.ecommerce.member.api.dto.relation2.SellerCarrierBuyerRelationDeleteDTO;
import com.ecommerce.member.api.dto.relation2.SellerCarrierBuyerRelationQueryDTO;
import com.ecommerce.member.api.service.ISellerCarrierBuyerRelationService;
import com.ecommerce.member.api.session.LoginInfo;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@RestController
@Tag(name = "SellerCarrierBuyerRelation", description = "卖家-承运商-买家关系")
@RequestMapping("/sellerCarrierBuyerRelation")
public class SellerCarrierBuyerRelationController {

    @Autowired
    private ISellerCarrierBuyerRelationService sellerCarrierBuyerRelationService;

    @Operation(summary = "删除关系")
    @PostMapping(value="/delete")
    public ItemResult<Boolean> delete(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody SellerCarrierBuyerRelationDeleteDTO deleteDTO){
        if(deleteDTO == null ){
            throw new BizException(BasicCode.CUSTOM_ERROR,"入参不能为空");
        }
        deleteDTO.setSellerId(loginInfo.getMemberId());
        deleteDTO.setOperator(loginInfo.getAccountId());
        return new ItemResult<>(sellerCarrierBuyerRelationService.delete(deleteDTO));
    }

    @Operation(summary = "批量新增关系")
    @PostMapping(value="/create")
    public ItemResult<Boolean> create(@Parameter(hidden = true) LoginInfo loginInfo,@RequestBody List<SellerCarrierBuyerRelationCreateDTO> createList){
        if(CollectionUtils.isEmpty(createList)){
            throw new BizException(BasicCode.CUSTOM_ERROR,"入参不能为空");
        }
        for (SellerCarrierBuyerRelationCreateDTO sellerCarrierBuyerRelationCreateDTO : createList) {
            sellerCarrierBuyerRelationCreateDTO.setSellerId(loginInfo.getMemberId());
            sellerCarrierBuyerRelationCreateDTO.setOperator(loginInfo.getAccountId());
        }
        return new ItemResult<>(sellerCarrierBuyerRelationService.create(createList));
    }

    @Operation(summary = "关系是否存在")
    @PostMapping(value="/relationExists")
    public ItemResult<Boolean> relationExists(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody RelationExistsDTO dto){
        dto.setSellerId(loginInfo.getMemberId());
        return new ItemResult<>(sellerCarrierBuyerRelationService.relationExists(dto));
    }

    @Operation(summary = "翻页查询")
    @PostMapping(value="/findAll")
    public ItemResult<PageInfo<SellerCarrierBuyerRelationDTO>> findAll(@Parameter(hidden = true) LoginInfo loginInfo,@RequestBody PageQuery<SellerCarrierBuyerRelationQueryDTO> query){
        query.setQueryDTO(query.getQueryDTO() == null ? new SellerCarrierBuyerRelationQueryDTO() : query.getQueryDTO());
        query.getQueryDTO().setSellerId(loginInfo.getMemberId());
        query.getQueryDTO().setSellerName(null);
        return new ItemResult<>(sellerCarrierBuyerRelationService.findAll(query));
    }

}
