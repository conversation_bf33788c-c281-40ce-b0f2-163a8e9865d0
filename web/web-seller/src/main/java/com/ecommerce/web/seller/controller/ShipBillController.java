package com.ecommerce.web.seller.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.ecommerce.base.api.dto.authRes.DataPrivCodeEnum;
import com.ecommerce.base.api.dto.authRes.ResCheckDTO;
import com.ecommerce.base.api.enums.ResourceTypeEnum;
import com.ecommerce.base.api.service.IDatapermService;
import com.ecommerce.base.api.service.IRoleService;
import com.ecommerce.common.enums.AppNames;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.exception.BusinessOperationException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.common.service.RedisService;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.goods.api.dto.ResourceDTO;
import com.ecommerce.goods.api.dto.contract.TrContractDTO;
import com.ecommerce.goods.api.service.IContractService;
import com.ecommerce.goods.api.service.IResourceService;
import com.ecommerce.logistics.api.dto.DeliveryDetailVehicleDTO;
import com.ecommerce.logistics.api.dto.GoodsInfoDTO;
import com.ecommerce.logistics.api.dto.ShipBillWaitRerouteListDTO;
import com.ecommerce.logistics.api.dto.WaitRerouteShipBillCondDTO;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.pickingbill.TwoLeaveOptionDTO;
import com.ecommerce.logistics.api.dto.shipbill.CancelShipBillDTO;
import com.ecommerce.logistics.api.dto.shipbill.ConcreteShipBillInfoDTO;
import com.ecommerce.logistics.api.dto.shipbill.QrCodeDTO;
import com.ecommerce.logistics.api.dto.shipbill.ReassignShipBillDTO;
import com.ecommerce.logistics.api.dto.shipbill.ShipBillDetailDTO;
import com.ecommerce.logistics.api.dto.shipbill.ShipBillListDTO;
import com.ecommerce.logistics.api.dto.shipbill.ShipBillQueryDTO;
import com.ecommerce.logistics.api.dto.shipbill.ShipWaybillListDTO;
import com.ecommerce.logistics.api.dto.shipbill.ShipWaybillQueryDTO;
import com.ecommerce.logistics.api.dto.shipbill.TransportInfoDTO;
import com.ecommerce.logistics.api.dto.waybill.CompleteShipBillDTO;
import com.ecommerce.logistics.api.dto.waybill.LeaveWarehouseDTO;
import com.ecommerce.logistics.api.dto.waybill.OpenCabinShipBillDTO;
import com.ecommerce.logistics.api.dto.waybill.QueryGoodsDTO;
import com.ecommerce.logistics.api.dto.waybill.RefundShipBillDTO;
import com.ecommerce.logistics.api.dto.waybill.TradeWaybillDTO;
import com.ecommerce.logistics.api.enums.ShipBillOperateEnum;
import com.ecommerce.logistics.api.enums.ShipBillStatusEnum;
import com.ecommerce.logistics.api.enums.TransportToolTypeEnum;
import com.ecommerce.logistics.api.service.IShipBillService;
import com.ecommerce.member.api.dto.account.AccountDTO;
import com.ecommerce.member.api.dto.member.config.MemberConfigDTO;
import com.ecommerce.member.api.service.IMemberConfigService;
import com.ecommerce.member.api.session.LoginInfo;
import com.ecommerce.order.api.dto.OrderDTO;
import com.ecommerce.order.api.service.IOrderService;
import com.ecommerce.report.api.dto.ReportShipBillQueryDTO;
import com.ecommerce.report.api.dto.ReportWaybillResultDTO;
import com.ecommerce.report.api.dto.redis.ReportRedisKeys;
import com.ecommerce.report.api.service.IReportService;
import com.ecommerce.web.seller.util.SellerWaybillExcelUtil;
import com.google.common.collect.Lists;
import feign.Response;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.InputStreamSource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.InputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


@Slf4j
@Tag(name = "ShipBillController", description = ": 运单服务接口")
@RestController
@RequestMapping("/shipBill")
public class ShipBillController {

    @Autowired
    private IShipBillService shipBillService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private IReportService reportService;

    @Autowired
    private IOrderService orderService;

    @Autowired
    private IContractService contractService;

    @Autowired
    private IResourceService resourceService;

    @Autowired
    private IDatapermService datapermService;

    @Autowired
    private IMemberConfigService memberConfigService;

    private static final String LICENSE_PLATE_NUMBER_ONE = "(^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼]{1})";

    @Autowired
    private IRoleService roleService;

    @Deprecated(since = "2.1.4-RELEASE")
    @Operation(summary = "运单列表分页查询")
    @PostMapping(value = "/queryShipBillList")
    public ItemResult<PageData<ShipBillListDTO>> queryShipBillList(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody PageQuery<ShipBillQueryDTO> pageQuery) {
        ShipBillQueryDTO queryDTO = pageQuery.getQueryDTO() == null ? new ShipBillQueryDTO() : pageQuery.getQueryDTO();
        queryDTO.setSellerIdFrom(loginInfo.getMemberId());
        queryDTO.setQueryAppName(AppNames.WEB_SERVICE_SELLER.getCode());
        pageQuery.setQueryDTO(queryDTO);

        ItemResult<PageData<ShipBillListDTO>> pageDataItemResult = shipBillService.queryShipBillList(pageQuery);
        if (loginInfo.getAccountType() == AccountDTO.ACCOUNT_TYPE_MEMBER_SUB &&
                pageDataItemResult != null && pageDataItemResult.getData() != null && CollectionUtils.isNotEmpty(pageDataItemResult.getData().getList())) {
            ItemResult<List<String>> accountStoreIdListResult = datapermService.findByAccountIdAndDataPrivCode(loginInfo.getAccountId(), DataPrivCodeEnum.STOREHOUSE.getCode());
            List<String> canLeaveWarehouseIdList = Lists.newArrayList();
            if (accountStoreIdListResult != null && CollectionUtils.isNotEmpty(accountStoreIdListResult.getData())) {
                canLeaveWarehouseIdList = accountStoreIdListResult.getData();
            }
            for (ShipBillListDTO shipBillListDTO : pageDataItemResult.getData().getList()) {
                List<String> btnList = shipBillListDTO.getBtnList();
                if (btnList != null && !canLeaveWarehouseIdList.contains(shipBillListDTO.getWarehouseId())) {
                    btnList.remove(ShipBillOperateEnum.LEAVE_WAREHOUSE.getCode());
                }
            }

            if (loginInfo.getSalesman()) {
                queryDTO.setSalesmanId(loginInfo.getAccountId());
            }

        }

        return pageDataItemResult;

    }

    @Operation(summary = "获取运单状态列表")
    @GetMapping(value = "/getShipBillStatusList")
    public ItemResult<List<String>> getShipBillStatusList() {
        List<String> statusList = new ArrayList<>();
        statusList.add("");
        statusList.add(ShipBillStatusEnum.WAIT_AUDIT.getCode());
        statusList.add(ShipBillStatusEnum.WAIT_RECEIVE.getCode());
        statusList.add(ShipBillStatusEnum.WAIT_ASSIGN.getCode());
        statusList.add(ShipBillStatusEnum.CANCELED.getCode());
        statusList.add(ShipBillStatusEnum.WAIT_CONFIRM.getCode());
        statusList.add(ShipBillStatusEnum.WAIT_DELIVERY.getCode());
        statusList.add(ShipBillStatusEnum.DELIVERING.getCode());
        statusList.add(ShipBillStatusEnum.SIGNED.getCode());
        statusList.add(ShipBillStatusEnum.COMPLETE.getCode());
        statusList.add(ShipBillStatusEnum.CLOSED.getCode());
        statusList.add(ShipBillStatusEnum.REFUND.getCode());
        return new ItemResult<>(statusList);
    }

    @Operation(summary = "根据委托单Id查询该委托单自己承运的运单")
    @PostMapping(value = "/queryShipBillByDeliveryBillId")
    public ItemResult<List<DeliveryDetailVehicleDTO>> queryShipBillByDeliveryBillId(@Parameter(description = "委托单Id") @RequestParam String deliveryBillId) {
        return shipBillService.queryShipBillByDeliveryBillId(deliveryBillId);
    }

    @Operation(summary = "获取运单详情")
    @GetMapping(value = "/getWaybillDetail")
    public ItemResult<ShipBillDetailDTO> getWaybillDetail(@Parameter(description = "运单子项ID") @RequestParam String waybillItemId) {
        return shipBillService.getWaybillDetail(waybillItemId);
    }

    @Operation(summary = "出站")
    @PostMapping(value = "/inputLeaveWarehouse")
    public ItemResult<Void> inputLeaveWarehouse(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody LeaveWarehouseDTO leaveWarehouseDTO) {
        leaveWarehouseDTO.setUserId(loginInfo.getAccountId());
        leaveWarehouseDTO.setUserName(loginInfo.getAccountName());
        return shipBillService.inputLeaveWarehouse(leaveWarehouseDTO);
    }

    @Operation(summary = "开仓(如果是一级开仓,将尝试开仓二级)")
    @PostMapping(value = "/openCabin")
    public ItemResult<Boolean> openCabin(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody OpenCabinShipBillDTO openCabinShipBillDTO) {
        openCabinShipBillDTO.setOperationUserId(loginInfo.getAccountId());
        openCabinShipBillDTO.setOperationUserName(loginInfo.getAccountName());
        return shipBillService.openCabin(openCabinShipBillDTO);
    }

    @Operation(summary = "完成运单")
    @PostMapping(value = "/completeShipBill")
    public ItemResult<Void> completeShipBill(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody CompleteShipBillDTO completeShipBillDTO) {
        completeShipBillDTO.setOperationUserId(loginInfo.getAccountId());
        completeShipBillDTO.setOperationUserName(loginInfo.getAccountName());
        return shipBillService.completeShipBill(completeShipBillDTO);
    }

    @Operation(summary = "完成")
    @PostMapping(value = "/completeConcrete")
    public ItemResult<Void> completeConcrete(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody CompleteShipBillDTO completeShipBillDTO) {
        //的完成,需要计算空载费
        ConcreteShipBillInfoDTO concreteShipBillInfoDTO = shipBillService.queryConcreteInfoByWaybillId(completeShipBillDTO.getWaybillId());
        if (concreteShipBillInfoDTO == null) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "没有找到签收的信息");
        }
        completeShipBillDTO.setOperationUserId(loginInfo.getAccountId());
        completeShipBillDTO.setOperationUserName(loginInfo.getAccountName());

        ItemResult<List<TradeWaybillDTO>> tradeWaybillDTOListResult = shipBillService.queryWaybillListByDeliveryNum(concreteShipBillInfoDTO.getTakeCode());
        log.info("查询运单:{}", JSON.toJSONString(tradeWaybillDTOListResult));
        if (tradeWaybillDTOListResult == null || CollectionUtils.isEmpty(tradeWaybillDTOListResult.getData())) {
            throw new BizException(BasicCode.UNDEFINED_ERROR, "运单信息查询失败");
        }





        completeShipBillDTO.setSellerDirectCompleteFlag(1);
        completeShipBillDTO.setSignType(completeShipBillDTO.getSignType());
        return shipBillService.completeShipBill(completeShipBillDTO);
    }

    @Operation(summary = "查询可改航运单列表")
    @PostMapping(value = "/queryWaitRerouteShipBills")
    public ItemResult<PageData<ShipBillWaitRerouteListDTO>> queryWaitRerouteShipBills(@Parameter(hidden = true) LoginInfo loginInfo,
                                                                                      @RequestBody PageQuery<WaitRerouteShipBillCondDTO> pageQuery) {
        if (pageQuery == null) {
            pageQuery = new PageQuery<>();
            pageQuery.setPageSize(20);
            pageQuery.setPageNum(1);
        }

        if (pageQuery.getQueryDTO() == null) {
            pageQuery.setQueryDTO(new WaitRerouteShipBillCondDTO());
        }

        pageQuery.getQueryDTO().setSellerId(loginInfo.getMemberId());

        return shipBillService.queryWaitRerouteShipBills(pageQuery);
    }

    @Operation(summary = "取消运单")
    @PostMapping(value = "/cancelShipBill")
    public ItemResult<Void> cancelShipBill(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody CancelShipBillDTO cancelShipBillDTO) {
        cancelShipBillDTO.setOperateUserId(loginInfo.getAccountId());
        cancelShipBillDTO.setOperateUserName(loginInfo.getAccountName());
        return shipBillService.cancelShipBill(cancelShipBillDTO);
    }

    @Operation(summary = "取消运单的重新指派")
    @PostMapping(value = "/assignCanceledShipBill")
    public ItemResult<Void> assignCanceledShipBill(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody ReassignShipBillDTO reassignShipBillDTO) {
        reassignShipBillDTO.setOperateUserId(loginInfo.getAccountId());
        reassignShipBillDTO.setOperateUserName(loginInfo.getAccountName());
        return shipBillService.assignCanceledShipBill(reassignShipBillDTO);
    }

    @Operation(summary = "查询主运单列表")
    @PostMapping(value = "/queryShipWaybillList")
    public ItemResult<PageData<ShipWaybillListDTO>> queryShipWaybillList(@Parameter(hidden = true) LoginInfo loginInfo,
                                                                         @RequestBody PageQuery<ShipWaybillQueryDTO> pageQuery) {
        ShipWaybillQueryDTO shipWaybillQueryDTO = pageQuery.getQueryDTO() == null ? new ShipWaybillQueryDTO() : pageQuery.getQueryDTO();
        shipWaybillQueryDTO.setQueryAppName(AppNames.WEB_SERVICE_SELLER.getCode());
        shipWaybillQueryDTO.setSellerId(loginInfo.getMemberId());
        if (CsStringUtils.isBlank(shipWaybillQueryDTO.getStatus())) {
            List<String> statusLists = this.getShipBillStatusList().getData();
            statusLists.remove("");
            shipWaybillQueryDTO.setStatusList(statusLists);
        }

        List<String> saleRegionIdList = loginInfo.getSaleRegionIdList();
        log.info("loginType:{}=>{}", loginInfo.getAccountName(), loginInfo.getAccountType());
        shipWaybillQueryDTO.setSaleRegionIdList(null);
        if (loginInfo.getAccountType() == AccountDTO.ACCOUNT_TYPE_MEMBER_SUB) {
            log.info("queryShipWaybillList_saleRegionIdList:" + JSON.toJSONString(saleRegionIdList));
            if (CollectionUtils.isEmpty(saleRegionIdList)) {
                return new ItemResult<>(new PageData<>(Lists.newArrayList()));
            }
            shipWaybillQueryDTO.setSaleRegionIdList(saleRegionIdList);
            if (loginInfo.getSalesman()) {
                shipWaybillQueryDTO.setSalesmanId(loginInfo.getAccountId());
            }
        }
        //2021.0.26  begin -- 如果查询类型为空，则根据角色默认查询类型  --问题修改：卖家中心-我的物流-提货管理-我的调度管理、我的运单管理：汽运和船运业务板块未分开，我的调度管理和我的运单管理船运和汽运数据混合一块，对于专门的船运岗位来说，多出的汽运数据容易混淆视线。
        if (CollectionUtils.isNotEmpty(loginInfo.getRoleNameList())) {
            int viewAll = 0;
            String type = shipWaybillQueryDTO.getTransportType();
            if (loginInfo.getRoleNameList().stream().anyMatch(item -> CsStringUtils.equals(item, "waybill_view_road"))) {//汽运运单数据查看角色
                shipWaybillQueryDTO.setTransportType(TransportToolTypeEnum.ROAD_TRANSPORT.getCode());
                viewAll++;
            }
            if (loginInfo.getRoleNameList().stream().anyMatch(item -> CsStringUtils.equals(item, "waybill_view_water"))) {//船运运单数据查看角色
                shipWaybillQueryDTO.setTransportType(TransportToolTypeEnum.WATER_TRANSPORT.getCode());
                viewAll++;
            }
            if (viewAll > 1) {
                shipWaybillQueryDTO.setTransportType(type);
            }
        }
        //2021.0.26  end -- 如果查询类型为空，则根据角色默认查询类型  --问题修改：卖家中心-我的物流-提货管理-我的调度管理、我的运单管理：汽运和船运业务板块未分开，我的调度管理和我的运单管理船运和汽运数据混合一块，对于专门的船运岗位来说，多出的汽运数据容易混淆视线。

        pageQuery.setQueryDTO(shipWaybillQueryDTO);
        log.info("queryShipWaybillList : {}", JSON.toJSONString(pageQuery));
        ItemResult<PageData<ShipWaybillListDTO>> pageDataItemResult = shipBillService.queryShipWaybillList(pageQuery);

        if (pageDataItemResult != null && pageDataItemResult.getData() != null && CollectionUtils.isNotEmpty(pageDataItemResult.getData().getList())) {
            List<String> canLeaveWarehouseIdList = Lists.newArrayList();
            if (loginInfo.getAccountType() == AccountDTO.ACCOUNT_TYPE_MEMBER_SUB) {
                ItemResult<List<String>> accountStoreIdListResult = datapermService.findByAccountIdAndDataPrivCode(loginInfo.getAccountId(), DataPrivCodeEnum.STOREHOUSE.getCode());
                if (accountStoreIdListResult != null && CollectionUtils.isNotEmpty(accountStoreIdListResult.getData())) {
                    canLeaveWarehouseIdList = accountStoreIdListResult.getData();
                }
            }

            MemberConfigDTO needShipArrivalConfig = memberConfigService.findByMemberIdAndKeyCode(loginInfo.getMemberId(), "need_ship_arrival");
            boolean needArrival = needShipArrivalConfig != null && CsStringUtils.equals(needShipArrivalConfig.getValue(), "1");

            for (ShipWaybillListDTO shipWaybillListDTO : pageDataItemResult.getData().getList()) {
                if (shipWaybillListDTO == null || CollectionUtils.isEmpty(shipWaybillListDTO.getWaybillItemList())) {
                    continue;
                }
                Set<String> btnList = shipWaybillListDTO.getWaybillItemList().get(0).getBtnList();
                if (btnList != null &&
                        !canLeaveWarehouseIdList.contains(shipWaybillListDTO.getWarehouseId()) &&
                        loginInfo.getAccountType() == AccountDTO.ACCOUNT_TYPE_MEMBER_SUB) {
                    btnList.remove(ShipBillOperateEnum.LEAVE_WAREHOUSE.getCode());
                }

                if (needArrival && btnList != null && shipWaybillListDTO.getWaybillItemList().get(0).getArriveDestinationTime() == null) {
                    btnList.remove(ShipBillOperateEnum.OPEN_CABIN_REMIND.getCode());
                    btnList.remove(ShipBillOperateEnum.OPEN_CABIN.getCode());
                }

            }
        }

        return pageDataItemResult;
    }

    @Operation(summary = "船运单卸货提醒")
    @PostMapping(value = "/unloadingRemind")
    public ItemResult<Void> unloadingRemind(@Parameter(description = "运单子项Id") @RequestParam String waybillItemId) {
        return shipBillService.unloadingRemind(waybillItemId);
    }

    @Operation(summary = "下载运单列表")
    @PostMapping("/downloadWaybillList")
    public ItemResult<String> downloadWaybillList(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody ReportShipBillQueryDTO reportWaybillQueryDTO) {
        if (reportWaybillQueryDTO == null) {
            reportWaybillQueryDTO = new ReportShipBillQueryDTO();
        }
        reportWaybillQueryDTO.setOperatorId(loginInfo.getAccountId());
        reportWaybillQueryDTO.setSellerId(loginInfo.getMemberId());
        reportWaybillQueryDTO.setQueryAppName(AppNames.WEB_SERVICE_SELLER.getCode());
        if (CsStringUtils.isBlank(reportWaybillQueryDTO.getStatus())) {
            List<String> statusLists = this.getShipBillStatusList().getData();
            statusLists.remove("");
            reportWaybillQueryDTO.setStatusList(statusLists);
        }
        String key = ReportRedisKeys.WAYBILL_EXPORT + loginInfo.getAccountId();
        if (redisService.hasKey(key)) {
            redisService.del(key);
        }

        final ReportShipBillQueryDTO queryDTO = reportWaybillQueryDTO;
        List<String> saleRegionIdList = loginInfo.getSaleRegionIdList();
        queryDTO.setSaleRegionIdList(null);
        if (loginInfo.getAccountType() == AccountDTO.ACCOUNT_TYPE_MEMBER_SUB) {
            if (CollectionUtils.isEmpty(saleRegionIdList)) {
                //不掉接口时直接在缓存中放一个空数组，避免轮询下载一直返回0000
                redisService.set(key, Lists.newArrayList());
                return new ItemResult<>("OK");
            }
            queryDTO.setSaleRegionIdList(saleRegionIdList);

            if (loginInfo.getSalesman()) {
                queryDTO.setSalesmanId(loginInfo.getAccountId());
            }
        }
        //2021.0.26  begin -- 如果查询类型为空，则根据角色默认查询类型  --问题修改：卖家中心-我的物流-提货管理-我的调度管理、我的运单管理：汽运和船运业务板块未分开，我的调度管理和我的运单管理船运和汽运数据混合一块，对于专门的船运岗位来说，多出的汽运数据容易混淆视线。
        if (CollectionUtils.isNotEmpty(loginInfo.getRoleNameList())) {
            int viewAll = 0;
            String type = queryDTO.getTransportType();
            if (loginInfo.getRoleNameList().stream().anyMatch(item -> CsStringUtils.equals(item, "waybill_view_road"))) {//汽运运单数据查看角色
                queryDTO.setTransportType(TransportToolTypeEnum.ROAD_TRANSPORT.getCode());
                viewAll++;
            }
            if (loginInfo.getRoleNameList().stream().anyMatch(item -> CsStringUtils.equals(item, "waybill_view_water"))) {//船运运运单数据查看角色
                queryDTO.setTransportType(TransportToolTypeEnum.WATER_TRANSPORT.getCode());
                viewAll++;
            }
            if (viewAll > 1) {
                queryDTO.setTransportType(type);
            }
        }
        //2021.0.26  end -- 如果查询类型为空，则根据角色默认查询类型  --问题修改：卖家中心-我的物流-提货管理-我的调度管理、我的运单管理：汽运和船运业务板块未分开，我的调度管理和我的运单管理船运和汽运数据混合一块，对于专门的船运岗位来说，多出的汽运数据容易混淆视线。

        //运单导出操作改为异步
        CompletableFuture.runAsync(() -> {
            try {
                reportService.exportPublishedWaybillList(queryDTO);
            } catch (Exception e) {
                log.error("导出运单异常:{}", e);
            }
        });

        return new ItemResult<>("OK");
    }

    @Operation(summary = "轮询运单下载结果")
    @PostMapping("/getDownloadWaybill")
    public ItemResult<String> getDownloadWaybill(@Parameter(hidden = true) LoginInfo info, @RequestBody List<String> selectHeaders) {
        //从redis中查询
        String key = ReportRedisKeys.WAYBILL_EXPORT + info.getAccountId();
        String result = "";
        if (redisService.hasKey(key)) {
            result = redisService.get(key);
        }
        if (CsStringUtils.isBlank(result)) {
            //缓存中没有报表数据，返回前端0000，让前端继续轮询
            return new ItemResult<>("0000");
        }
        if (CsStringUtils.equals(result, "error")) {
            //查询报表数据时报错，返回前端0001，让前端停止轮询
            return new ItemResult<>("0001");
        }
        if (CsStringUtils.equals(result, "success")) {
            //查询报表数据成功，但上次轮询还未返回前端数据，返回0002让前端停止轮询
            return new ItemResult<>("0002");
        }
        redisService.set(key, "success");
        log.info("开始json转换=============");
        List<ReportWaybillResultDTO> resultDTOList = JSON.parseObject(result, new TypeReference<List<ReportWaybillResultDTO>>() {
        });
        log.info("json转换结束=============");
        ResCheckDTO resCheckDTO = new ResCheckDTO();
        resCheckDTO.setAccountId(info.getAccountId());
        resCheckDTO.setAppName(AppNames.WEB_SERVICE_SELLER.getPlatform());
        resCheckDTO.setResourceType(ResourceTypeEnum.BUTTON.getName());
        //订单列表价格显示的按钮
        resCheckDTO.setCode("3202S01-10");
        //有资源就要隐藏
        boolean needHidePrice = !roleService.checkRes(resCheckDTO);
        return new ItemResult<>(SellerWaybillExcelUtil.htmlExcelFormat(resultDTOList, selectHeaders, needHidePrice));
    }

    @Operation(summary = "获取报表字段")
    @GetMapping("/getReportHeaders")
    public ItemResult<List<String>> getReportHeaders() {
        return new ItemResult<>(SellerWaybillExcelUtil.getHeaders());
    }

    @Operation(summary = "运单下载2")
    @PostMapping("/downloadWaybill")
    public ResponseEntity downloadWaybill(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody ReportShipBillQueryDTO reportWaybillQueryDTO) {
        String key = AppNames.WEB_SERVICE_SELLER.getPlatform() + ":downloadWaybill:" + loginInfo.getAccountId();
        try {
            if (redisService.hasKey(key)) {
                throw new BizException(BasicCode.CUSTOM_ERROR, "已有运单下载任务请勿重复请求，或30秒后再试");
            }
            redisService.set(key, "1");
            redisService.setTimeout(key, 30, TimeUnit.SECONDS);
            ResCheckDTO resCheckDTO = new ResCheckDTO();
            resCheckDTO.setAccountId(loginInfo.getAccountId());
            resCheckDTO.setAppName(AppNames.WEB_SERVICE_SELLER.getPlatform());
            resCheckDTO.setResourceType(ResourceTypeEnum.BUTTON.getName());
            //订单列表价格显示的按钮
            resCheckDTO.setCode("3202S01-10");
            //有资源就要隐藏
            boolean needHidePrice = !roleService.checkRes(resCheckDTO);
            reportWaybillQueryDTO.setNeedHidePrice(needHidePrice);
            reportWaybillQueryDTO.setOperatorId(loginInfo.getAccountId());
            reportWaybillQueryDTO.setSellerId(loginInfo.getMemberId());
            reportWaybillQueryDTO.setQueryAppName(AppNames.WEB_SERVICE_SELLER.getCode());
            if (CsStringUtils.isBlank(reportWaybillQueryDTO.getStatus())) {
                List<String> statusLists = this.getShipBillStatusList().getData();
                statusLists.remove("");
                reportWaybillQueryDTO.setStatusList(statusLists);
            }
            reportWaybillQueryDTO.setSaleRegionIdList(null);
            if (loginInfo.getAccountType() == AccountDTO.ACCOUNT_TYPE_MEMBER_SUB) {
                reportWaybillQueryDTO.setSaleRegionIdList(loginInfo.getSaleRegionIdList());
                if (loginInfo.getSalesman()) {
                    reportWaybillQueryDTO.setSalesmanId(loginInfo.getAccountId());
                }
            }
            //2021.0.26  begin -- 如果查询类型为空，则根据角色默认查询类型  --问题修改：卖家中心-我的物流-提货管理-我的调度管理、我的运单管理：汽运和船运业务板块未分开，我的调度管理和我的运单管理船运和汽运数据混合一块，对于专门的船运岗位来说，多出的汽运数据容易混淆视线。
            if (CollectionUtils.isNotEmpty(loginInfo.getRoleNameList())) {
                int viewAll = 0;
                String type = reportWaybillQueryDTO.getTransportType();
                if (loginInfo.getRoleNameList().stream().anyMatch(item -> CsStringUtils.equals(item, "waybill_view_road"))) {//汽运运单数据查看角色
                    reportWaybillQueryDTO.setTransportType(TransportToolTypeEnum.ROAD_TRANSPORT.getCode());
                    viewAll++;
                }
                if (loginInfo.getRoleNameList().stream().anyMatch(item -> CsStringUtils.equals(item, "waybill_view_water"))) {//船运运运单数据查看角色
                    reportWaybillQueryDTO.setTransportType(TransportToolTypeEnum.WATER_TRANSPORT.getCode());
                    viewAll++;
                }
                if (viewAll > 1) {
                    reportWaybillQueryDTO.setTransportType(type);
                }
            }
            //2021.0.26  end -- 如果查询类型为空，则根据角色默认查询类型  --问题修改：卖家中心-我的物流-提货管理-我的调度管理、我的运单管理：汽运和船运业务板块未分开，我的调度管理和我的运单管理船运和汽运数据混合一块，对于专门的船运岗位来说，多出的汽运数据容易混淆视线。

            Response response1 = reportService.exportPublishedWaybill(reportWaybillQueryDTO);
            Map<String, Collection<String>> headers = response1.headers();

            HttpHeaders httpHeaders = new HttpHeaders();
            if (headers != null && !headers.isEmpty()) {
                headers.forEach((k, v) -> {
                    List<String> values = Lists.newLinkedList();
                    values.addAll(v);
                    httpHeaders.put(k, values);
                });
            }
            Response.Body body = response1.body();
            InputStream inputStream = body.asInputStream();
            InputStreamSource resource = new InputStreamResource(inputStream);
            return ResponseEntity
                    .ok()
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .headers(httpHeaders)
                    .body(resource);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BizException(BasicCode.CUSTOM_ERROR, "运单下载出错");
        } finally {
            redisService.del(key);
        }
    }

    /**
     * 查询运单的泵候选信息
     *
     * @param waybillId
     * @return
     */
    @Operation(summary = "查询运单的泵候选信息")
    @PostMapping(value = "/queryPumpListByWaybillId")
    public ItemResult<TwoLeaveOptionDTO> queryPumpListByWaybillId(@Parameter(description = "运单ID") @RequestParam String waybillId) {
        return shipBillService.queryPumpListByWaybillId(waybillId);
    }

    /**
     * 运单退货
     *
     * @param refundShipBillDTO
     * @return
     */
    @Operation(summary = "运单退货")
    @PostMapping(value = "/refundShipBill")
    public ItemResult<Void> refundShipBill(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody @Validated RefundShipBillDTO refundShipBillDTO) {
        refundShipBillDTO.setMemberId(loginInfo.getMemberId());
        refundShipBillDTO.setOperationUserId(loginInfo.getAccountId());
        refundShipBillDTO.setOperationUserName(loginInfo.getAccountName());
        return shipBillService.refundShipBill(refundShipBillDTO);
    }


    @Operation(summary = "获取商品种类下拉列表")
    @PostMapping(value = "/queryGoodsNameListDropDownBox")
    public ItemResult<List<GoodsInfoDTO>> queryGoodsNameListDropDownBox(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody QueryGoodsDTO queryGoodsDTO) {
        queryGoodsDTO.setSellerId(loginInfo.getMemberId());
        return shipBillService.queryGoodsNameListDropDownBox(queryGoodsDTO);
    }

    @Operation(summary = "通过车牌号、船名查询")
    @PostMapping(value = "/queryVehicleOrShippingListByName")
    public ItemResult<List<String>> queryVehicleOrShippingListByName(@Parameter(hidden = true) LoginInfo loginInfo, @Parameter(name = "arg0", description = "船名/车牌号") @RequestParam String arg0) {
        ShipBillQueryDTO queryDTO = new ShipBillQueryDTO();

        queryDTO.setSellerId(loginInfo.getMemberId());
        queryDTO.setVehicleNum(arg0);
        return shipBillService.queryVehicleOrShippingListByName(queryDTO);
    }

    /**
     * 运单号查询运输工具
     *
     * @param waybillNum
     * @return
     */
    @Operation(summary = "运单号查询运输工具")
    @PostMapping(value = "/queryTransportByWaybillNum")
    public ItemResult<TransportInfoDTO> queryTransportByWaybillNum(@RequestParam String waybillNum) {
        return shipBillService.queryTransportByWaybillNum(waybillNum);
    }

    /**
     * 查询订单下待发货的二维码信息
     *
     * @param orderCode
     * @return
     */
    @Operation(summary = "查询订单下待发货的二维码信息")
    @PostMapping(value = "/queryWDQrCodeByOrderCode")
    public ItemResult<List<QrCodeDTO>> queryWDQrCodeByOrderCode(@RequestParam String orderCode) {
        return shipBillService.queryWDQrCodeByOrderCode(orderCode);
    }

}
