package com.ecommerce.web.seller.controller;

import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.exception.BusinessOperationException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.goods.api.dto.GoodsResourceListDTO;
import com.ecommerce.goods.api.dto.PagePurchaseDTO;
import com.ecommerce.goods.api.dto.PurchaseDTO;
import com.ecommerce.goods.api.dto.ReqGoodsResourceDTO;
import com.ecommerce.goods.api.service.IPurchaseService;
import com.ecommerce.goods.api.service.IResourceService;
import com.ecommerce.information.api.dto.shop.AddShopAdvertisementDTO;
import com.ecommerce.information.api.dto.shop.AddShopApplyDTO;
import com.ecommerce.information.api.dto.shop.AddShopCarouselDTO;
import com.ecommerce.information.api.dto.shop.AddShopInfoDTO;
import com.ecommerce.information.api.dto.shop.GetShopCarouselListDTO;
import com.ecommerce.information.api.dto.shop.GetShopNewsListDTO;
import com.ecommerce.information.api.dto.shop.GetShopNoticeListDTO;
import com.ecommerce.information.api.dto.shop.GetShopPictureListDTO;
import com.ecommerce.information.api.dto.shop.PageShopAdvertisementListDTO;
import com.ecommerce.information.api.dto.shop.PageShopApplyListDTO;
import com.ecommerce.information.api.dto.shop.PageShopListDTO;
import com.ecommerce.information.api.dto.shop.PageShopNewsListDTO;
import com.ecommerce.information.api.dto.shop.PageShopNoticeListDTO;
import com.ecommerce.information.api.dto.shop.PageShopPictureListDTO;
import com.ecommerce.information.api.dto.shop.ShopAdvertisementDTO;
import com.ecommerce.information.api.dto.shop.ShopApplyDTO;
import com.ecommerce.information.api.dto.shop.ShopCarouselDTO;
import com.ecommerce.information.api.dto.shop.ShopCarouselListDTO;
import com.ecommerce.information.api.dto.shop.ShopInfoDTO;
import com.ecommerce.information.api.dto.shop.ShopListDTO;
import com.ecommerce.information.api.dto.shop.ShopNewsDTO;
import com.ecommerce.information.api.dto.shop.ShopNewsListDTO;
import com.ecommerce.information.api.dto.shop.ShopNoticeDTO;
import com.ecommerce.information.api.dto.shop.ShopNoticeListDTO;
import com.ecommerce.information.api.dto.shop.ShopPictureDTO;
import com.ecommerce.information.api.dto.shop.ShopPictureListDTO;
import com.ecommerce.information.api.dto.shop.UpdateShopAdvertisementDTO;
import com.ecommerce.information.api.dto.shop.UpdateShopCarouselDTO;
import com.ecommerce.information.api.dto.shop.UpdateShopInfoDTO;
import com.ecommerce.information.api.dto.shop.shopApproveDTO;
import com.ecommerce.information.api.service.IShopService;
import com.ecommerce.member.api.dto.exception.MemberCode;
import com.ecommerce.member.api.dto.member.MemberDetailDTO;
import com.ecommerce.member.api.service.IMemberService;
import com.ecommerce.member.api.session.LoginInfo;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import com.ecommerce.common.utils.CsStringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@Slf4j
@Tag(name = "Shop", description = ":店铺服务")
@RestController
@RequestMapping("/shop")
public class ShopController {

    @Autowired
    private IShopService iShopService;
    @Autowired
    private IResourceService resourceService;
    @Autowired
    private IPurchaseService iPurchaseService;
    @Autowired
    private IMemberService memberService;

    private static final String SHOP_AD_ID_ERROR = "店铺广告id不正确";
    private static final String INTERFACE_NOT_OPEN = "接口未开放";

    @Operation(summary = "禁用店铺")
    @PostMapping(value="/disable")
    public ItemResult<Boolean> disable(@Parameter(hidden = true) LoginInfo loginInfo, @Parameter(name = "shopId", description = "店铺Id") @RequestParam String shopId){
        checkShopId(loginInfo,shopId);
        iShopService.disable(shopId,loginInfo.getAccountId());
        return new ItemResult<>(true);
    }


    @Operation(summary = "启用店铺")
    @PostMapping(value="/enable")
    public ItemResult<Boolean> enable(@Parameter(hidden = true) LoginInfo loginInfo, @Parameter(name = "shopId", description = "店铺Id") @RequestParam String shopId){
        checkShopId(loginInfo,shopId);
        iShopService.enable(shopId,loginInfo.getAccountId());
        return new ItemResult<>(true);
    }


    @Operation(summary = "店铺轮播图详情")
    @PostMapping(value="/getShopCarouselDetail")
    public ItemResult<ShopCarouselDTO> getShopCarouselDetail(@Parameter(name = "carouselId", description = "轮播图Id") @RequestParam String carouselId){
        return new ItemResult<>(iShopService.getShopCarouselDetail(carouselId));
    }


    @Operation(summary = "修改店铺轮播图")
    @PostMapping(value="/updateShopCarousel")
    public ItemResult<Boolean> updateShopCarousel(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody UpdateShopCarouselDTO updateShopCarouselDTO){
        checkShopId(loginInfo,updateShopCarouselDTO.getShopId());
        iShopService.updateShopCarousel(updateShopCarouselDTO,loginInfo.getAccountId());
        return new ItemResult<>(true);
    }


    @Operation(summary = "删除店铺轮播图")
    @PostMapping(value="/deleteShopCarousel")
    public ItemResult<Boolean> deleteShopCarousel(@Parameter(hidden = true) LoginInfo loginInfo, @Parameter(name = "carouselId", description = "轮播图Id") @RequestParam String carouselId){
        ShopCarouselDTO shopCarouselDTO = iShopService.getShopCarouselDetail(carouselId);
        if( shopCarouselDTO == null ){
            return new ItemResult<>(true);
        }
        checkShopId(loginInfo,shopCarouselDTO.getShopId());
        iShopService.deleteShopCarousel(carouselId,loginInfo.getAccountId());
        return new ItemResult<>(true);
    }


    @Operation(summary = "新增店铺商品广告")
    @PostMapping(value="/addShopAdvertisement")
    public ItemResult<Boolean> addShopAdvertisement(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody AddShopAdvertisementDTO addShopAdvertisementDTO){
        checkShopId(loginInfo,addShopAdvertisementDTO.getShopId());
        iShopService.addShopAdvertisement(addShopAdvertisementDTO,loginInfo.getAccountId());
        return new ItemResult<>(true);
    }


    @Operation(summary = "修改店铺商品广告")
    @PostMapping(value="/updateShopAdvertisement")
    public ItemResult<Boolean> updateShopAdvertisement(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody UpdateShopAdvertisementDTO updateShopAdvertisementDTO){
        checkShopId(loginInfo,updateShopAdvertisementDTO.getShopId());
        iShopService.updateShopAdvertisement(updateShopAdvertisementDTO,loginInfo.getAccountId());
        return new ItemResult<>(true);
    }


    @Operation(summary = "启用店铺商品广告")
    @PostMapping(value="/enableShopAdvertisement")
    public ItemResult<Boolean> enableShopAdvertisement(@Parameter(hidden = true) LoginInfo loginInfo, @Parameter(name = "shopAdvertisementId", description = "店铺商品广告Id") @RequestParam String shopAdvertisementId){
        ShopAdvertisementDTO shopAdvertisementDTO = iShopService.getShopAdvertisementDetail(shopAdvertisementId);
        if( shopAdvertisementDTO == null ){
            throw new BizException(BasicCode.CUSTOM_ERROR,SHOP_AD_ID_ERROR);
        }
        checkShopId(loginInfo,shopAdvertisementDTO.getShopId());
        iShopService.enableShopAdvertisement(shopAdvertisementId,loginInfo.getAccountId());
        return new ItemResult<>(true);
    }


    @Operation(summary = "禁用店铺商品广告")
    @PostMapping(value="/disableShopAdvertisement")
    public ItemResult<Boolean> disableShopAdvertisement(@Parameter(hidden = true) LoginInfo loginInfo, @Parameter(name = "shopAdvertisementId", description = "店铺商品广告Id") @RequestParam String shopAdvertisementId){
        ShopAdvertisementDTO shopAdvertisementDTO = iShopService.getShopAdvertisementDetail(shopAdvertisementId);
        if( shopAdvertisementDTO == null ){
            throw new BizException(BasicCode.CUSTOM_ERROR,SHOP_AD_ID_ERROR);
        }
        checkShopId(loginInfo,shopAdvertisementDTO.getShopId());
        iShopService.disableShopAdvertisement(shopAdvertisementId,loginInfo.getAccountId());
        return new ItemResult<>(true);
    }


    @Operation(summary = "店铺商品广告详情")
    @PostMapping(value="/getShopAdvertisementDetail")
    public ItemResult<ShopAdvertisementDTO> getShopAdvertisementDetail(@Parameter(hidden = true) LoginInfo loginInfo,@Parameter(name = "shopAdvertisementId", description = "店铺商品广告Id") @RequestParam String shopAdvertisementId){
        return new ItemResult<>(iShopService.getShopAdvertisementDetail(shopAdvertisementId));
    }


    @Operation(summary = "店铺商品广告分页查询")
    @PostMapping(value="/getShopAdvertisementList")
    public ItemResult<PageInfo<ShopAdvertisementDTO>> getShopAdvertisementList(@Parameter(hidden = true) LoginInfo loginInfo,@RequestBody PageShopAdvertisementListDTO pageShopAdvertisementListDTO){
        checkShopId(loginInfo,pageShopAdvertisementListDTO.getShopId());
        return new ItemResult<>(iShopService.getShopAdvertisementList(pageShopAdvertisementListDTO));
    }


    @Operation(summary = "删除店铺广告")
    @PostMapping(value="/deleteShopAdvertisement")
    public ItemResult<Boolean> deleteShopAdvertisement(@Parameter(hidden = true) LoginInfo loginInfo, @Parameter(name = "shopAdvertisementId", description = "店铺商品广告Id") @RequestParam String shopAdvertisementId){
        ShopAdvertisementDTO shopAdvertisementDTO = iShopService.getShopAdvertisementDetail(shopAdvertisementId);
        if( shopAdvertisementDTO == null ){
            throw new BizException(BasicCode.CUSTOM_ERROR,SHOP_AD_ID_ERROR);
        }
        checkShopId(loginInfo,shopAdvertisementDTO.getShopId());
        iShopService.deleteShopAdvertisement(shopAdvertisementId,loginInfo.getAccountId());
        return new ItemResult<>(true);
    }


    @Operation(summary = "店铺轮播图列表")
    @PostMapping(value="/getShopCarouselList")
    public ItemResult<List<ShopCarouselListDTO>> getShopCarouselList(@Parameter(hidden = true) LoginInfo loginInfo,@RequestBody GetShopCarouselListDTO getShopCarouselListDTO){
        checkShopId(loginInfo,getShopCarouselListDTO.getShopId());
        return new ItemResult<>(iShopService.getShopCarouselList(getShopCarouselListDTO));
    }


    @Operation(summary = "分页店铺图集列表")
    @PostMapping(value="/pageShopPictureList")
    public ItemResult<PageInfo<ShopPictureListDTO>> pageShopPictureList(@Parameter(hidden = true) LoginInfo loginInfo,@RequestBody PageShopPictureListDTO pageShopPictureListDTO){
        checkShopId(loginInfo,pageShopPictureListDTO.getShopId());
        return new ItemResult<>(iShopService.pageShopPictureList(pageShopPictureListDTO));
    }


    @Operation(summary = "店铺图集详情")
    @PostMapping(value="/getShopPictureDetail")
    public ItemResult<ShopPictureDTO> getShopPictureDetail(@Parameter(name = "pictureId", description = "图集Id") @RequestParam String pictureId){
        return new ItemResult<>(iShopService.getShopPictureDetail(pictureId));
    }


    @Operation(summary = "店铺活动公告详情")
    @PostMapping(value="/getShopNoticeDetail")
    public ItemResult<ShopNoticeDTO> getShopNoticeDetail(@Parameter(name = "noticeId", description = "公告Id") @RequestParam String noticeId){
        return new ItemResult<>(iShopService.getShopNoticeDetail(noticeId));
    }


    @Operation(summary = "店铺图集列表")
    @PostMapping(value="/getShopPictureList")
    public ItemResult<List<ShopPictureListDTO>> getShopPictureList(@Parameter(hidden = true) LoginInfo loginInfo,@RequestBody GetShopPictureListDTO getShopPictureListDTO){
        checkShopId(loginInfo,getShopPictureListDTO.getShopId());
        return new ItemResult<>(iShopService.getShopPictureList(getShopPictureListDTO));
    }


    @Operation(summary = "分页查看店铺申请列表")
    @PostMapping(value="/pageShopApplyList")
    public ItemResult<PageInfo<ShopApplyDTO>> pageShopApplyList(@RequestBody PageShopApplyListDTO pageShopApplyListDTO){
        //平台才能使用
        throw new BizException(BasicCode.CUSTOM_ERROR,INTERFACE_NOT_OPEN);
    }


    @Operation(summary = "下移店铺活动公告")
    @PostMapping(value="/moveDownShopNotice")
    public ItemResult<Boolean> moveDownShopNotice(@Parameter(hidden = true) LoginInfo loginInfo, @Parameter(name = "noticeId", description = "公告Id") @RequestParam String noticeId){
        iShopService.moveDownShopNotice(noticeId,loginInfo.getAccountId());
        return new ItemResult<>(true);
    }


    @Operation(summary = "店铺活动公告列表")
    @PostMapping(value="/getShopNoticeList")
    public ItemResult<List<ShopNoticeListDTO>> getShopNoticeList(@Parameter(hidden = true) LoginInfo loginInfo,@RequestBody GetShopNoticeListDTO getShopNoticeListDTO){
        checkShopId(loginInfo,getShopNoticeListDTO.getShopId());
        return new ItemResult<>(iShopService.getShopNoticeList(getShopNoticeListDTO));
    }


    @Operation(summary = "分页店铺活动公告列表")
    @PostMapping(value="/pageShopNoticeList")
    public ItemResult<PageInfo<ShopNoticeListDTO>> pageShopNoticeList(@Parameter(hidden = true) LoginInfo loginInfo,@RequestBody PageShopNoticeListDTO pageShopNoticeListDTO){
        checkShopId(loginInfo,pageShopNoticeListDTO.getShopId());
        return new ItemResult<>(iShopService.pageShopNoticeList(pageShopNoticeListDTO));
    }


    @Operation(summary = "上传店铺图集")
    @PostMapping(value="/uploadShopPicture")
    public ItemResult<Boolean> uploadShopPicture(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody ShopPictureDTO shopPictureDTO){
        checkShopId(loginInfo,shopPictureDTO.getShopId());
        iShopService.uploadShopPicture(shopPictureDTO,loginInfo.getAccountId());
        return new ItemResult<>(true);
    }


    @Operation(summary = "店铺企业动态详情")
    @PostMapping(value="/getShopNewsDetail")
    public ItemResult<ShopNewsDTO> getShopNewsDetail(@Parameter(hidden = true) LoginInfo loginInfo,@Parameter(name = "newsId", description = "动态消息Id") @RequestParam String newsId){
        return new ItemResult<>(iShopService.getShopNewsDetail(newsId));
    }


    @Operation(summary = "下移店铺轮播图")
    @PostMapping(value="/moveDownShopCarousel")
    public ItemResult<Boolean> moveDownShopCarousel(@Parameter(hidden = true) LoginInfo loginInfo, @Parameter(name = "carouselId", description = "轮播图Id") @RequestParam String carouselId){
        iShopService.moveDownShopCarousel(carouselId,loginInfo.getAccountId());
        return new ItemResult<>(true);
    }


    @Operation(summary = "查看店铺申请详情")
    @PostMapping(value="/getShopApplyDetail")
    public ItemResult<ShopApplyDTO> getShopApplyDetail(@Parameter(hidden = true) LoginInfo loginInfo){
        return new ItemResult<>(iShopService.getShopApplyDetail(loginInfo.getMemberId()));
    }


    @Operation(summary = "上移店铺轮播图")
    @PostMapping(value="/moveUpShopCarousel")
    public ItemResult<Boolean> moveUpShopCarousel(@Parameter(hidden = true) LoginInfo loginInfo, @Parameter(name = "carouselId", description = "轮播图Id") @RequestParam String carouselId){
        iShopService.moveUpShopCarousel(carouselId,loginInfo.getAccountId());
        return new ItemResult<>(true);
    }


    @Operation(summary = "置顶店铺企业动态")
    @PostMapping(value="/toTopShopNews")
    public ItemResult<Boolean> toTopShopNews(@Parameter(hidden = true) LoginInfo loginInfo, @Parameter(name = "newsId", description = "企业动态Id") @RequestParam String newsId){
        ShopNewsDTO shopNewsDTO = iShopService.getShopNewsDetail(newsId);
        if( shopNewsDTO != null ) {
            checkShopId(loginInfo, shopNewsDTO.getShopId());
            iShopService.toTopShopNews(newsId, loginInfo.getAccountId());
        }
        return new ItemResult<>(true);
    }


    @Operation(summary = "上移店铺活动公告")
    @PostMapping(value="/moveUpShopNotice")
    public ItemResult<Boolean> moveUpShopNotice(@Parameter(hidden = true) LoginInfo loginInfo, @Parameter(name = "noticeId", description = "活动公告Id") @RequestParam String noticeId){
        iShopService.moveUpShopNotice(noticeId,loginInfo.getAccountId());
        return new ItemResult<>(true);
    }


    @Operation(summary = "会员是否可以提出申请")
    @PostMapping(value="/isExistShopApply")
    public ItemResult<Boolean> isExistShopApply(@Parameter(hidden = true) LoginInfo loginInfo){
        return new ItemResult<>(iShopService.isExistShopApply(loginInfo.getMemberId()));
    }


    @Operation(summary = "会员开店")
    @PostMapping(value="/addShopInfo")
    public ItemResult<Boolean> addShopInfo(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody AddShopInfoDTO addShopInfoDTO){
        MemberDetailDTO memberDetailDTO = memberService.findMemberById(addShopInfoDTO.getMemberId());
        //根据开店会员拥有的角色判断是否可以开启对应店铺
        if (memberDetailDTO == null) {
            throw new BizException(BasicCode.UNDEFINED_ERROR,"不存在此会员");
        }
        addShopInfoDTO.setIsCarrier(0);
        addShopInfoDTO.setIsSeller(0);
        addShopInfoDTO.setIsSupplier(0);
        if(memberDetailDTO.getSellerFlg() == 1){
            addShopInfoDTO.setIsSeller(1);
        }
        if(memberDetailDTO.getCarrierFlg() == 1){
            addShopInfoDTO.setIsCarrier(1);
        }
        if(memberDetailDTO.getSupplierFlg() == 1){
            addShopInfoDTO.setIsSupplier(1);
        }
        iShopService.addShopInfo(addShopInfoDTO,loginInfo.getAccountId());
        return new ItemResult<>(true);
    }


    @Operation(summary = "会员修改店铺信息")
    @PostMapping(value="/updateShopInfo")
    public ItemResult<Boolean> updateShopInfo(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody UpdateShopInfoDTO updateShopInfoDTO){
        updateShopInfoDTO.setMemberId(loginInfo.getMemberId());
        //根据开店会员拥有的角色判断是否可以开启对应店铺
        MemberDetailDTO memberDetailDTO = memberService.findMemberById(updateShopInfoDTO.getMemberId());
        if (memberDetailDTO == null) {
            throw new BizException(BasicCode.UNDEFINED_ERROR,"不存在此会员");
        }
        if (updateShopInfoDTO.getIsSeller() == 1 && memberDetailDTO.getSellerFlg() == 0) {
            throw new BizException(BasicCode.UNDEFINED_ERROR,"会员无卖家角色，不能开启卖家店铺");
        }
        if (updateShopInfoDTO.getIsCarrier() == 1 && memberDetailDTO.getCarrierFlg() == 0) {
            throw new BizException(BasicCode.UNDEFINED_ERROR,"会员无承运商角色，不能开启承运商店铺");
        }
        if (updateShopInfoDTO.getIsSupplier() == 1 && memberDetailDTO.getSupplierFlg() == 0) {
            throw new BizException(BasicCode.UNDEFINED_ERROR,"会员无供应商角色，不能开启供应商店铺");
        }
        iShopService.updateShopInfo(updateShopInfoDTO,loginInfo.getAccountId());
        return new ItemResult<>(true);
    }

    @Operation(summary = "会员修改店铺富文本信息")
    @PostMapping(value="/updateShopInfoRichText")
    public ItemResult<Boolean> updateShopInfoRichText(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody UpdateShopInfoDTO updateShopInfoDTO){
        updateShopInfoDTO.setMemberId(loginInfo.getMemberId());
        ShopInfoDTO shopInfoDTO = iShopService.getShopInfo(updateShopInfoDTO.getShopId());
        checkShopId(loginInfo, shopInfoDTO == null ? null : shopInfoDTO.getShopId());
        UpdateShopInfoDTO updateShopInfoDTO1 = new UpdateShopInfoDTO();
        BeanUtils.copyProperties(shopInfoDTO,updateShopInfoDTO1);
        if(updateShopInfoDTO.getRichText1() != null){
            updateShopInfoDTO1.setRichText1(updateShopInfoDTO.getRichText1());
        }
        if(updateShopInfoDTO.getRichText2() != null){
            updateShopInfoDTO1.setRichText2(updateShopInfoDTO.getRichText2());
        }
        if(updateShopInfoDTO.getRichText3() != null){
            updateShopInfoDTO1.setRichText3(updateShopInfoDTO.getRichText3());
        }
        if(updateShopInfoDTO.getRichText4() != null){
            updateShopInfoDTO1.setRichText4(updateShopInfoDTO.getRichText4());
        }
        if(updateShopInfoDTO.getRichText5() != null){
            updateShopInfoDTO1.setRichText5(updateShopInfoDTO.getRichText5());
        }
        iShopService.updateShopInfo(updateShopInfoDTO1,loginInfo.getAccountId());
        return new ItemResult<>(true);
    }


    @Operation(summary = "查看店铺详情")
    @PostMapping(value="/getShopInfo")
    public ItemResult<ShopInfoDTO> getShopInfo(@Parameter(name = "shopId", description = "店铺Id") @RequestParam String shopId){
        return new ItemResult<>(iShopService.getShopInfo(shopId));
    }

    @Operation(summary = "通过memberId查看店铺详情")
    @PostMapping(value="/getShopInfoByMemberId")
    public ItemResult<ShopInfoDTO> getShopInfoByMemberId(@Parameter(hidden = true) LoginInfo loginInfo){
        return new ItemResult<>(iShopService.getShopInfoByMemberId(loginInfo.getMemberId()));
    }

    @Operation(summary = "通过域名前缀查询店铺")
    @PostMapping(value="/getShopInfoByUrl")
    public ItemResult<ShopInfoDTO> getShopInfoByUrl(@Parameter(name = "shopUrlPrefix", description = "域名前缀") @RequestParam String shopUrlPrefix){
        return new ItemResult<>(iShopService.getShopInfoByUrl(shopUrlPrefix));
    }

    @Operation(summary = "新增店铺轮播图（最多五条，如果多于五条则删除序号最小的一条）")
    @PostMapping(value="/addShopCarousel")
    public ItemResult<Boolean> addShopCarousel(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody AddShopCarouselDTO addShopCarouselDTO){
        checkShopId(loginInfo, addShopCarouselDTO.getShopId());
        iShopService.addShopCarousel(addShopCarouselDTO,loginInfo.getAccountId());
        return new ItemResult<>(true);
    }


    @Operation(summary = "下移店铺企业动态")
    @PostMapping(value="/moveDownShopNews")
    public ItemResult<Object> moveDownShopNews(@Parameter(hidden = true) LoginInfo loginInfo, @Parameter(name = "newsId", description = "企业动态Id") @RequestParam String newsId){
        iShopService.moveDownShopNews(newsId,loginInfo.getAccountId());
        return new ItemResult<>(true);
    }


    @Operation(summary = "删除店铺申请")
    @PostMapping(value="/deleteShopApply")
    public ItemResult<Object> deleteShopApply(@Parameter(hidden = true) LoginInfo loginInfo, @Parameter(name = "applyId", description = "店铺申请Id") @RequestParam String applyId){
        ShopApplyDTO shopApplyDTO = iShopService.getShopApplyDetail(loginInfo.getMemberId());
        if (shopApplyDTO == null || !CsStringUtils.equals(shopApplyDTO.getApplyId(), applyId)) {
            throw new BizException(BasicCode.CUSTOM_ERROR,"applyId不正确");
        }
        iShopService.deleteShopApply(applyId,loginInfo.getAccountId());
        return new ItemResult<>(true);
    }


    @Operation(summary = "移除店铺图集")
    @PostMapping(value="/deletShopPicture")
    public ItemResult<Object> deletShopPicture(@Parameter(hidden = true) LoginInfo loginInfo, @Parameter(name = "pictureId", description = "图集Id") @RequestParam String pictureId){
        ShopPictureDTO shopPictureDTO = iShopService.getShopPictureDetail(pictureId);
        checkShopId(loginInfo, shopPictureDTO == null ? null : shopPictureDTO.getShopId());
        iShopService.deletShopPicture(pictureId,loginInfo.getAccountId());
        return new ItemResult<>(true);
    }


    @Operation(summary = "分页店铺企业动态列表")
    @PostMapping(value="/pageShopNewsList")
    public ItemResult<PageInfo<ShopNewsListDTO>> pageShopNewsList(@Parameter(hidden = true) LoginInfo loginInfo,@RequestBody PageShopNewsListDTO pageShopNewsListDTO){
        checkShopId(loginInfo, pageShopNewsListDTO.getShopId());
        return new ItemResult<>(iShopService.pageShopNewsList(pageShopNewsListDTO));
    }


    @Operation(summary = "店铺企业动态列表")
    @PostMapping(value="/getShopNewsList")
    public ItemResult<List<ShopNewsListDTO>> getShopNewsList(@Parameter(hidden = true) LoginInfo loginInfo,@RequestBody GetShopNewsListDTO getShopNewsListDTO){
        checkShopId(loginInfo, getShopNewsListDTO.getShopId());
        return new ItemResult<>(iShopService.getShopNewsList(getShopNewsListDTO));
    }


    @Operation(summary = "修改店铺活动公告")
    @PostMapping(value="/updateShopNotice")
    public ItemResult<Object> updateShopNotice(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody ShopNoticeDTO shopNoticeDTO){
        checkShopId(loginInfo, shopNoticeDTO.getShopId());
        iShopService.updateShopNotice(shopNoticeDTO,loginInfo.getAccountId());
        return new ItemResult<>(true);
    }


    @Operation(summary = "删除店铺活动公告")
    @PostMapping(value="/deleteShopNotice")
    public ItemResult<Object> deleteShopNotice(@Parameter(hidden = true) LoginInfo loginInfo, @Parameter(name = "noticeId", description = "活动公告Id") @RequestParam String noticeId){
        ShopNoticeDTO shopNoticeDTO = iShopService.getShopNoticeDetail(noticeId);
        checkShopId(loginInfo, shopNoticeDTO == null ? null : shopNoticeDTO.getShopId());
        iShopService.deleteShopNotice(noticeId,loginInfo.getAccountId());
        return new ItemResult<>(true);
    }


    @Operation(summary = "置顶店铺活动公告")
    @PostMapping(value="/toTopShopNotice")
    public ItemResult<Object> toTopShopNotice(@Parameter(hidden = true) LoginInfo loginInfo, @Parameter(name = "noticeId", description = "活动公告Id") @RequestParam String noticeId){
        iShopService.toTopShopNotice(noticeId,loginInfo.getAccountId());
        return new ItemResult<>(true);
    }


    @Operation(summary = "创建店铺申请")
    @PostMapping(value="/addShopApply")
    public ItemResult<Object> addShopApply(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody AddShopApplyDTO addShopApplyDTO){
        addShopApplyDTO.setMemberId(loginInfo.getMemberId());
        addShopApplyDTO.setMemberName(loginInfo.getMemberName());
        if (!iShopService.isExistShopApply(loginInfo.getMemberId())) {
            return ItemResult.fail("操作失败，申请已存在，请勿重复提交");
        }
        iShopService.addShopApply(addShopApplyDTO,loginInfo.getAccountId());
        return new ItemResult<>("操作成功");
    }


    @Operation(summary = "修改店铺企业动态")
    @PostMapping(value="/updateShopNews")
    public ItemResult<Object> updateShopNews(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody ShopNewsDTO shopNewsDTO){
        checkShopId(loginInfo, shopNewsDTO.getShopId());
        iShopService.updateShopNews(shopNewsDTO,loginInfo.getAccountId());
        return new ItemResult<>(true);
    }


    @Operation(summary = "新增店铺活动公告")
    @PostMapping(value="/addShopNotice")
    public ItemResult<Object> addShopNotice(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody ShopNoticeDTO shopNoticeDTO){
        checkShopId(loginInfo, shopNoticeDTO.getShopId());
        iShopService.addShopNotice(shopNoticeDTO,loginInfo.getAccountId());
        return new ItemResult<>(true);
    }


    @Operation(summary = "新增店铺企业动态")
    @PostMapping(value="/addShopNews")
    public ItemResult<Object> addShopNews(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody ShopNewsDTO shopNewsDTO){
        checkShopId(loginInfo, shopNewsDTO.getShopId());
        iShopService.addShopNews(shopNewsDTO,loginInfo.getAccountId());
        return new ItemResult<>(true);
    }


    @Operation(summary = "删除店铺企业动态")
    @PostMapping(value="/deletShopNews")
    public ItemResult<Object> deletShopNews(@Parameter(hidden = true) LoginInfo loginInfo, @Parameter(name = "newsId", description = "企业动态Id") @RequestParam String newsId){
        ShopNewsDTO shopNewsDTO = iShopService.getShopNewsDetail(newsId);
        checkShopId(loginInfo, shopNewsDTO == null ? null : shopNewsDTO.getShopId());
        iShopService.deletShopNews(newsId,loginInfo.getAccountId());
        return new ItemResult<>(true);
    }


    @Operation(summary = "上移店铺企业动态")
    @PostMapping(value="/moveUpShopNews")
    public ItemResult<Object> moveUpShopNews(@Parameter(hidden = true) LoginInfo loginInfo, @Parameter(name = "newsId", description = "企业动态Id") @RequestParam String newsId){
        iShopService.moveUpShopNews(newsId,loginInfo.getAccountId());
        return new ItemResult<>(true);
    }


    @Operation(summary = "编辑店铺申请")
    @PostMapping(value="/updateShopApply")
    public ItemResult<Object> updateShopApply(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody ShopApplyDTO shopApplyDTO){
        if (CsStringUtils.isBlank(shopApplyDTO.getApplyId())) {
            throw new BizException(BasicCode.CUSTOM_ERROR,"applyId不可为空");
        }
        ShopApplyDTO shopApplyDTO1 = iShopService.getShopApplyDetail(loginInfo.getMemberId());
        if (shopApplyDTO1 == null || !CsStringUtils.equals(shopApplyDTO1.getApplyId(), shopApplyDTO.getApplyId())) {
            throw new BizException(BasicCode.CUSTOM_ERROR,"applyId不正确");
        }
        shopApplyDTO.setMemberId(loginInfo.getMemberId());
        iShopService.updateShopApply(shopApplyDTO,loginInfo.getAccountId());
        return new ItemResult<>(true);
    }


    @Operation(summary = "审批店铺申请")
    @PostMapping(value="/approved")
    public ItemResult<Object> approved(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody shopApproveDTO dto){
        //平台才能使用
        throw new BizException(BasicCode.CUSTOM_ERROR,INTERFACE_NOT_OPEN);
    }


    @Operation(summary = "会员是否可以开店")
    @PostMapping(value="/isExistShop")
    public ItemResult<Boolean> isExistShop(@Parameter(hidden = true) LoginInfo loginInfo){
        return new ItemResult<>(iShopService.isExistShop(loginInfo.getMemberId()));
    }


    @Operation(summary = "会员关店")
    @PostMapping(value="/closeShopInfo")
    public ItemResult<Object> closeShopInfo(@Parameter(hidden = true) LoginInfo loginInfo, @Parameter(name = "shopId", description = "店铺Id") @RequestParam String shopId){
        checkShopId(loginInfo, shopId);
        iShopService.closeShopInfo(shopId,loginInfo.getAccountId());
        return new ItemResult<>(true);
    }


    @Operation(summary = "分页查询店铺列表")
    @PostMapping(value="/pageShopList")
    public ItemResult<PageInfo<ShopListDTO>> pageShopList(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody PageShopListDTO pageShopListDTO){
        //平台才能使用
        throw new BizException(BasicCode.CUSTOM_ERROR,INTERFACE_NOT_OPEN);
    }

    @Operation(summary = "通过关键字查询店铺商品广告")
    @PostMapping(value="/searchGoodsResourceEmall")
    public ItemResult<PageInfo<GoodsResourceListDTO>> searchGoodsResourceEmall(@RequestBody ReqGoodsResourceDTO query){
        return new ItemResult<>(resourceService.searchGoodsResourceEmall(query));
    }

    @Operation(summary = "分页采购列表查询")
    @PostMapping(value="/searchPagePurchase")
    public ItemResult<PageInfo<PurchaseDTO>> searchPagePurchase(@RequestBody PagePurchaseDTO arg0){
        return new ItemResult<>(iPurchaseService.searchPagePurchase(arg0));
    }

    //承运商店铺广告问题汇总
    private void checkShopId(LoginInfo loginInfo,String shopId){
        if (loginInfo == null || CsStringUtils.isBlank(loginInfo.getMemberId())) {
            throw new BizException(MemberCode.LOGIN_ERROR, new Object[]{"你还没有登录，请先登录"});
        }
        if (CsStringUtils.isBlank(shopId)) {
            throw new BizException(BasicCode.CUSTOM_ERROR,"店铺id不可为空");
        }
        ShopInfoDTO shopInfoDTO = iShopService.getShopInfo(shopId);
        if (shopInfoDTO == null || !CsStringUtils.equals(shopInfoDTO.getMemberId(), loginInfo.getMemberId())) {
            throw new BizException(BasicCode.CUSTOM_ERROR,"店铺id不正确,你只能查看自己的店铺");
        }
    }

}
