package com.ecommerce.web.seller.controller;


import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.common.result.ItemResult;
import com.ecommerce.goods.api.dto.PageStartSaleNumDTO;
import com.ecommerce.goods.api.dto.StartSaleNumDTO;
import com.ecommerce.goods.api.dto.base.PageQuery;
import com.ecommerce.goods.api.service.IStartSaleNumService;
import com.ecommerce.member.api.session.LoginInfo;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


/**
 * @Created锛�Tue Aug 20 14:32:18 CST 2019
 * <AUTHOR>
 * @Version:2
 * @Description:起售量管理服务
*/

@RestController
@Tag(name = "StartSaleNum", description = "起售量管理服务")
@RequestMapping("/startSaleNum")
public class StartSaleNumController {

   @Autowired 
   private IStartSaleNumService iStartSaleNumService;

   @Operation(summary = "修改起售量规则")
   @PostMapping(value="/updateStartSaleNum")
   public ItemResult<Void> updateStartSaleNum(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody StartSaleNumDTO startSaleNumDTO){
      return iStartSaleNumService.updateStartSaleNum(startSaleNumDTO,loginInfo.getAccountId());
   }

   @Operation(summary = "通过ID查询起售量规则")
   @PostMapping(value="/findById")
   public ItemResult<StartSaleNumDTO> findById(@Parameter(name = "startSaleId", description = "规则Id") @RequestParam String startSaleId){
      return iStartSaleNumService.findById(startSaleId);
   }

   @Operation(summary = "删除起售量规则")
   @PostMapping(value="/deleteStartSaleNum")
   public ItemResult<Void> deleteStartSaleNum(@Parameter(hidden = true) LoginInfo loginInfo,@Parameter(name = "startSaleId", description = "规则Id") @RequestParam String startSaleId){
      return iStartSaleNumService.deleteStartSaleNum(startSaleId,loginInfo.getAccountId());
   }

   @Operation(summary = "新增起售量规则")
   @PostMapping(value="/addStartSaleNum")
   public ItemResult<Void> addStartSaleNum(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody StartSaleNumDTO startSaleNumDTO){
      startSaleNumDTO.setSellerId(loginInfo.getMemberId());
      return iStartSaleNumService.addStartSaleNum(startSaleNumDTO,loginInfo.getAccountId());
   }

   @Operation(summary = "分页查询起售量规则")
   @PostMapping(value="/pageStartSaleNum")
   public ItemResult<PageInfo<StartSaleNumDTO>> pageStartSaleNum(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody PageQuery<PageStartSaleNumDTO> pageQuery){
      PageStartSaleNumDTO pageStartSaleNumDTO = pageQuery.getQueryDTO();
      pageStartSaleNumDTO = pageStartSaleNumDTO == null ? new PageStartSaleNumDTO() : pageStartSaleNumDTO;
      pageStartSaleNumDTO.setSellerId(loginInfo.getMemberId());
      pageStartSaleNumDTO.setPageNum(pageQuery.getPageNum());
      pageStartSaleNumDTO.setPageSize(pageQuery.getPageSize());
      return iStartSaleNumService.pageStartSaleNum(pageStartSaleNumDTO);
   }

}
