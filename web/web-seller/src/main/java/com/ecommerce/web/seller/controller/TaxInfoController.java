package com.ecommerce.web.seller.controller;


import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.common.result.ItemResult;
import com.ecommerce.member.api.dto.TaxInfoDTO;
import com.ecommerce.member.api.dto.TaxInfoDetailDTO;
import com.ecommerce.member.api.dto.TaxInvoiceInfoDTO;
import com.ecommerce.member.api.service.ITaxInfoService;
import com.ecommerce.member.api.session.LoginInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import com.ecommerce.common.utils.CsStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;


/**
 * 增票资质相关service
 *
 * <AUTHOR>
*/

@RestController
@Tag(name = "TaxInfo", description = "增票资质相关service")
@RequestMapping("/taxInfo")
public class TaxInfoController {

   @Autowired
   private ITaxInfoService iTaxInfoService;

   private static final String USER_NO_PERMISSION = "此用户没有权限";

   @Operation(summary = "修改增票资质")
   @PostMapping(value="/update")
   public ItemResult<Object> update(@Parameter(hidden = true) LoginInfo loginInfo, @Valid @RequestBody TaxInfoDTO taxInfo){
       if (CsStringUtils.isBlank(taxInfo.getId())) {
          return new ItemResult<>("500", "id不存在");
      }
      if(!iTaxInfoService.checkTaxInfo(loginInfo.getMemberId(),taxInfo.getId())){
         return new ItemResult<>("500", USER_NO_PERMISSION);
      }
      taxInfo.setMemberId(loginInfo.getMemberId());
      iTaxInfoService.update(taxInfo,loginInfo.getAccountId());
      return new ItemResult<>(new Object());
   }


   @Operation(summary = "创建增票资质")
   @PostMapping(value="/create")
   public ItemResult<Object> create(@Parameter(hidden = true) LoginInfo loginInfo, @Valid @RequestBody TaxInfoDTO taxInfo){
      taxInfo.setMemberId(loginInfo.getMemberId());
      iTaxInfoService.create(taxInfo,loginInfo.getAccountId());
      return new ItemResult<>(new Object());
   }


   @Operation(summary = "创建增票收票人信息")
   @PostMapping(value="/createInvoiceInfo")
   public ItemResult<Object> createInvoiceInfo(@Parameter(hidden = true) LoginInfo loginInfo, @Valid @RequestBody TaxInvoiceInfoDTO taxInvoiceInfoDTO){
      if(!iTaxInfoService.checkTaxInfo(loginInfo.getMemberId(),taxInvoiceInfoDTO.getId())){
         return new ItemResult<>(USER_NO_PERMISSION);
      }
      iTaxInfoService.createInvoiceInfo(taxInvoiceInfoDTO,loginInfo.getAccountId());
      return new ItemResult<>(new Object());
   }


   @Operation(summary = "设置默认增票资质")
   @PostMapping(value="/setDefaultById")
   public ItemResult<Object> setDefaultById(@Parameter(hidden = true) LoginInfo loginInfo, @Parameter(name = "id", description = "资质Id") @RequestParam String id){
      if(!iTaxInfoService.checkTaxInfo(loginInfo.getMemberId(),id)){
         return new ItemResult<>(USER_NO_PERMISSION);
      }
      iTaxInfoService.setDefaultById(id,loginInfo.getMemberId(),loginInfo.getAccountId());
      return new ItemResult<>(new Object());
   }


   @Operation(summary = "根据id删除增票资质")
   @PostMapping(value="/deleteById")
   public ItemResult<Object> deleteById(@Parameter(hidden = true) LoginInfo loginInfo, @Parameter(name = "id", description = "资质Id") @RequestParam String id){
      if(!iTaxInfoService.checkTaxInfo(loginInfo.getMemberId(),id)){
         return new ItemResult<>(USER_NO_PERMISSION);
      }
      iTaxInfoService.deleteById(id,loginInfo.getAccountId());
      return new ItemResult<>(new Object());
   }


   @Operation(summary = "根据会员id获取默认增票资质（企业员工则根据企业id获取默认收货地址）")
   @PostMapping(value="/getDefaultById")
   public ItemResult<TaxInfoDetailDTO> getDefaultById(@Parameter(hidden = true) LoginInfo loginInfo){
      return new ItemResult<>(iTaxInfoService.getDefaultById(loginInfo.getMemberId()));
   }


   @Operation(summary = "根据id查找增票资质")
   @PostMapping(value="/findById")
   public ItemResult<TaxInfoDetailDTO> findById(@Parameter(hidden = true) LoginInfo loginInfo,@Parameter(name = "id", description = "资质Id") @RequestParam String id){
      if(!iTaxInfoService.checkTaxInfo(loginInfo.getAccountId(),id)){
         return ItemResult.fail(USER_NO_PERMISSION);
      }
      return new ItemResult<>(iTaxInfoService.findById(id));
   }


   @Operation(summary = "查询某会员的所有增票资质")
   @PostMapping(value="/findByMemberId")
   public ItemResult<List<TaxInfoDetailDTO>> findByMemberId(@Parameter(hidden = true) LoginInfo loginInfo){
      return new ItemResult<>(iTaxInfoService.findByMemberId(loginInfo.getMemberId()));
   }

   @Operation(summary = "根据公司名称或纳税人识别码查找公司名称和纳税人识别码")
   @GetMapping(value="/findByCompanyNameOrCode")
   public ItemResult<List<Map<String,String>>> findByCompanyNameOrCode(@Parameter(hidden = true) LoginInfo loginInfo,@Parameter(name = "keyWord", description = "关键字") String keyWord){
      return new ItemResult<>(iTaxInfoService.findByCompanyNameOrCode(keyWord));
   }


}
