package com.ecommerce.web.seller.controller;


import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.common.result.ItemResult;
import com.ecommerce.trace.api.dto.delivery.TraceDeliveryNotePlanQueryDTO;
import com.ecommerce.trace.api.dto.delivery.TraceDeliveryPlanPathDTO;
import com.ecommerce.trace.api.dto.location.GpsLocationDTO;
import com.ecommerce.trace.api.service.ITraceDeliveryNoteService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * @Created锛�Thu Jun 27 18:57:42 CST 2019
 * <AUTHOR>
 * @Version:2
 * @Description:: 送货单服务接口
*/

@RestController
@Tag(name = "TraceDeliveryNote", description = ": 送货单服务接口")
@RequestMapping("/traceDeliveryNote")
public class TraceDeliveryNoteController {

   @Autowired 
   private ITraceDeliveryNoteService iTraceDeliveryNoteService;

   @Operation(summary = "送货单规划路线查询")
   @PostMapping(value="/planQuery")
   public ItemResult<TraceDeliveryPlanPathDTO> planQuery(@RequestBody TraceDeliveryNotePlanQueryDTO traceDeliveryNotePlanQueryDTO)throws Exception{
      return iTraceDeliveryNoteService.planQuery(traceDeliveryNotePlanQueryDTO);
   }

   @Operation(summary = "获取送货单车辆")
   @PostMapping(value="/getRealTimeLocation")
   public ItemResult<GpsLocationDTO> getRealTimeLocation(@RequestBody TraceDeliveryNotePlanQueryDTO traceDeliveryNotePlanQueryDTO)throws Exception{
      return iTraceDeliveryNoteService.getRealTimeLocation(traceDeliveryNotePlanQueryDTO);
   }



}
