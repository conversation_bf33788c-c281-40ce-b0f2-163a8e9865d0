package com.ecommerce.web.seller.controller;


import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.common.result.ItemResult;
import com.ecommerce.trace.api.dto.fence.*;
import com.ecommerce.trace.api.service.ITraceFenceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * 位置信息保存在hbase中之后,把geohash值作为rowkey的一部分,并把geohash保存到提货点/卸货点的表中,如此一来电子围栏就跟地址坐标关联上
 *
 * <AUTHOR>
*/

@RestController
@Tag(name = "TraceFence", description = "位置信息保存在hbase中之后,把geohash值作为rowkey的一部分,并把geohash保存到提货点/卸货点的表中,如此一来电子围栏就跟地址坐标关联上")
@RequestMapping("/traceFence")
public class TraceFenceController {

   @Autowired 
   private ITraceFenceService iTraceFenceService;

   @Operation(summary = "删除围栏")
   @PostMapping(value="/deleteFence")
   public ItemResult<Boolean> deleteFence(@RequestBody FenceEditDTO arg0){
      return iTraceFenceService.deleteFence(arg0);
   }

   @Operation(summary = "根fenceId查询围栏")
   @PostMapping(value="/queryByFenceId")
   public ItemResult<FenceDTO> queryByFenceId(@Parameter(name = "fenceId", description = "围栏Id") @RequestParam String fenceId){
      FenceQueryDTO fenceQueryDTO = new FenceQueryDTO();
      fenceQueryDTO.setFenceId(fenceId);
      return iTraceFenceService.queryByFenceId(fenceQueryDTO);
   }


   @Operation(summary = "添加围栏")
   @PostMapping(value="/addFence")
   public ItemResult<String> addFence(@RequestBody FenceInsertDTO arg0){
      return iTraceFenceService.addFence(arg0);
   }


   @Operation(summary = "更新围栏")
   @PostMapping(value="/updateFence")
   public ItemResult<Boolean> updateFence(@RequestBody FenceUpdateDTO arg0){
      return iTraceFenceService.updateFence(arg0);
   }



}
