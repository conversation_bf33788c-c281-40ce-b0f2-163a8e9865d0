package com.ecommerce.web.seller.controller;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.transportcategory.CategoryColDTO;
import com.ecommerce.logistics.api.service.ITransportCategoryService;
import com.ecommerce.logistics.api.service.IWaybillService;
import com.ecommerce.member.api.session.LoginInfo;
import com.ecommerce.trace.api.dto.alarm.CheckWaybillNumWarnQueryDTO;
import com.ecommerce.trace.api.dto.alarm.WaybillWarnCheckDTO;
import com.ecommerce.trace.api.dto.location.GpsLocationDTO;
import com.ecommerce.trace.api.dto.realtime.RealTimeMonitorQueryDTO;
import com.ecommerce.trace.api.dto.vehicle.VehicleLocationDTO;
import com.ecommerce.trace.api.dto.vehicle.VehicleLocationQueryDTO;
import com.ecommerce.trace.api.dto.waybill.WaybillLocationDTO;
import com.ecommerce.trace.api.service.ITracePositionService;
import com.ecommerce.trace.api.service.ITraceVehicleService;
import com.ecommerce.trace.api.service.ITraceWarnRecordService;
import com.ecommerce.trace.api.service.ITraceWaybillService;
import com.ecommerce.web.seller.dto.logistics.VehicleLocationWithWarnDTO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * @Created锛�Wed Sep 12 10:46:09 CST 2018
 * <AUTHOR>
 * @Version:2
 * @Description:: ITracePositionService
 */

@RestController
@Tag(name = "TracePosition", description = ": ITracePositionService")
@RequestMapping("/tracePosition")
public class TracePositionController {

	@Autowired
	private ITracePositionService iTracePositionService;

	@Autowired
	private ITraceWaybillService iTraceWaybillService;

	@Autowired
	private IWaybillService lgsWaybillService;

	@Autowired
	private ITraceWarnRecordService traceWarnRecordService;

	@Autowired
	private ITransportCategoryService transportCategoryService;
	
	@Autowired
	private ITraceVehicleService iTraceVehicleService;

	@Operation(summary = "根据运单查询车辆实时位置")
	@PostMapping(value = "/getLocationByWaybillMappingId")
	public ItemResult<WaybillLocationDTO> getLocationByWaybillMappingId(
			@Parameter(name = "arg0", description = "运单Id") @RequestParam String arg0) {
		return iTraceWaybillService.getLocationByWaybillMappingId(arg0);
	}

	@Operation(summary = "查询车辆位置(运单)")
	@PostMapping(value = "/getVehicleLocation")
	public ItemResult<PageData<VehicleLocationWithWarnDTO>> getVehicleLocation(LoginInfo loginInfo,
			@RequestBody VehicleLocationQueryDTO vehicleLocationQueryDTO) {
		vehicleLocationQueryDTO.setSellerId(loginInfo.getMemberId());
		ItemResult<PageData<VehicleLocationDTO>> vehicleLocationResult = iTracePositionService
				.getVehicleLocation(vehicleLocationQueryDTO);

		if (vehicleLocationResult == null || vehicleLocationResult.getData() == null
				|| CollectionUtils.isEmpty(vehicleLocationResult.getData().getList())) {
			return new ItemResult<>(new PageData<>());
		}

		Date lastDate = null;
		if (vehicleLocationQueryDTO.getIntervalSeconds() != null && vehicleLocationQueryDTO.getIntervalSeconds() > 0) {
			LocalDateTime lastDT = LocalDateTime.now().minusSeconds(vehicleLocationQueryDTO.getIntervalSeconds());
			lastDate = Date.from(lastDT.atZone(ZoneId.systemDefault()).toInstant());
		}

		CheckWaybillNumWarnQueryDTO checkWaybillNumWarnQueryDTO = new CheckWaybillNumWarnQueryDTO();
		checkWaybillNumWarnQueryDTO.setLastDate(lastDate);
		List<VehicleLocationDTO> vehicleLocationDTOList = vehicleLocationResult.getData().getList();
		List<String> waybillNumList = vehicleLocationDTOList.stream().map(VehicleLocationDTO::getWaybillNum).distinct()
				.toList();
		checkWaybillNumWarnQueryDTO.setWaybillNumList(waybillNumList);
		ItemResult<List<WaybillWarnCheckDTO>> warnListResult = traceWarnRecordService
				.collectWarnByWaybillNumList(checkWaybillNumWarnQueryDTO);
		Map<String, WaybillWarnCheckDTO> waybillNumToWarnMap = warnListResult.getData().stream()
				.collect(Collectors.toMap(WaybillWarnCheckDTO::getWaybillNum, Function.identity()));

		ItemResult<List<CategoryColDTO>> categoryColResult = transportCategoryService
				.queryWaybillTransportCategoryMap(waybillNumList);
		Map<String, String> waybillNum2CategoryMap = Maps.newHashMap();

		if (categoryColResult != null && CollectionUtils.isNotEmpty(categoryColResult.getData())) {
			for (CategoryColDTO categoryColDTO : categoryColResult.getData()) {
				waybillNum2CategoryMap.put(categoryColDTO.getWaybillNum(), categoryColDTO.getTransportCategory());
			}
		}


		PageData<VehicleLocationWithWarnDTO> page = new PageData<>();
		BeanUtils.copyProperties(vehicleLocationResult.getData(), page, "list");
		List<VehicleLocationWithWarnDTO> resultList = Lists.newArrayList();
		for (VehicleLocationDTO locationDTO : vehicleLocationDTOList) {
			VehicleLocationWithWarnDTO withWarnDTO = new VehicleLocationWithWarnDTO();
			BeanUtils.copyProperties(locationDTO, withWarnDTO);
			withWarnDTO.setTransportCategory(waybillNum2CategoryMap.get(locationDTO.getWaybillNum()));
			WaybillWarnCheckDTO waybillWarnCheckDTO = waybillNumToWarnMap.get(locationDTO.getWaybillNum());
			if (waybillWarnCheckDTO != null) {
				withWarnDTO.setNewWarnCount(waybillWarnCheckDTO.getNewWarnCount());
				withWarnDTO.setWaybillWarnRecordDTO(waybillWarnCheckDTO);
			}
			resultList.add(withWarnDTO);
		}
		page.setList(resultList);

		ItemResult<PageData<VehicleLocationWithWarnDTO>> result = new ItemResult<>();
		BeanUtils.copyProperties(vehicleLocationResult, result, "data");
		result.setData(page);
		return result;
	}

	@Operation(summary = "查询实时监控车辆位置(车辆)")
	@PostMapping(value = "/getRealTimeLocation")
	public ItemResult<List<GpsLocationDTO>> getRealTimeLocation(
			@RequestBody RealTimeMonitorQueryDTO realTimeMonitorQueryDTO) {
		return iTracePositionService.getRealTimeLocation(realTimeMonitorQueryDTO);
	}
	
}
