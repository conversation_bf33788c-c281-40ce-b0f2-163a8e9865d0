package com.ecommerce.web.seller.controller;


import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.common.result.ItemResult;
import com.ecommerce.trace.api.dto.plan.PlanRouteDTO;
import com.ecommerce.trace.api.service.ITraceRoutePlanService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


/**
 * @Created锛�Mon Oct 08 11:22:08 CST 2018
 * <AUTHOR>
 * @Version:2
 * @Description:路线规划服务
*/

@RestController
@Tag(name = "TraceRoutePlan", description = "路线规划服务")
@RequestMapping("/traceRoutePlan")
public class TraceRoutePlanController {

   @Autowired 
   private ITraceRoutePlanService iTraceRoutePlanService;

   @Operation(summary = "查询运单的规划路线")
   @PostMapping(value="/queryPlanRouteByWaybillId")
   public ItemResult<PlanRouteDTO> queryPlanRouteByWaybillId(@Parameter(name = "waybillId", description = "运单Id") @RequestParam String waybillId){
      ItemResult<PlanRouteDTO> itemResult = iTraceRoutePlanService.queryPlanRouteByWaybillId(waybillId);
      if (itemResult != null && !itemResult.isSuccess()){
         return new ItemResult<>(null);
      }
      if (itemResult != null && itemResult.getData() != null) {
         itemResult.getData().setWayPointLocationList(null);
      }
      return itemResult;
   }




}
