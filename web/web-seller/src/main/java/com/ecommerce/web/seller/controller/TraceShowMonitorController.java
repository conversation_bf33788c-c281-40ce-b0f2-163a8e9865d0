package com.ecommerce.web.seller.controller;


import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.common.enums.AppNames;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.logistics.api.dto.shipbill.ShipBillBriCondDTO;
import com.ecommerce.logistics.api.dto.shipbill.ShipBillBriResDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleOptionsDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleOptionsQueryDTO;
import com.ecommerce.logistics.api.enums.UserRoleEnum;
import com.ecommerce.logistics.api.service.IShipBillService;
import com.ecommerce.logistics.api.service.IVehicleService;
import com.ecommerce.member.api.session.LoginInfo;
import com.ecommerce.trace.api.dto.fence.RegionQueryDTO;
import com.ecommerce.trace.api.dto.location.GpsLocationDTO;
import com.ecommerce.trace.api.dto.location.WaybillTracePointDTO;
import com.ecommerce.trace.api.dto.show.HistoryMonitorCondDTO;
import com.ecommerce.trace.api.dto.show.HistoryMonitorResDTO;
import com.ecommerce.trace.api.dto.vehicle.TraceVehicleStatusDTO;
import com.ecommerce.trace.api.service.ITraceShowMonitorService;
import com.ecommerce.trace.api.service.ITraceVehicleService;
import com.ecommerce.web.seller.dto.logistics.ExtGpsLocationDTO;
import com.google.common.collect.Maps;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 物流监控信息展现服务
 *
 * <AUTHOR>
 */

@Slf4j
@Tag(name = "TraceShowMonitor", description = "物流监控信息展现服务")
@RestController
@RequestMapping("/traceShowMonitor")
public class TraceShowMonitorController {

   @Autowired
   private ITraceShowMonitorService iTraceShowMonitorService;

   @Autowired
   private ITraceVehicleService traceVehicleService;

   @Autowired
   private IVehicleService vehicleService;

   @Autowired
   private IShipBillService shipBillService;

   @Operation(summary = "查询车辆历史记录")
   @PostMapping(value="/queryVehicleHistory")
   public ItemResult<HistoryMonitorResDTO> queryVehicleHistory(@RequestBody HistoryMonitorCondDTO historyMonitorCondDTO){
      return iTraceShowMonitorService.queryVehicleHistory(historyMonitorCondDTO);
   }


   @Operation(summary = "区域查询车辆")
   @PostMapping(value="/queryVehicleByFence")
   public ItemResult<List<GpsLocationDTO>> queryVehicleByFence(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody RegionQueryDTO regionQueryDTO){
      ItemResult<List<GpsLocationDTO>> locationResult = iTraceShowMonitorService.queryVehicleByFence(regionQueryDTO);
      if (locationResult == null || CollectionUtils.isEmpty(locationResult.getData())) {
         return locationResult;
      }
      List<GpsLocationDTO> gpsLocationDTOList = locationResult.getData();
      Map<String, GpsLocationDTO> number2LocationMap = Maps.newLinkedHashMap();
      List<String> vehicleNumList = Lists.newArrayList();
      for (GpsLocationDTO gpsLocationDTO : gpsLocationDTOList) {
          if (gpsLocationDTO == null || CsStringUtils.isBlank(gpsLocationDTO.getPlateNumber())) {
            continue;
         }
         vehicleNumList.add(gpsLocationDTO.getPlateNumber());
         number2LocationMap.put(gpsLocationDTO.getPlateNumber(), gpsLocationDTO);
      }
      VehicleOptionsQueryDTO vehicleOptionsQueryDTO = new VehicleOptionsQueryDTO();
      vehicleOptionsQueryDTO.setUserId(loginInfo.getMemberId());
      vehicleOptionsQueryDTO.setUserType(UserRoleEnum.SELLER.getCode());
      vehicleOptionsQueryDTO.setVehicleNumList(vehicleNumList);
      ItemResult<List<VehicleOptionsDTO>> optionResult = vehicleService.queryVehicleOptions(vehicleOptionsQueryDTO);
      if (optionResult == null || CollectionUtils.isEmpty(optionResult.getData())) {
         locationResult.setData(Lists.newArrayList());
         return locationResult;
      }
       List<String> userVehicleNumList = optionResult.getData().stream().map(VehicleOptionsDTO::getLabel).filter(CsStringUtils::isNotBlank).toList();
      vehicleNumList.removeAll(userVehicleNumList);
      if (CollectionUtils.isNotEmpty(vehicleNumList)) {
         List<GpsLocationDTO> gpsTrueList = number2LocationMap.entrySet()
                 .stream()
                 .filter(item -> !vehicleNumList.contains(item.getKey()))
                 .map(Map.Entry::getValue)
                 .toList();
         locationResult.setData(gpsTrueList);
      }
      return locationResult;
   }

   @Operation(summary = "查询车辆指定rowKey的点详情")
   @PostMapping(value="/queryVehicleTimePointDetailByRowKey")
   public ItemResult<ExtGpsLocationDTO> queryVehicleTimePointDetailByRowKey(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody ShipBillBriCondDTO shipBillBriCondDTO) {
      ExtGpsLocationDTO extDTO = new ExtGpsLocationDTO();
      ItemResult<GpsLocationDTO> rowKeyResult = iTraceShowMonitorService.queryVehicleTimePointDetailByRowKey(shipBillBriCondDTO.getRowKey());
      if (rowKeyResult != null && rowKeyResult.getData() != null) {
         BeanUtils.copyProperties(rowKeyResult.getData(), extDTO);
      }
      shipBillBriCondDTO.setAccountId(loginInfo.getAccountId());
      shipBillBriCondDTO.setSellerId(loginInfo.getMemberId());
      shipBillBriCondDTO.setAppName(AppNames.WEB_SERVICE_SELLER.getCode());
      ShipBillBriResDTO resDTO = shipBillService.queryShipBillBriByCond(shipBillBriCondDTO);
      if (resDTO != null) {
         BeanUtils.copyProperties(resDTO, extDTO);
      }
      log.info("查询车辆指定点:{}=>{}", shipBillBriCondDTO, extDTO);
      return new ItemResult<>(extDTO);
   }

   @Operation(summary = "查询车辆信号状态")
   @PostMapping(value="/selectVehicleSignalStatus")
   public ItemResult<List<TraceVehicleStatusDTO>> selectVehicleSignalStatus(@Parameter(description = "车牌号集合") @RequestBody List<String> plateNumberList) {
      return new ItemResult<>(traceVehicleService.selectVehicleSignalStatus(plateNumberList));
   }

   /**
    * 查询运单指定的一个点
    * @param rowKey
    * @return
    */
   @Operation(summary = "查询运单指定的一个点")
   @PostMapping(value="/getWaybillTracePointByRowKey")
   public ItemResult<WaybillTracePointDTO> getWaybillTracePointByRowKey(@RequestParam String rowKey) {
      return iTraceShowMonitorService.getWaybillTracePointByRowKey(rowKey);
   }

}
