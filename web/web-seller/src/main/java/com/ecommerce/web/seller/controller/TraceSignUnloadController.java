package com.ecommerce.web.seller.controller;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.member.api.session.LoginInfo;
import com.ecommerce.trace.api.dto.signunload.SignUnloadAddDTO;
import com.ecommerce.trace.api.dto.signunload.SignUnloadDTO;
import com.ecommerce.trace.api.dto.signunload.SignUnloadMonitorDTO;
import com.ecommerce.trace.api.dto.signunload.SignUnloadQueryDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;


/**
 * @Created：Wed Sep 05 17:03:07 CST 2018
 * <AUTHOR>
 * @Version:2
 * @Description:: ITraceUnloadPointService - 卸货点服务
 */

@RestController
@Tag(name = "TraceSignUnload", description = ": ITraceUnloadPointService - 卸货点服务")
@RequestMapping("/traceSignUnload")
public class TraceSignUnloadController {

    @Operation(summary = "根据条件查询 卸货点列表")
    @PostMapping(value = "/queryByCondition")
    public ItemResult<PageData<SignUnloadDTO>> queryByCondition(LoginInfo loginInfo, @RequestBody SignUnloadQueryDTO arg0) {
        arg0.setSellerId(loginInfo.getMemberId());
        return new ItemResult<>(new PageData<>(Collections.emptyList()));
    }

    @Operation(summary = "添加卸货点")
    @PostMapping(value = "/addUnloadPoint")
    public ItemResult<Boolean> addUnloadPoint(LoginInfo loginInfo, @RequestBody SignUnloadAddDTO signUnloadAddDTO) {
        signUnloadAddDTO.setSellerId(loginInfo.getMemberId());
        signUnloadAddDTO.setSellerName(loginInfo.getMemberName());
        return new ItemResult<>(Boolean.TRUE);
    }

    @Operation(summary = "卸货点 设置监控或者取消监控")
    @PostMapping(value = "/setOrCancelMonitor")
    public ItemResult<Boolean> setOrCancelMonitor(@RequestBody SignUnloadMonitorDTO arg0) {
        return new ItemResult<>(Boolean.TRUE);
    }

}
