package com.ecommerce.web.seller.controller;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.transportaddress.*;
import com.ecommerce.logistics.api.enums.UserRoleEnum;
import com.ecommerce.logistics.api.service.ITransportAddressService;
import com.ecommerce.member.api.session.LoginInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <AUTHOR>
 * Description:
 * Date: Create in 下午5:26 18/12/21
 */
@RestController
@Tag(name = "TransportAddress", description = "运输地址")
@RequestMapping("/transportAddress")
public class TransportAddressController {

    @Autowired
    private ITransportAddressService transportAddressService;

    @Operation(summary = "添加运输地址")
    @PostMapping(value="/addTransportAddress")
    public ItemResult<String> addTransportAddress(@Parameter(hidden = true) LoginInfo loginInfo,
                                           @RequestBody TransportAddressSaveDTO transportAddressSaveDTO) {
        transportAddressSaveDTO.setUserId(loginInfo.getMemberId());
        transportAddressSaveDTO.setUserName(loginInfo.getMemberName());
        transportAddressSaveDTO.setUserType(UserRoleEnum.SELLER.getCode());
        transportAddressSaveDTO.setOperatorUserId(loginInfo.getAccountId());
        transportAddressSaveDTO.setOperatorUserName(loginInfo.getAccountName());

        return transportAddressService.addTransportAddress(transportAddressSaveDTO);
    }

    @Operation(summary = "删除运输地址")
    @PostMapping(value="/deleteTransportAddress")
    public ItemResult<Void> deleteTransportAddress(@Parameter(hidden = true) LoginInfo loginInfo,
                                            @RequestBody TransportAddressDeleteDTO transportAddressDeleteDTO) {
        transportAddressDeleteDTO.setOperatorUserId(loginInfo.getAccountId());
        transportAddressDeleteDTO.setOperatorUserName(loginInfo.getAccountName());

        return transportAddressService.deleteTransportAddress(transportAddressDeleteDTO);
    }

    @Operation(summary = "编辑运输地址")
    @PostMapping(value="/editTransportAddress")
    public ItemResult<Void> editTransportAddress(@Parameter(hidden = true) LoginInfo loginInfo,
                                          @RequestBody TransportAddressSaveDTO transportAddressSaveDTO) {
        transportAddressSaveDTO.setOperatorUserId(loginInfo.getAccountId());
        transportAddressSaveDTO.setOperatorUserName(loginInfo.getAccountName());
        return transportAddressService.editTransportAddress(transportAddressSaveDTO);
    }

    @Operation(summary = "查询运输地址详情")
    @PostMapping(value="/queryTransportAddressDetail")
    public ItemResult<TransportAddressDetailDTO> queryTransportAddressDetail(@Parameter(description = "运输地址ID") @RequestParam String transportAddressId) {
        return transportAddressService.queryTransportAddressDetail(transportAddressId);
    }

    @Operation(summary = "查询运输地址列表")
    @PostMapping(value="/queryTransportAddressList")
    public ItemResult<PageData<TransportAddressListDTO>> queryTransportAddressList(@Parameter(hidden = true) LoginInfo loginInfo,
                                                                            @RequestBody PageQuery<TransportAddressQueryDTO> pageQuery) {
        if (pageQuery.getQueryDTO() == null) {
            pageQuery.setQueryDTO(new TransportAddressQueryDTO());
        }
        pageQuery.getQueryDTO().setUserId(loginInfo.getMemberId());
        pageQuery.getQueryDTO().setUserType(UserRoleEnum.SELLER.getCode());
        return transportAddressService.queryTransportAddressList(pageQuery);
    }

    @Operation(summary = "搜索运输地址列表")
    @PostMapping(value="/searchTransportAddressList")
    public ItemResult<List<TransportAddressListDTO>> searchTransportAddressList(@Parameter(hidden = true) LoginInfo loginInfo,
                                                                         @RequestBody TransportAddressSearchDTO transportAddressSearchDTO) {
        transportAddressSearchDTO.setUserId(loginInfo.getMemberId());
        transportAddressSearchDTO.setUserType(UserRoleEnum.SELLER.getCode());
        return transportAddressService.searchTransportAddressList(transportAddressSearchDTO);
    }
}
