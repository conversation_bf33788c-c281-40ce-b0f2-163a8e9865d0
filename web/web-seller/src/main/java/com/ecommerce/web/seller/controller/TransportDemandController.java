package com.ecommerce.web.seller.controller;

import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.transportdemand.*;
import com.ecommerce.logistics.api.enums.UserRoleEnum;
import com.ecommerce.logistics.api.service.ITransportDemandService;
import com.ecommerce.member.api.session.LoginInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * <AUTHOR>
 * Description:
 * Date: Create in 下午5:04 18/12/21
 */
@RestController
@Tag(name = "TransportDemand", description = "运输需求")
@RequestMapping("/transportDemand")
public class TransportDemandController {

    @Autowired
    private ITransportDemandService transportDemandService;

    @Operation(summary = "发布运输需求")
    @PostMapping(value="/addTransportDemand")
    public ItemResult<String> addTransportDemand(@Parameter(hidden = true) LoginInfo loginInfo,
                                          @RequestBody TransportDemandSaveDTO transportDemandSaveDTO) {
        transportDemandSaveDTO.setUserId(loginInfo.getMemberId());
        transportDemandSaveDTO.setUserName(loginInfo.getMemberName());
        transportDemandSaveDTO.setUserType(UserRoleEnum.SELLER.getCode());
        transportDemandSaveDTO.setOperatorUserId(loginInfo.getAccountId());
        transportDemandSaveDTO.setOperatorUserName(loginInfo.getAccountName());

        return transportDemandService.addTransportDemand(transportDemandSaveDTO);
    }

    @Operation(summary = "删除运输需求")
    @PostMapping(value="/deleteTransportDemand")
    public ItemResult<Void> deleteTransportDemand(@Parameter(hidden = true) LoginInfo loginInfo,
                                           @RequestBody TransportDemandDeleteDTO transportDemandDeleteDTO) {
        transportDemandDeleteDTO.setOperatorUserId(loginInfo.getAccountId());
        transportDemandDeleteDTO.setOperatorUserName(loginInfo.getAccountName());
        return transportDemandService.deleteTransportDemand(transportDemandDeleteDTO);
    }

    @Operation(summary = "编辑运输需求")
    @PostMapping(value="/editTransportDemand")
    public ItemResult<Void> editTransportDemand(@Parameter(hidden = true) LoginInfo loginInfo,
                                         @RequestBody TransportDemandSaveDTO transportDemandSaveDTO) {
        transportDemandSaveDTO.setOperatorUserId(loginInfo.getAccountId());
        transportDemandSaveDTO.setOperatorUserName(loginInfo.getAccountName());
        return transportDemandService.editTransportDemand(transportDemandSaveDTO);
    }

    @Operation(summary = "查询运输需求详情")
    @PostMapping(value="/queryTransportDemandDetail")
    public ItemResult<TransportDemandDetailDTO> queryTransportDemandDetail(@Parameter(description = "运输需求ID") @RequestParam String transportDemandId) {
        return transportDemandService.queryTransportDemandDetail(transportDemandId);
    }

    @Operation(summary = "查询运输需求列表")
    @PostMapping(value="/queryTransportDemandList")
    public ItemResult<PageData<TransportDemandListDTO>> queryTransportDemandList(@Parameter(hidden = true) LoginInfo loginInfo,
                                                                          @RequestBody PageQuery<TransportDemandQueryDTO> pageQuery) {
        if (pageQuery.getQueryDTO() == null) {
            pageQuery.setQueryDTO(new TransportDemandQueryDTO());
        }
        pageQuery.getQueryDTO().setUserId(loginInfo.getMemberId());
        pageQuery.getQueryDTO().setUserType(UserRoleEnum.SELLER.getCode());
        return transportDemandService.queryTransportDemandList(pageQuery);
    }
}
