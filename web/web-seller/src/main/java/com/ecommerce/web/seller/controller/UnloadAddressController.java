package com.ecommerce.web.seller.controller;


import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.base.api.dto.unloadaddress.UnloadAddressDTO;
import com.ecommerce.base.api.service.IUnloadAddressService;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.member.api.session.LoginInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * 收货地址相关service
 *
 * <AUTHOR>
*/

@RestController
@Tag(name = "UnloadAddress", description = "ERP卸货地")
@RequestMapping("/unloadAddress")
public class UnloadAddressController {

   @Autowired
   private IUnloadAddressService unloadAddressService;

   @Operation(summary = "根据卖家和关键字查询")
   @PostMapping(value="/findBySellerId")
   public ItemResult<List<UnloadAddressDTO>> findBySellerId(@Parameter(hidden = true) LoginInfo loginInfo,
                                                            @Parameter(name = "buyerId", description = "买家Id") @RequestParam String buyerId,
                                                            @Parameter(name = "keyword", description = "关键字") @RequestParam(required = false) String keyword){
      return new ItemResult<>(unloadAddressService.findBySellerIdAndBuyerId(loginInfo.getMemberId(),buyerId,keyword));
   }

   @Operation(summary = "同步卸货地")
   @PostMapping(value="/syncERPUnloadAddress")
   public ItemResult<Object> syncERPUnloadAddress(@Parameter(hidden = true) LoginInfo loginInfo, @Parameter(name = "buyerId", description = "买家Id") @RequestParam String buyerId){
      unloadAddressService.syncERPUnloadAddress(loginInfo.getMemberId(),buyerId,loginInfo.getAccountId());
      return new ItemResult<>(new Object());
   }
}
