package com.ecommerce.web.seller.controller;


import cn.hutool.core.text.CharSequenceUtil;
import com.ecommerce.base.api.dto.cloud.AttachmentinfoDTO;
import com.ecommerce.base.api.service.IAttachmentService;
import com.ecommerce.common.exception.BasicCode;
import com.ecommerce.common.exception.BizException;
import com.ecommerce.common.exception.BusinessOperationException;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.common.utils.CsStringUtils;
import com.ecommerce.logistics.api.dto.attachment.AttListDTO;
import com.ecommerce.logistics.api.dto.base.PageQuery;
import com.ecommerce.logistics.api.dto.driver.BindDriverDTO;
import com.ecommerce.logistics.api.dto.shipping.ShippingInfoListQueryDTO;
import com.ecommerce.logistics.api.dto.vehicle.UserVehicleListDTO;
import com.ecommerce.logistics.api.dto.vehicle.UserVehicleListQueryDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleAddDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleAppDataDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleBaseDataDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleBatchAddResultDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleBuyerTakeQueryDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleDetailDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleDisableFlgEditDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleEditDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleImportTemplateOptionDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleListDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleListQueryDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleOptionsDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleOptionsQueryDTO;
import com.ecommerce.logistics.api.dto.vehicle.VehicleRemoveDTO;
import com.ecommerce.logistics.api.enums.UserRoleEnum;
import com.ecommerce.logistics.api.service.IShippingInfoService;
import com.ecommerce.logistics.api.service.IVehicleService;
import com.ecommerce.member.api.dto.account.AccountDTO;
import com.ecommerce.member.api.dto.account.AccountSearchDTO;
import com.ecommerce.member.api.service.IAccountService;
import com.ecommerce.member.api.service.IMemberService;
import com.ecommerce.member.api.session.LoginInfo;
import com.ecommerce.trace.api.dto.vehicle.TraceVehicleBatchDeleteDTO;
import com.ecommerce.trace.api.dto.vehicle.TraceVehicleDTO;
import com.ecommerce.trace.api.dto.vehicle.TraceVehicleEditDTO;
import com.ecommerce.trace.api.dto.vehicle.TraceVehicleStatusDTO;
import com.ecommerce.trace.api.service.ITraceVehicleService;
import com.ecommerce.web.seller.dto.logistics.DriverDTO;
import com.ecommerce.web.seller.dto.logistics.ExtVehicleListDTO;
import com.ecommerce.web.seller.util.VehicleExcelUtil;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @Date: 22/08/2018 09:12
 * @Description:
 */
@RestController
@RequestMapping(value = "/vehicle/")
@CrossOrigin
@Slf4j
@Tag(name = "vehicle", description = "车辆服务")
public class VehicleController {

    @Autowired
    private IVehicleService vehicleService;

    @Autowired
    private ITraceVehicleService traceVehicleService;

    @Autowired
    private IAttachmentService attachmentService;

    @Autowired
    private IAccountService iAccountService;

    @Autowired
    private IMemberService memberService;

    @Autowired
    private IShippingInfoService shippingInfoService;

    private static final String LICENSE_PLATE_NUMBER_ONE = "(^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼]{1})";

    @Operation(summary = "获取车辆列表")
    @PostMapping("queryVehicleList")
    public ItemResult<PageData<ExtVehicleListDTO>> queryVehicleList(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody PageQuery<VehicleListQueryDTO> pageQuery) {
        ItemResult<PageData<ExtVehicleListDTO>> result = new ItemResult<>(new PageData<>(new PageInfo<>(Lists.newArrayList())));
        pageQuery.getQueryDTO().setUserId(loginInfo.getMemberId());
        pageQuery.getQueryDTO().setUserType(UserRoleEnum.SELLER.getCode());
        ItemResult<PageData<VehicleListDTO>> pageDataItemResult = vehicleService.queryVehicleList(pageQuery);
        if (pageDataItemResult == null || pageDataItemResult.getData() == null || CollectionUtils.isEmpty(pageDataItemResult.getData().getList())) {
            result.setSuccess(true);
            result.setDescription("查询到的车辆列表为空");
            return result;
        }
        List<VehicleListDTO> vehicleListDTOS = pageDataItemResult.getData().getList();
        List<TraceVehicleStatusDTO> traceVehicleStatusDTOS = traceVehicleService.selectVehicleSignalStatus(vehicleListDTOS.stream().map(VehicleListDTO::getNumber).toList());
        Map<String, String> plateNumber2SignalStatusMap = traceVehicleStatusDTOS.stream().collect(Collectors.toMap(TraceVehicleStatusDTO::getPlateNumber, TraceVehicleStatusDTO::getSignalStatus));
        List<ExtVehicleListDTO> extVehicleListDTOList = Lists.newArrayList();
        for (VehicleListDTO lgsDto : vehicleListDTOS) {
            ExtVehicleListDTO extVehicleListDTO = new ExtVehicleListDTO();
            BeanUtils.copyProperties(lgsDto, extVehicleListDTO);
            extVehicleListDTO.setSignalStatus(plateNumber2SignalStatusMap.get(lgsDto.getNumber()));
            extVehicleListDTOList.add(extVehicleListDTO);
        }

        BeanUtils.copyProperties(pageDataItemResult, result, "data");

        PageData<ExtVehicleListDTO> extPage = new PageData<>();
        BeanUtils.copyProperties(pageDataItemResult.getData(), extPage, "list");
        extPage.setList(extVehicleListDTOList);
        result.setData(extPage);
        return result;
    }

    @Operation(summary = "编辑车辆")
    @PostMapping("modifyVehicle")
    public ItemResult<Void> modifyVehicle(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody VehicleEditDTO vehicleEditDTO) {
        TraceVehicleEditDTO traceVehicleEditDTO = new TraceVehicleEditDTO();
        vehicleEditDTO.setUpdateUser(loginInfo.getMemberId());
        BeanUtils.copyProperties(vehicleEditDTO, traceVehicleEditDTO);
        if (vehicleEditDTO.getAxles() != null) {
            traceVehicleEditDTO.setAxle(vehicleEditDTO.getAxles());
        }
        if (vehicleEditDTO.getGpsDeviceNumber() != null) {
            traceVehicleEditDTO.setGpsSerial(vehicleEditDTO.getGpsDeviceNumber());
        }
        vehicleEditDTO.setOperationUserId(loginInfo.getAccountId());
        vehicleEditDTO.setOperationUserName(loginInfo.getAccountName());
        try {
            traceVehicleEditDTO.setUserId(loginInfo.getMemberId());
            traceVehicleEditDTO.setUserName(loginInfo.getMemberName());
            traceVehicleService.updateVehicle(traceVehicleEditDTO);
        } catch (Exception e) {
            log.error("修改车辆信息同步trace异常:{}", traceVehicleEditDTO, e);
        }
        return vehicleService.modifyVehicle(vehicleEditDTO);
    }

    @Operation(summary = "删除车辆")
    @PostMapping(value = "/removeVehicle", consumes = "application/json;charset=UTF-8")
    public ItemResult<Void> removeVehicle(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody List<VehicleRemoveDTO> list) {
        TraceVehicleBatchDeleteDTO traceVehicleBatchDeleteDTO = new TraceVehicleBatchDeleteDTO();
        List<String> ids = new ArrayList<>();
        for (VehicleRemoveDTO dto : list) {
            dto.setUpdateUser(loginInfo.getMemberId());
            dto.setOperationUserId(loginInfo.getAccountId());
            dto.setOperationUserName(loginInfo.getAccountName());
            ids.add(dto.getVehicleId());
        }
        traceVehicleBatchDeleteDTO.setVehicleIds(ids);
        traceVehicleBatchDeleteDTO.setUserId(loginInfo.getMemberId());
        try {
            traceVehicleService.batchDeleteVehicle(traceVehicleBatchDeleteDTO);
        } catch (Exception e) {
            log.error("删除车辆同步trace异常:{}", traceVehicleBatchDeleteDTO, e);
        }
        return vehicleService.removeVehicle(list);
    }


    @Operation(summary = "添加车辆")
    @PostMapping(value = "/addVehicle")
    public ItemResult<String> addVehicle(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody VehicleAddDTO vehicleAddDTO) {
        vehicleAddDTO.setCreateUser(loginInfo.getMemberId());
        vehicleAddDTO.setUserName(loginInfo.getMemberName());
        vehicleAddDTO.setUserId(loginInfo.getMemberId());
        vehicleAddDTO.setUserType(UserRoleEnum.SELLER.getCode());
        vehicleAddDTO.setOperationUserId(loginInfo.getAccountId());
        vehicleAddDTO.setOperationUserName(loginInfo.getAccountName());
        TraceVehicleDTO traceVehicleDTO = new TraceVehicleDTO();
        BeanUtils.copyProperties(vehicleAddDTO, traceVehicleDTO);
        ItemResult<String> itemResult = vehicleService.addVehicle(vehicleAddDTO);
        traceVehicleDTO.setVehicleId(itemResult.getData());
        traceVehicleDTO.setGpsSerial(vehicleAddDTO.getGpsDeviceNumber());
        try {
            traceVehicleService.addVehicle(traceVehicleDTO);
        } catch (Exception e) {
            log.error("添加车辆同步trace异常:{}", traceVehicleDTO, e);
        }
        return new ItemResult<>(null);
    }


    @Operation(summary = "查询车辆详情")
    @PostMapping(value = "/queryVehicle")
    public ItemResult<VehicleDetailDTO> queryVehicle(@Parameter(name = "vehicleId", description = "车辆Id") @RequestParam String vehicleId) {
        ItemResult<VehicleDetailDTO> itemResult = vehicleService.queryVehicle(vehicleId);
        for (AttListDTO dto : itemResult.getData().getAttListDTOS()
                ) {
            List<AttachmentinfoDTO> list = attachmentService.getAttachmentByBID(dto.getBid());
            if(CollectionUtils.isNotEmpty(list)) {
                dto.setFileUrl(list.get(0).getAttcPath());
            } else {
                return null;
            }
        }
        return itemResult;
    }

    @Operation(summary = "根据Id 查询")
    @PostMapping(value = "/selectVehicleById")
    public ItemResult<VehicleDTO> selectVehicleById(@Parameter(name = "vehicleId", description = "车辆Id") @RequestParam String vehicleId) {
        return vehicleService.selectVehicleById(vehicleId);
    }

    @Operation(summary = "通过用户ID(买家，卖家，承运商)")
    @PostMapping(value = "/queryVehicleListByUserId")
    public ItemResult<PageData<UserVehicleListDTO>> queryVehicleListByUserId(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody PageQuery<UserVehicleListQueryDTO> pageQuery) {
    	if (pageQuery == null) {
    	    pageQuery = new PageQuery<>();
        }
        UserVehicleListQueryDTO queryDTO = pageQuery.getQueryDTO() == null ? new UserVehicleListQueryDTO():pageQuery.getQueryDTO();
        queryDTO.setUserId(CsStringUtils.isEmpty(queryDTO.getUserId()) ? loginInfo.getMemberId() : queryDTO.getUserId());
    	queryDTO.setUserType(queryDTO.getUserType() == null ? UserRoleEnum.SELLER.getCode() : queryDTO.getUserType());
    	return vehicleService.queryVehicleListByUserId(pageQuery);
    }

    @Operation(summary = "查询App端的车辆数据")
    @PostMapping(value = "/queryAppVehicle")
    public ItemResult<List<VehicleAppDataDTO>> queryAppVehicle(@Parameter(name = "userId", description = "用户Id") @RequestParam String userId) {
        ItemResult<List<VehicleAppDataDTO>> itemResult = vehicleService.queryAppVehicleByUserId(userId);
        log.info("----调用附件查询接口查询附件数据:{}", itemResult.getData());
        for (VehicleAppDataDTO vehicles : itemResult.getData()) {
            for (AttListDTO dto : vehicles.getAttListDTOS()
                    ) {
                String bid = dto.getBid();
                List<AttachmentinfoDTO> list = attachmentService.getAttachmentByBID(bid);
                if (CollectionUtils.isEmpty(list)) {
                    itemResult.setSuccess(false);
                    itemResult.setDescription("获取附件信息失败");
                    log.error("获取附件失败");
                } else {
                    dto.setFileUrl(list.get(0).getAttcPath());
                }
            }
        }
        return itemResult;
    }

    @Operation(summary = "获取司机下拉选的初始化数据")
    @GetMapping(value = "/getDriver")
    public ItemResult<List<DriverDTO>> getDriver(@Parameter(hidden = true) LoginInfo loginInfo) {
        AccountSearchDTO searchDTO = new AccountSearchDTO();
        searchDTO.setMemberId(loginInfo.getMemberId());
        searchDTO.setEntDriver(true);
        searchDTO.setPageNumber(1);
        searchDTO.setPageSize(10000);
        PageInfo<AccountDTO> pageInfo = iAccountService.findAll(searchDTO);
        List<AccountDTO> list = pageInfo.getList();
        DriverDTO driverDTO = null;
        List<DriverDTO> driverOptions = new ArrayList<>();
        for (AccountDTO dto : list
                ) {
            driverDTO = new DriverDTO();
            driverDTO.setDriverName(dto.getMemberName());
            driverDTO.setDriverPhone(dto.getMobile());
            driverOptions.add(driverDTO);
        }
        return new ItemResult<>(driverOptions);
    }

    @Operation(summary = "获取车辆下拉列表数据")
    @GetMapping(value = "/getVehicleOptions")
    public ItemResult<List<VehicleOptionsDTO>> getVehicleOptions(@Parameter(hidden = true) LoginInfo loginInfo,@Parameter(name = "keyword", description = "关键词") @RequestParam String keyword) {
        VehicleOptionsQueryDTO vehicleOptionsQueryDTO = new VehicleOptionsQueryDTO();
        vehicleOptionsQueryDTO.setUserId(loginInfo.getMemberId());
        vehicleOptionsQueryDTO.setUserType(UserRoleEnum.SELLER.getCode());
        vehicleOptionsQueryDTO.setKeyword(keyword);
        ItemResult<List<VehicleOptionsDTO>> itemResult = vehicleService.queryVehicleOptions(vehicleOptionsQueryDTO);
        return itemResult;
    }

    @Operation(summary = "获取车辆下拉列表数据")
    @PostMapping(value = "/getVehicleOptionsWithBillId")
    public ItemResult<List<VehicleOptionsDTO>> getVehicleOptionsWithBillId(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody VehicleOptionsQueryDTO vehicleOptionsQueryDTO) {
        vehicleOptionsQueryDTO.setUserId(loginInfo.getMemberId());
        vehicleOptionsQueryDTO.setUserType(UserRoleEnum.SELLER.getCode());
        ItemResult<List<VehicleOptionsDTO>> itemResult = vehicleService.queryVehicleOptions(vehicleOptionsQueryDTO);
        return itemResult;
    }

    @Operation(summary = "Excel批量导入车辆")
    @PostMapping(value = "/addVehicleByExcel")
    public ItemResult<VehicleBatchAddResultDTO> addVehicleByExcel(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody MultipartFile file) throws IOException {
        if (file == null || file.isEmpty()) {
            throw new BizException(BasicCode.INVALID_PARAM, ":上传失败，请选择文件");
        }
        if (CsStringUtils.isEmpty(file.getOriginalFilename()) || !CharSequenceUtil.endWith(file.getOriginalFilename(), ".xlsx")) {
            throw new BizException(BasicCode.INVALID_PARAM, ":导入文件格式不正确，请下载正确模板！");
        }
        List<VehicleAddDTO> vehicleAddDTOList = VehicleExcelUtil.readExcel(new XSSFWorkbook(file.getInputStream()), loginInfo);
        log.info("vehicleAddDTOList.size : {}", vehicleAddDTOList.size());
        if (vehicleAddDTOList.size() == 0) {
            throw new BizException(BasicCode.INVALID_PARAM, ":导入文件无数据，请修改后重试！");
        }

        ItemResult<VehicleBatchAddResultDTO> itemResult = vehicleService.batchAddVehicle(vehicleAddDTOList);
        log.info("result : {}", itemResult.getData());


        return itemResult;
    }

    @Operation(summary = "生成车辆导入模板Excel")
    @PostMapping(value = "/generateVehicleImportTemplate")
    public void generateVehicleImportTemplate(@Parameter(hidden = true) HttpServletResponse response) {
        VehicleImportTemplateOptionDTO optionDTO = vehicleService.queryVehicleImportTemplateOptions().getData();
        VehicleExcelUtil.exportExcel(optionDTO, response);
    }

    @Operation(summary = "批量启用或禁用车辆")
    @PostMapping(value = "/updateVehicleDisableFlg")
    public ItemResult<Void> updateVehicleDisableFlg(@Parameter(hidden = true) LoginInfo loginInfo,
                                                    @Parameter(name = "list", description = "修改车辆是否禁用DTO列表") @RequestBody List<VehicleDisableFlgEditDTO> list) {
        for (VehicleDisableFlgEditDTO dto : list) {
            dto.setUpdateUser(loginInfo.getAccountId());
            dto.setOperationUserId(loginInfo.getAccountId());
            dto.setOperationUserName(loginInfo.getAccountName());
        }
        ItemResult<Void> itemResult = vehicleService.updateVehicleDisableFlg(list);
        if (!itemResult.isSuccess()) {
            log.error("批量启用或禁用车辆失败！" + itemResult.getDescription());
            throw new BizException(BasicCode.UNKNOWN_ERROR, ":批量启用或禁用车辆失败！" + itemResult.getDescription());
        }
        return new ItemResult<>(null);
    }

    @Operation(summary = "获取买家自提车辆")
    @PostMapping(value = "/getBuyerTakeCars")
    public ItemResult<List<VehicleBaseDataDTO>> getBuyerTakeCars(@Parameter(name = "queryDTO", description = "买家自提车辆列表查询对象") @RequestBody VehicleBuyerTakeQueryDTO queryDTO) {
        return vehicleService.getBuyerTakeCars(queryDTO);
    }

    @Operation(summary = "绑定司机")
    @PostMapping(value="/bindDriver")
    public ItemResult<Void> bindDriver(@Parameter(hidden = true) LoginInfo loginInfo,@RequestBody @Valid BindDriverDTO dto) {
        dto.setOperatorId(loginInfo.getAccountId());
        dto.setOperatorName(loginInfo.getAccountName());
        return vehicleService.bindDriver(dto);
    }


    @Operation(summary = "车辆导出")
    @PostMapping(value = "/exportVehicle")
    public void exportVehicle(@Parameter(hidden = true) LoginInfo loginInfo, @Parameter(hidden = true) HttpServletResponse response,@RequestBody VehicleListQueryDTO queryDTO) {
        queryDTO.setUserId(loginInfo.getMemberId());
        queryDTO.setUserType(2);
        VehicleImportTemplateOptionDTO optionDTO = vehicleService.queryVehicleImportTemplateOptions().getData();
        List<VehicleListDTO> vehicleListDTOList = vehicleService.queryExportVehicleList(queryDTO).getData();
        if (CollectionUtils.isEmpty(vehicleListDTOList)) {
            log.info( "未查询到车辆");
            return;
        }
        VehicleExcelUtil.exportVehicleExcel(response, optionDTO, vehicleListDTOList);
    }

    @Operation(summary = "通过车牌号、船名查询")
    @PostMapping(value="/queryVehicleOrShippingListByName")
    public ItemResult<List<String>> queryVehicleOrShippingListByName(@Parameter(hidden = true) LoginInfo loginInfo,@Parameter(name = "arg0", description = "船名/车牌号") @RequestParam String arg0){
    	boolean isVehicleNumber = isVehicleNumber(arg0);
    	if (isVehicleNumber) {
    		VehicleListQueryDTO queryDTO = new VehicleListQueryDTO();
    		queryDTO.setUserId(loginInfo.getMemberId());
            //用户类型 1：买家 2：卖家 3：承运商
            queryDTO.setUserType(2);
            queryDTO.setNumber(arg0);
    		return vehicleService.selectVehicleNumber(queryDTO);
		}else {
			ShippingInfoListQueryDTO queryDTO = new ShippingInfoListQueryDTO();
			queryDTO.setMemberId(loginInfo.getMemberId());
			queryDTO.setShippingName(arg0);
			queryDTO.setManagerMemberType("20");
			return shippingInfoService.selectShippingInfoName(queryDTO);
		}
    }

    private boolean isVehicleNumber(String arg0) {
		log.info("识别入参：{} 是否为车牌号", arg0);
        if (CsStringUtils.isBlank(arg0)) {
			return true;
		}
		return Pattern.matches(LICENSE_PLATE_NUMBER_ONE, Character.toString(arg0.charAt(0)));
	}

    @Operation(summary = "获取车辆下拉列表数据")
    @PostMapping(value="/queryVehicleOptions")
    public ItemResult<List<VehicleOptionsDTO>> queryVehicleOptions(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody VehicleOptionsQueryDTO vehicleOptionsQueryDTO){
        if (vehicleOptionsQueryDTO == null) {
            vehicleOptionsQueryDTO = new VehicleOptionsQueryDTO();
        }
        vehicleOptionsQueryDTO.setUserId(loginInfo.getMemberId());
        vehicleOptionsQueryDTO.setUserType(UserRoleEnum.SELLER.getCode());
        return vehicleService.queryVehicleOptions(vehicleOptionsQueryDTO);
    }

    @Operation(summary = "通过车牌号查询车辆下拉列表")
    @PostMapping(value="/queryVehicleListDropDownBox")
    public ItemResult<List<UserVehicleListDTO>> queryVehicleListDropDownBox(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody UserVehicleListQueryDTO queryDTO){
        queryDTO.setUserId(loginInfo.getMemberId());
        //用户类型 1：买家 2：卖家 3：承运商
        queryDTO.setUserType(2);
        return vehicleService.queryVehicleListDropDownBox(queryDTO);
    }

    /*@ApiOperation("卖家给买家添加车辆 管控和非管控")
    @PostMapping(value = "/addBuyerVehicle")
    public ItemResult<Object> addBuyerVehicle(LoginInfo loginInfo, @RequestBody VehicleCommonDTO vehicleCommonDTO)
    {
        String vehicleId = "";
        TraceVehicleDTO traceVehicleDTO = new TraceVehicleDTO();

        // 获取买家名称
        MemberDetailDTO memberDetailDTO = memberService.findMemberById(vehicleCommonDTO.getUserId());
        vehicleCommonDTO.setUserName(memberDetailDTO.getMemberName());

        // 管控车辆
        if(vehicleCommonDTO.getIsMonitor() == 1)
        {
            VehicleAddDTO vehicleAddDTO = new VehicleAddDTO();
            vehicleAddDTO.setUserId(vehicleCommonDTO.getUserId());
            vehicleAddDTO.setUserName(vehicleCommonDTO.getUserName());
            vehicleAddDTO.setUserType(UserRoleEnum.BUYER.getCode());
            vehicleAddDTO.setNumber(vehicleCommonDTO.getNumber());
            vehicleAddDTO.setDriverName(vehicleCommonDTO.getDriverName());
            vehicleAddDTO.setDriverPhone(vehicleCommonDTO.getDriverPhone());
            vehicleAddDTO.setSelfCapacity(vehicleCommonDTO.getSelfCapacity() == null ? BigDecimal.ZERO : vehicleCommonDTO.getSelfCapacity());
            vehicleAddDTO.setLoadCapacity(vehicleCommonDTO.getLoadCapacity() == null ? BigDecimal.ZERO : vehicleCommonDTO.getLoadCapacity());
            vehicleAddDTO.setColor(vehicleCommonDTO.getColor());
            vehicleAddDTO.setVehicleTypeId(vehicleCommonDTO.getVehicleTypeId());
            vehicleAddDTO.setTransportCategoryId(vehicleCommonDTO.getTransportCategoryId());
            vehicleAddDTO.setGpsManufacturerId(vehicleCommonDTO.getGpsManufacturerId());
            vehicleAddDTO.setSimNumber(vehicleCommonDTO.getSimNumber());
            vehicleAddDTO.setGpsDeviceNumber(vehicleCommonDTO.getGpsDeviceNumber());
            vehicleAddDTO.setLength(vehicleCommonDTO.getLength() == null ? BigDecimal.ZERO : vehicleCommonDTO.getLength());
            vehicleAddDTO.setWidth(vehicleCommonDTO.getWidth() == null ? BigDecimal.ZERO : vehicleCommonDTO.getWidth());
            vehicleAddDTO.setHeight(vehicleCommonDTO.getHeight() == null ? BigDecimal.ZERO : vehicleCommonDTO.getHeight());
            vehicleAddDTO.setOperationUserId("[快]" + loginInfo.getAccountId());
            vehicleAddDTO.setOperationUserName("[快]" + loginInfo.getAccountName());

            log.info("管控车辆: {}", JSON.toJSONString(vehicleAddDTO));
            ItemResult<String> result = vehicleService.addVehicle(vehicleAddDTO);
            log.info("管控车辆: {}", JSON.toJSONString(result));
            if(!result.isSuccess())
            {
                throw new BizException(BasicCode.UNDEFINED_ERROR, result.getDescription());
            }

            vehicleId = result.getData();
        }
        // 非管控车辆
        else
        {
            VehicleAppAddDTO vehicleAppAddDTO = new VehicleAppAddDTO();
            vehicleAppAddDTO.setUserId(vehicleCommonDTO.getUserId());
            vehicleAppAddDTO.setUserName(vehicleCommonDTO.getUserName());
            vehicleAppAddDTO.setUserType(UserRoleEnum.BUYER.getCode());
            vehicleAppAddDTO.setNumber(vehicleCommonDTO.getNumber());
            vehicleAppAddDTO.setDriverName(vehicleCommonDTO.getDriverName());
            vehicleAppAddDTO.setDriverPhone(vehicleCommonDTO.getDriverPhone());
            vehicleAppAddDTO.setLoadCapacity(vehicleCommonDTO.getLoadCapacity() == null ? BigDecimal.ZERO : vehicleCommonDTO.getLoadCapacity());
            vehicleAppAddDTO.setColor(vehicleCommonDTO.getColor());
            vehicleAppAddDTO.setVehicleTypeId(vehicleCommonDTO.getVehicleTypeId());
            vehicleAppAddDTO.setOperationUserId("[快]" + loginInfo.getAccountId());
            vehicleAppAddDTO.setOperationUserName("[快]" + loginInfo.getAccountName());

            log.info("非管控车辆: {}", JSON.toJSONString(vehicleAppAddDTO));
            ItemResult<String> result = vehicleService.addAppVehicle(vehicleAppAddDTO);
            log.info("非管控车辆: {}", JSON.toJSONString(result));
            if(!result.isSuccess())
            {
                throw new BizException(BasicCode.UNDEFINED_ERROR, result.getDescription());
            }

            vehicleId = result.getData();
        }

        try
        {
            BeanUtils.copyProperties(vehicleCommonDTO, traceVehicleDTO);
            traceVehicleDTO.setVehicleId(vehicleId);
            traceVehicleService.addVehicle(traceVehicleDTO);
        }
        catch(Exception e)
        {
            log.error("添加车辆同步trace异常: " + JSON.toJSONString(traceVehicleDTO), e);
        }

        return new ItemResult<>(null);
    }*/

}
