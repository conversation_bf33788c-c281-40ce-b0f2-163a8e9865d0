package com.ecommerce.web.seller.controller;


import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.base.api.dto.warehouse.PageQuery;
import com.ecommerce.base.api.dto.warehouse.WarehouseAllocationDTO;
import com.ecommerce.base.api.dto.warehouse.WarehouseDetailsDTO;
import com.ecommerce.base.api.dto.warehouse.WarehouseListDTO;
import com.ecommerce.base.api.dto.warehouse.WarehouseListQueryDTO;
import com.ecommerce.base.api.dto.warehouse.WarehouseOptionDTO;
import com.ecommerce.base.api.dto.warehouse.WarehouseOptionQueryDTO;
import com.ecommerce.base.api.dto.warehouse.WarehouseSellerDTO;
import com.ecommerce.base.api.dto.warehouse.WarehouseSellerQueryDTO;
import com.ecommerce.base.api.service.IWarehouseService;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.common.result.PageData;
import com.ecommerce.logistics.api.enums.UserRoleEnum;
import com.ecommerce.member.api.dto.storeAccountRelation.AccountStoreRelationDTO;
import com.ecommerce.member.api.service.IAccountStoreRelationService;
import com.ecommerce.member.api.session.LoginInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * <AUTHOR>
 * @Date: 22/08/2018 16:45
 * @Description:
 */
@RestController
@RequestMapping("/warehouse")
@Slf4j
@Tag(name = "Warehouse", description = "仓库服务")
public class WarehouseController {

    @Autowired
    private IWarehouseService warehouseService;


    @Autowired
    private IAccountStoreRelationService iAccountStoreRelationService;

    @Operation(summary = "获取仓库列表")
    @PostMapping(value="/queryWarehouseList")
    public ItemResult<PageData<WarehouseListDTO>> queryWarehouseList(LoginInfo loginInfo, @RequestBody PageQuery<WarehouseListQueryDTO> arg0){
        arg0.getQueryDTO().setUserId(loginInfo.getMemberId());
        arg0.getQueryDTO().setUserType(UserRoleEnum.SELLER.getCode());
        return new ItemResult<>(warehouseService.queryWarehouseList(arg0));
    }

    @Operation(summary = "查询我的仓库")
    @GetMapping(value="/queryMyWarehouse")
    public ItemResult<WarehouseDetailsDTO> queryMyWarehouse(LoginInfo loginInfo) {
        List<AccountStoreRelationDTO> list = iAccountStoreRelationService.findByAccountId(loginInfo.getAccountId());
        ItemResult<WarehouseDetailsDTO> itemResult = new ItemResult<>(null);
        if (CollectionUtils.isNotEmpty(list)) {
            WarehouseDetailsDTO warehouseDetailsDTO = warehouseService.queryWarehouseDetails(list.get(0).getStoreId());
            itemResult.setData(warehouseDetailsDTO);
        }
        return itemResult;
    }
    
    @Operation(summary = "查询仓库下拉列表")
    @PostMapping(value="/queryWarehouseIdAndName")
    public List<WarehouseOptionDTO> queryWarehouseIdAndName(@RequestBody WarehouseOptionQueryDTO warehouseOptionQueryDTO){
       return warehouseService.queryWarehouseIdAndName(warehouseOptionQueryDTO);
    }
    
    @Operation(summary = "查询仓库下拉列表(封装)")
    @PostMapping(value="/getWarehouseIdAndName")
    public ItemResult<List<WarehouseOptionDTO>> getWarehouseIdAndName(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody WarehouseOptionQueryDTO warehouseOptionQueryDTO){
        warehouseOptionQueryDTO.setUserId(loginInfo.getMemberId());
        return warehouseService.getWarehouseIdAndName(warehouseOptionQueryDTO);
    }
    
    @Operation(summary = "查询仓库库位")
    @PostMapping(value="/queryWarehouseAllocationListByWarehouseId")
    public ItemResult<List<WarehouseAllocationDTO>> queryWarehouseAllocationListByWarehouseId(@RequestBody WarehouseAllocationDTO dto) {
       return warehouseService.queryWarehouseAllocationListByWarehouseId(dto);
    }
    
    @Operation(summary = "查询仓库下拉列表(封装)")
    @PostMapping(value="/getWarehouseListBymemberId")
    public ItemResult<List<WarehouseSellerDTO>> getWarehouseListBymemberId(@Parameter(hidden = true) LoginInfo loginInfo,@RequestBody WarehouseSellerQueryDTO sellerQueryDTO){
    	log.info("getWarehouseListBymemberId->{}",sellerQueryDTO);
    	sellerQueryDTO.setMemberId(loginInfo.getMemberId());
        return warehouseService.getWarehouseListBymemberId(sellerQueryDTO);
    } 
}
