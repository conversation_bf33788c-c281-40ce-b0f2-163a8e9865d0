package com.ecommerce.web.seller.controller;


import com.ecommerce.common.exception.BusinessOperationException;import com.ecommerce.base.api.dto.warehouse.WarehouseUserRelationAddDTO;
import com.ecommerce.base.api.dto.warehouse.WarehouseUserRelationDTO;
import com.ecommerce.base.api.dto.warehouse.WarehouseUserRelationUpdateDTO;
import com.ecommerce.base.api.service.IWarehouseUserRelationService;
import com.ecommerce.common.result.ItemResult;
import com.ecommerce.member.api.session.LoginInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * @Created锛�Thu Aug 22 09:33:26 CST 2019
 * <AUTHOR>
 * @Version:2
 * @Description:中心仓--卖家映射
*/

@RestController
@Tag(name = "WarehouseUserRelation", description = "中心仓--卖家映射")
@RequestMapping("/warehouseUserRelation")
public class WarehouseUserRelationController {

   @Autowired 
   private IWarehouseUserRelationService iWarehouseUserRelationService;

   @Operation(summary = "删除中心仓-卖家关系映射")
   @PostMapping(value="/deleteWarehouseUserRelation")
   public ItemResult<Object> deleteWarehouseUserRelation(@Parameter(hidden = true) LoginInfo loginInfo, @Parameter(name = "warehouseId", description = "仓库Id") @RequestParam String warehouseId){
      return new ItemResult<>(iWarehouseUserRelationService.deleteWarehouseUserRelation(loginInfo.getMemberId(), warehouseId));
   }


   @Operation(summary = "根据用户ID,查询使用的中心仓/平台门店")
   @PostMapping(value="/queryWarehouseIdFromRelation")
   public ItemResult<List<String>> queryWarehouseIdFromRelation(@Parameter(hidden = true) LoginInfo loginInfo){
      return new ItemResult<>(iWarehouseUserRelationService.queryWarehouseIdFromRelation(loginInfo.getMemberId()));
   }


   @Operation(summary = "创建中心仓-卖家映射")
   @PostMapping(value="/createWarehouseUserRelation")
   public ItemResult<Object> createWarehouseUserRelation(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody List<WarehouseUserRelationAddDTO> addDTOs){
      if(CollectionUtils.isNotEmpty(addDTOs)){
         addDTOs.forEach(item->{
            item.setUserName(loginInfo.getMemberName());
            item.setUserId(loginInfo.getMemberId());
         });
      }
      return new ItemResult<>(iWarehouseUserRelationService.createWarehouseUserRelation(addDTOs));
   }


   @Operation(summary = "修改中心仓-卖家映射")
   @PostMapping(value="/updateWarehouseUserRelation")
   public ItemResult<Object> updateWarehouseUserRelation(@Parameter(hidden = true) LoginInfo loginInfo, @RequestBody WarehouseUserRelationUpdateDTO updateDTO){
      updateDTO.setUserId(loginInfo.getMemberId());
      updateDTO.setUserName(loginInfo.getMemberName());
      return new ItemResult<>(iWarehouseUserRelationService.updateWarehouseUserRelation(updateDTO));
   }



}
